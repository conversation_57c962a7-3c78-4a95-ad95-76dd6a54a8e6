<?php
declare(strict_types=1);

return [
    // 测试文件管理配置
    'test_file' => [
        // 源目录（待处理的测试文件）
        'source_dir' => env('TEST_FILE_SOURCE_DIR', '/home/<USER>'),
        
        // 目标目录（重排后的文件存储位置）
        'target_dir' => env('TEST_FILE_TARGET_DIR', '/home/<USER>'),
        
        // 文件扫描配置
        'scan' => [
            // 最大扫描深度
            'max_depth' => env('TEST_FILE_SCAN_MAX_DEPTH', 10),
            // 忽略的文件类型
            'ignore_extensions' => ['.tmp', '.log', '.cache'],
            // 忽略的目录
            'ignore_dirs' => ['.git', '.svn', 'node_modules'],
        ],
        
        // 任务配置
        'task' => [
            // 单次处理最大文件数
            'batch_size' => env('TEST_FILE_BATCH_SIZE', 100),
            // 任务超时时间（秒）
            'timeout' => env('TEST_FILE_TASK_TIMEOUT', 3600),
            // 是否保留源文件
            'keep_source' => env('TEST_FILE_KEEP_SOURCE', true),
        ],
        
        // 文件解析配置
        'parse' => [
            // 是否自动解析测试结果
            'auto_parse' => env('TEST_FILE_AUTO_PARSE', true),
            // 支持的测试结果文件
            'result_files' => [
                'agingTest_result.txt',
                'factoryTest_result.txt',
                'cpuid.txt',
                'test_summary.json',
            ],
        ],
    ],
];
