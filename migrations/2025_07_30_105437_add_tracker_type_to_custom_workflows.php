<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class AddTrackerTypeToCustomWorkflows extends Migration
{
    protected $connection = 'tchip_redmine';

    public function up(): void
    {
        if (Schema::hasTable('custom_workflows')) {
            Schema::table('custom_workflows', function (Blueprint $table) {
                if (!Schema::hasColumn('custom_workflows', 'tracker_type')) {
                    $table->string('tracker_type', 20)->nullable()
                        ->comment('事项大类：requirement/bug/task ……')
                        ->after('project_id');

                }
            });

            // if (Schema::hasColumn('custom_workflows', 'tracker_type')) {
            //     // 添加索引
            //     Schema::table('custom_workflows', function (Blueprint $table) {
            //         $table->index(['project_id', 'tracker_type'], 'idx_project_tracker_type');
            //     });
            // }
        }
    }

    public function down(): void
    {
        Schema::table('custom_workflows', function (Blueprint $table) {
            $table->dropIndex('idx_project_tracker_type');

            if (Schema::hasColumn('custom_workflows', 'tracker_type')) {
                $table->dropColumn('tracker_type');
            }
        });
    }
}