<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateFmsRecycleBinTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fms_recycle_bin', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->enum('target_type', ['directory', 'file'])->comment('删除对象类型');
            $table->unsignedBigInteger('target_id')->comment('对应目录/文件ID');
            $table->unsignedBigInteger('deleted_by')->comment('删除者ID');
            $table->timestamp('deleted_at')->comment('删除时间');

            // 索引
            $table->index(['target_type', 'target_id'], 'idx_fms_recycle_bin_target');
            $table->index(['deleted_by'], 'idx_fms_recycle_bin_deleted_by');
            $table->index(['deleted_at'], 'idx_fms_recycle_bin_deleted_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fms_recycle_bin');
    }
}