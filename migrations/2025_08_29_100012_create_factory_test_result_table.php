<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateFactoryTestResultTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('file_factory_test_result', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('file_sync_id')->comment('关联文件表ID');
            $table->string('product', 64)->comment('产品名称');
            $table->string('sn', 64)->comment('序列号');
            $table->string('test_datetime', 32)->comment('测试日期时间');
            
            // 厂测特有字段 - 仅保留9个必要字段
            $table->string('device_name', 128)->nullable()->comment('设备名');
            $table->string('cpuid', 64)->nullable()->comment('CPUID');
            $table->string('factory_test_version', 64)->nullable()->comment('厂测版本');
            $table->string('firmware_version', 255)->nullable()->comment('固件版本');
            $table->string('ddr', 32)->nullable()->comment('DDR');
            $table->string('flash', 32)->nullable()->comment('Flash');
            $table->text('success_projects')->nullable()->comment('成功项目');
            $table->text('failed_projects')->nullable()->comment('失败项目');
            $table->string('factory_test_result', 20)->nullable()->comment('厂测结果');
            
            $table->dateTime('parsed_at')->nullable()->comment('解析时间');
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            
            // 索引
            $table->unique('file_sync_id', 'uk_file_sync_id');
            $table->index(['product', 'sn'], 'idx_product_sn');
            $table->index('test_datetime', 'idx_test_datetime');
            $table->index('factory_test_result', 'idx_factory_test_result');
            $table->index('device_name', 'idx_device_name');
            $table->index('firmware_version', 'idx_firmware_version');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('file_factory_test_result');
    }
}
