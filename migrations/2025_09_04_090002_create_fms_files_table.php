<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateFmsFilesTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fms_files', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->unsignedBigInteger('directory_id')->comment('所属目录ID');
            $table->string('name', 255)->comment('文件名');
            $table->string('path', 500)->comment('文件存储路径（Flysystem适配器）');
            $table->unsignedBigInteger('size')->comment('文件大小（字节）');
            $table->string('mime_type', 100)->comment('文件类型（MIME）');
            $table->integer('version')->default(1)->comment('版本号（覆盖上传时递增）');
            $table->enum('visibility', ['public', 'department', 'user', 'private'])->default('public')->comment('可见性范围');
            $table->integer('sort_order')->default(0)->comment('文件排序（拖拽排序用）');
            $table->tinyInteger('is_deleted')->default(0)->comment('是否删除（回收站标记）');
            $table->unsignedBigInteger('created_by')->comment('上传者ID');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('最后修改者ID');
            $table->timestamps();

            // 索引
            $table->index(['directory_id'], 'idx_fms_files_directory_id');
            $table->index(['name'], 'idx_fms_files_name');
            $table->index(['mime_type'], 'idx_fms_files_mime_type');
            $table->index(['visibility'], 'idx_fms_files_visibility');
            $table->index(['is_deleted'], 'idx_fms_files_is_deleted');
            $table->index(['created_by'], 'idx_fms_files_created_by');
            $table->index(['sort_order'], 'idx_fms_files_sort_order');
            $table->index(['version'], 'idx_fms_files_version');

            // 外键约束
            $table->foreign('directory_id')->references('id')->on('fms_directories')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fms_files');
    }
}
