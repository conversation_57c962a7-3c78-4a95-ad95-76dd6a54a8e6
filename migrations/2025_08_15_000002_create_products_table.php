<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProductsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('file_products', function (Blueprint $table) {
            $table->increments('id');
            $table->string('product_name', 64)->unique()->comment('产品名称');
            $table->string('product_code', 32)->nullable()->unique()->comment('产品代码');
            $table->text('description')->nullable()->comment('产品描述');
            $table->tinyInteger('status')->default(1)->comment('状态: 1-启用, 0-禁用');
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('file_products');
    }
}
