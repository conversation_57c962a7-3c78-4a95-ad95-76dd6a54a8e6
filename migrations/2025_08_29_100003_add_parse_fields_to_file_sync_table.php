<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class AddParseFieldsToFileSyncTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('file_sync')) {
            Schema::table('file_sync', function (Blueprint $table) {
                // 检查字段是否存在，避免重复添加
                if (!Schema::hasColumn('file_sync', 'file_type')) {
                    $table->string('file_type', 32)->nullable()
                        ->comment('文件类型(aging_test/factory_test/other)')
                        ->after('test_type');
                }
                
                if (!Schema::hasColumn('file_sync', 'is_parsed')) {
                    $table->tinyInteger('is_parsed')->default(0)
                        ->comment('是否已解析(0-未解析,1-已解析,2-解析失败)')
                        ->after('sync_status');
                }
                
                if (!Schema::hasColumn('file_sync', 'parsed_at')) {
                    $table->dateTime('parsed_at')->nullable()
                        ->comment('解析时间')
                        ->after('is_parsed');
                }
                
                // 添加索引
                $table->index('file_type', 'idx_file_type');
                $table->index('is_parsed', 'idx_is_parsed');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('file_sync')) {
            Schema::table('file_sync', function (Blueprint $table) {
                // 删除索引
                $table->dropIndex('idx_file_type');
                $table->dropIndex('idx_is_parsed');
                
                // 删除字段
                if (Schema::hasColumn('file_sync', 'file_type')) {
                    $table->dropColumn('file_type');
                }
                if (Schema::hasColumn('file_sync', 'is_parsed')) {
                    $table->dropColumn('is_parsed');
                }
                if (Schema::hasColumn('file_sync', 'parsed_at')) {
                    $table->dropColumn('parsed_at');
                }
            });
        }
    }
}
