<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class AddOaReturnOrderExpressTableRecognitionMode extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('oa_return_order_express', function (Blueprint $table) {
            //
        });
        if (Schema::hasTable('oa_return_order_express')) {
            Schema::table('oa_return_order_express', function (Blueprint $table) {
                if (!Schema::hasColumn('oa_return_order_express', 'recognition_mode')) {
                    $table->tinyInteger('recognition_mode')->default(1)->comment('识别模式1.OCR自动识别2.pda识别');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('oa_return_order_express', function (Blueprint $table) {
            //
        });
    }
}
