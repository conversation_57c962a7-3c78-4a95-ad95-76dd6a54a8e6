<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class AddTrackerTypeToWorkflowTemplates extends Migration
{
    protected $connection = 'tchip_redmine';

    public function up(): void
    {
        if (Schema::hasTable('workflow_templates')) {
            Schema::table('workflow_templates', function (Blueprint $table) {
                if (!Schema::hasColumn('workflow_templates', 'tracker_type')) {
                    $table->string('tracker_type', 20)->nullable()
                          ->comment('适用的事项大类：requirement/bug/task')
                          ->after('project_id');
                }
            });

            // 添加索引
            Schema::table('workflow_templates', function (Blueprint $table) {
                $table->index(['project_id', 'tracker_type'], 'idx_wt_project_tracker_type');
            });
        }
    }

    public function down(): void
    {
        Schema::table('workflow_templates', function (Blueprint $table) {

            $table->dropIndex('idx_wt_project_tracker_type');
            
            if (Schema::hasColumn('workflow_templates', 'tracker_type')) {
                $table->dropColumn('tracker_type');
            }
        });
    }
}