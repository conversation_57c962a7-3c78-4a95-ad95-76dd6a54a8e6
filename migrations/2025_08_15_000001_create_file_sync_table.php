<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateFileSyncTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('file_sync', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('product', 64)->index()->comment('产品名称');
            $table->string('sn', 64)->index()->comment('序列号');
            $table->string('date_folder', 32)->index()->comment('日期文件夹');
            $table->string('test_datetime', 32)->comment('测试日期时间');
            $table->string('test_type', 32)->nullable()->comment('测试类型');
            $table->string('filename', 255)->comment('文件名');
            $table->bigInteger('file_size')->nullable()->comment('文件大小(字节)');
            $table->dateTime('file_mtime')->nullable()->comment('文件修改时间');
            $table->string('src_path', 512)->nullable()->comment('源文件路径');
            $table->string('dst_path', 512)->nullable()->comment('目标文件路径');
            $table->char('file_md5', 32)->nullable()->comment('文件MD5值');
            $table->tinyInteger('sync_status')->default(1)->comment('同步状态: 1-已同步, 2-已重排, 3-已删除');
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            
            // 复合索引
            $table->index(['product', 'sn', 'date_folder']);
            $table->index('sync_status');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('file_sync');
    }
}
