<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateFmsAclConditionsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fms_acl_conditions', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->unsignedBigInteger('rule_id')->comment('关联fms_acl_rules.id');
            $table->enum('condition_type', ['tag', 'time', 'ip'])->comment('条件类型');
            $table->string('condition_value', 255)->comment('条件值（如标签=confidential，时间=2025-01-01~2025-12-31，IP=***********/24）');

            // 索引
            $table->index(['rule_id'], 'idx_fms_acl_conditions_rule_id');
            $table->index(['condition_type'], 'idx_fms_acl_conditions_type');

            // 外键约束
            $table->foreign('rule_id')->references('id')->on('fms_acl_rules')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fms_acl_conditions');
    }
}