<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateFmsFileSharesTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fms_file_shares', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->unsignedBigInteger('file_id')->comment('被分享的文件ID');
            $table->string('share_token', 64)->unique()->comment('外链唯一标识（随机字符串/UUID）');
            $table->unsignedBigInteger('created_by')->comment('分享发起人（用户ID）');
            $table->tinyInteger('is_public')->default(1)->comment('是否公开（1=无需密码，0=需密码）');
            $table->timestamp('expire_at')->nullable()->comment('过期时间（NULL=永不过期）');
            $table->string('password_hash', 255)->nullable()->comment('外链访问密码（hash存储）');
            $table->integer('visits')->default(0)->comment('当前访问次数');
            $table->timestamps();

            // 索引
            $table->unique(['share_token'], 'uk_fms_file_shares_token');
            $table->index(['file_id'], 'idx_fms_file_shares_file_id');
            $table->index(['created_by'], 'idx_fms_file_shares_created_by');
            $table->index(['expire_at'], 'idx_fms_file_shares_expire_at');
            $table->index(['is_public'], 'idx_fms_file_shares_is_public');

            // 外键约束
            $table->foreign('file_id')->references('id')->on('fms_files')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fms_file_shares');
    }
}