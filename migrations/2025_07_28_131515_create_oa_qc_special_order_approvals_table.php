<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateOaQcSpecialOrderApprovalsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('oa_qc_special_order_approvals', function (Blueprint $table) {
            $table->bigIncrements('id');

            // 关联信息
            $table->unsignedBigInteger('order_id')->comment('特采订单ID');
            $table->string('order_no', 50)->comment('特采订单号');

            // 审批步骤信息
            $table->unsignedTinyInteger('step')->comment('审批步骤：1-采购，2-品质经理，3-PMC，4-副总经理');
            $table->string('step_name', 50)->comment('审批步骤名称');
            $table->unsignedTinyInteger('round')->default(1)->comment('审批轮次：1-首次提交，2-第一次重新提交，以此类推');

            // 审批人信息
            $table->unsignedInteger('approver_id')->comment('审批人ID');
            $table->string('approver_name', 50)->comment('审批人姓名');
            $table->string('approver_department', 50)->nullable()->comment('审批人部门');

            // 审批动作
            $table->string('action', 20)->comment('审批动作：approve-同意，reject-驳回');
            $table->text('comment')->nullable()->comment('审批意见/备注');

            // 驳回相关
            $table->unsignedTinyInteger('reject_to_step')->nullable()->comment('驳回到步骤：1-采购，2-品质经理，3-PMC，4-副总经理');
            $table->string('reject_reason', 255)->nullable()->comment('驳回原因');

            // 审批时间
            $table->timestamp('approved_at')->nullable()->comment('审批时间');
            $table->unsignedInteger('approval_duration')->nullable()->comment('审批耗时(分钟)');

            // 系统信息
            $table->string('ip_address', 45)->nullable()->comment('审批时IP地址');
            $table->string('user_agent', 255)->nullable()->comment('用户代理');

            $table->timestamps();

            // 索引
            $table->index('order_id');
            $table->index('order_no');
            $table->index('step');
            $table->index('approver_id');
            $table->index('action');
            $table->index('approved_at');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('oa_qc_special_order_approvals');
    }
}
