<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateReorganizeTasksTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('file_reorganize_tasks', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('task_type', 32)->comment('任务类型: manual/scheduled');
            $table->string('source_dir', 512)->nullable()->comment('源目录');
            $table->string('target_dir', 512)->nullable()->comment('目标目录');
            $table->tinyInteger('status')->default(0)->comment('状态: 0-待处理, 1-处理中, 2-完成, 3-失败');
            $table->integer('total_files')->default(0)->comment('总文件数');
            $table->integer('processed_files')->default(0)->comment('已处理文件数');
            $table->text('error_message')->nullable()->comment('错误信息');
            $table->dateTime('started_at')->nullable()->comment('开始时间');
            $table->dateTime('completed_at')->nullable()->comment('完成时间');
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            
            // 索引
            $table->index('status');
            $table->index('task_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('file_reorganize_tasks');
    }
}
