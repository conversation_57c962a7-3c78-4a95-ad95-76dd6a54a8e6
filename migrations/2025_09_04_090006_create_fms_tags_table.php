<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateFmsTagsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fms_tags', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->string('name', 50)->comment('标签名称');
            $table->unsignedBigInteger('created_by')->comment('创建者ID');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('最后修改者ID');
            $table->timestamp('created_at')->nullable()->comment('创建时间');

            // 索引
            $table->unique(['name'], 'uk_fms_tags_name');
            $table->index(['created_by'], 'idx_fms_tags_created_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fms_tags');
    }
}