<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class AddAssignedAtToTestPlanCaseTable extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('test_plan_case')) {
            Schema::table('test_plan_case', function (Blueprint $table) {
                if (!Schema::hasColumn('test_plan_case', 'assigned_at')) {
                    $table->dateTime('assigned_at')->nullable()->default(null)->comment('分配时间');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('test_plan_case')) {
            Schema::table('test_plan_case', function (Blueprint $table) {
                if (Schema::hasColumn('test_plan_case', 'assigned_at')) {
                    $table->dropColumn('assigned_at');
                }
            });
        }
    }
}
