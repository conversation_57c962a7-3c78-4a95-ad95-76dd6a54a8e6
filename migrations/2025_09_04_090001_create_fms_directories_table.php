<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateFmsDirectoriesTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fms_directories', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->unsignedBigInteger('parent_id')->nullable()->comment('父目录ID，NULL=根目录');
            $table->unsignedBigInteger('owner_id')->comment('拥有者ID（用户或部门）');
            $table->enum('owner_type', ['user', 'dept'])->comment('拥有者类型');
            $table->string('name', 255)->comment('目录名称');
            $table->string('path', 500)->comment('目录路径 /1/2/3/');
            $table->enum('visibility', ['public', 'department', 'user', 'private'])->default('public')->comment('可见性范围');
            $table->integer('sort_order')->default(0)->comment('目录排序（拖拽排序用）');
            $table->tinyInteger('is_deleted')->default(0)->comment('是否删除（回收站标记）');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('最后修改者ID');
            $table->timestamps();

            // 索引
            $table->index(['parent_id'], 'idx_fms_directories_parent_id');
            $table->index(['owner_id', 'owner_type'], 'idx_fms_directories_owner');
            $table->index(['visibility'], 'idx_fms_directories_visibility');
            $table->index(['is_deleted'], 'idx_fms_directories_is_deleted');
            $table->index(['sort_order'], 'idx_fms_directories_sort_order');
            $table->index(['path'], 'idx_fms_directories_path');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fms_directories');
    }
}
