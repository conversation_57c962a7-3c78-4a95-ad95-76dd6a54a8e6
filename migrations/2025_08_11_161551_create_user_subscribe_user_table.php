<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateUserSubscribeUserTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_subscribe_user', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('user_id')->index('user_id')->comment('用户ID');
            $table->integer('subscribed_user_id')->index('subscribed_user_id')->comment('被订阅用户ID');
            $table->string('type', 64)->index('type')->comment('订阅动作subscribe=关注');
            $table->string('module', 64)->index('module')->comment('订阅模块week_report=周报');
            $table->dateTime('created_at');
            $table->dateTime('updated_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_subscribe_user');
    }
}
