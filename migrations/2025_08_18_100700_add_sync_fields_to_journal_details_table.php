<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class AddSyncFieldsToJournalDetailsTable extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';

    public function up(): void
    {
        if (Schema::hasTable('journal_details')) {
            Schema::table('journal_details', function (Blueprint $table) {
                if (!Schema::hasColumn('journal_details', 'sync_type')) {
                    $table->string('sync_type', 50)->nullable()->after('value')
                        ->comment('来源：manual=人工，sync=同步，system=系统自动');
                    $table->index('sync_type', 'idx_sync_type');
                }

                if (!Schema::hasColumn('journal_details', 'relation_id')) {
                    $table->bigInteger('relation_id')->nullable()->after('sync_type')
                        ->comment('如果来源于同步，关联 issue_sync_relations.id');
                    $table->index('relation_id', 'idx_relation_id');
                }
            });
        }
    }

    public function down(): void
    {
        if (Schema::hasTable('journal_details')) {
            Schema::table('journal_details', function (Blueprint $table) {
                if (Schema::hasColumn('journal_details', 'sync_type')) {
                    $table->dropIndex('idx_sync_type');
                    $table->dropColumn('sync_type');
                }

                if (Schema::hasColumn('journal_details', 'relation_id')) {
                    $table->dropIndex('idx_relation_id');
                    $table->dropColumn('relation_id');
                }
            });
        }
    }
}
