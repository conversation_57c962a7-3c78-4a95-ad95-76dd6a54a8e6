<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateIssueCopyRelationsTable extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';

    /**
     * 同步关系链表
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('issue_sync_relations')) {
            Schema::create('issue_sync_relations', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->bigInteger('source_issue_id')->comment('源问题ID');
                $table->bigInteger('target_issue_id')->comment('目标问题ID');
                $table->string('sync_direction', 50)->default('bidirectional')->comment('同步方向: source_to_target|target_to_source|bidirectional');
                $table->tinyInteger('is_active')->default(1)->comment('是否激活同步关系');
                $table->string('created_by', 100)->nullable()->comment('创建人');
                $table->string('updated_by', 100)->nullable()->comment('更新人');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->dateTime('deleted_at')->nullable();
                
                // 创建唯一索引
                $table->unique(['source_issue_id', 'target_issue_id'], 'idx_source_target_unique');
                // 创建普通索引
                $table->index('target_issue_id', 'idx_target_issue_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('issue_sync_relations', function (Blueprint $table) {
            Schema::dropIfExists('issue_sync_relations');
        });
    }
}
