<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class AddAssembleOrderTableFactory extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('assemble_order')) {
            Schema::table('assemble_order', function (Blueprint $table) {
                if (!Schema::hasColumn('assemble_order', 'factory_name')) {
                    $table->string('factory_name')->default('')->comment('工厂名称');
                }
                if (!Schema::hasColumn('assemble_order', 'factory_code')) {
                    $table->string('factory_code')->default('')->comment('工厂编码');
                }
                if (!Schema::hasColumn('assemble_order', 'factory_id')) {
                    $table->integer('factory_id')->default(0)->comment('工厂id');
                }
            });
        }
        if (Schema::hasTable('production_factory')) {
            Schema::table('production_factory', function (Blueprint $factoryTable) {
                if (Schema::hasColumn('production_factory', 'status')) {
                    $factoryTable->smallInteger('status')->default(1)->comment('状态：1-启用，2-禁用')->change();
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assemble_order', function (Blueprint $table) {
            //
        });
    }
}
