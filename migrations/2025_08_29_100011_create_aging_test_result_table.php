<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateAgingTestResultTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('file_aging_test_result', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('file_sync_id')->comment('关联文件表ID');
            $table->string('product', 64)->comment('产品名称');
            $table->string('sn', 64)->comment('序列号');
            $table->string('test_datetime', 32)->comment('测试日期时间');
            
            // 老化测试特有字段
            $table->string('runtime', 20)->nullable()->comment('运行时间格式化字符串');
            $table->integer('runtime_seconds')->nullable()->comment('运行时间总秒数')->index();
            
            $table->dateTime('parsed_at')->nullable()->comment('解析时间');
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            
            // 索引
            $table->unique('file_sync_id', 'uk_file_sync_id');
            $table->index(['product', 'sn'], 'idx_product_sn');
            $table->index('test_datetime', 'idx_test_datetime');
            $table->index('runtime', 'idx_runtime');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('file_aging_test_result');
    }
}
