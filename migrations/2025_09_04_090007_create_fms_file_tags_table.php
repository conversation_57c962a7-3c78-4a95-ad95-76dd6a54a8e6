<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateFmsFileTagsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fms_file_tags', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->unsignedBigInteger('file_id')->comment('文件ID');
            $table->unsignedBigInteger('tag_id')->comment('标签ID');
            $table->unsignedBigInteger('created_by')->comment('创建者ID');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('最后修改者ID');
            $table->timestamp('created_at')->nullable()->comment('创建时间');

            // 索引
            $table->unique(['file_id', 'tag_id'], 'uk_fms_file_tags_file_tag');
            $table->index(['file_id'], 'idx_fms_file_tags_file_id');
            $table->index(['tag_id'], 'idx_fms_file_tags_tag_id');

            // 外键约束
            $table->foreign('file_id')->references('id')->on('fms_files')->onDelete('cascade');
            $table->foreign('tag_id')->references('id')->on('fms_tags')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fms_file_tags');
    }
}