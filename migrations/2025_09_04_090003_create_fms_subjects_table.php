<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateFmsSubjectsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fms_subjects', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->enum('subject_type', ['user', 'dept', 'role'])->comment('主体类型');
            $table->unsignedBigInteger('subject_id')->comment('对应用户/部门/角色ID');

            // 索引
            $table->unique(['subject_type', 'subject_id'], 'uk_fms_subjects_type_id');
            $table->index(['subject_type'], 'idx_fms_subjects_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fms_subjects');
    }
}