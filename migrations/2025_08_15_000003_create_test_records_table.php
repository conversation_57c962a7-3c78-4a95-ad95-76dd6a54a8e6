<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateTestRecordsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('file_test_records', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedInteger('product_id')->comment('产品ID');
            $table->string('sn', 64)->index()->comment('序列号');
            $table->string('cpuid', 64)->nullable()->index()->comment('CPU ID');
            $table->date('test_date')->index()->comment('测试日期');
            $table->string('test_datetime', 32)->comment('测试日期时间');
            $table->json('test_types')->nullable()->comment('测试类型列表');
            $table->integer('file_count')->default(0)->comment('文件数量');
            $table->bigInteger('total_size')->default(0)->comment('总文件大小');
            $table->string('factory_test_result', 32)->nullable()->comment('厂测结果');
            $table->string('aging_test_result', 32)->nullable()->comment('老化测试结果');
            $table->string('firmware_version', 255)->nullable()->comment('固件版本');
            $table->string('factory_test_version', 64)->nullable()->comment('厂测版本');
            $table->string('ddr_size', 32)->nullable()->comment('DDR大小');
            $table->string('flash_size', 32)->nullable()->comment('Flash大小');
            $table->text('remarks')->nullable()->comment('备注');
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            
            // 索引
            $table->index(['product_id', 'sn']);
            $table->unique(['product_id', 'sn', 'test_datetime']);
            
            // 外键
            $table->foreign('product_id')->references('id')->on('file_products');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('file_test_records');
    }
}
