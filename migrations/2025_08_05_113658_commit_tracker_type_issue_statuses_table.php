<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitTrackerTypeIssueStatusesTable extends Migration
{
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('issue_statuses')) {
            Schema::table('issue_statuses', function (Blueprint $table) {
                if (!Schema::hasColumn('issue_statuses', 'tracker_type')) {
                    $table->string('tracker_type', 64)->nullable()
                        ->comment('状态类型');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('issue_statuses')) {
            Schema::table('issue_statuses', function (Blueprint $table) {
                if (Schema::hasColumn('issue_statuses', 'tracker_type')) {
                    $table->dropColumn('tracker_type');
                }
            });
        }
    }
}
