<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateFmsOperationLogsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fms_operation_logs', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->unsignedBigInteger('user_id')->comment('操作者ID');
            $table->enum('action', ['upload', 'download', 'delete', 'restore', 'create_dir', 'rename'])->comment('操作类型');
            $table->enum('target_type', ['directory', 'file'])->comment('操作目标类型');
            $table->unsignedBigInteger('target_id')->comment('操作对象ID');
            $table->text('detail')->nullable()->comment('操作详情（JSON，可选）');
            $table->timestamp('created_at')->comment('操作时间');

            // 索引
            $table->index(['user_id'], 'idx_fms_operation_logs_user_id');
            $table->index(['action'], 'idx_fms_operation_logs_action');
            $table->index(['target_type', 'target_id'], 'idx_fms_operation_logs_target');
            $table->index(['created_at'], 'idx_fms_operation_logs_created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fms_operation_logs');
    }
}