<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateRedmineStatusMappingTable extends Migration
{
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('status_mapping')) {
            Schema::create('status_mapping', function (Blueprint $table) {
                $table->integer('old_status_id');
                $table->string('tracker_type', 64);
                $table->integer('new_status_id');
                // 定义复合主键（多个列组成一个主键）
                $table->primary(['old_status_id', 'tracker_type']);
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('status_mapping');
    }
}
