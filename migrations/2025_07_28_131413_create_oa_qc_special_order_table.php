<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateOaQcSpecialOrderTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('oa_qc_special_order', function (Blueprint $table) {
            $table->bigIncrements('id');

            // 基础订单信息
            $table->string('order_no', 50)->unique()->comment('特采订单号');
            $table->unsignedBigInteger('qc_order_id')->nullable()->comment('关联的QC订单ID');

            // 特采申请内容 - 冗余字段已通过qc_order_id关联获取
            $table->text('special_reason')->nullable()->comment('特采理由');
            // $table->text('improvement_measures')->nullable()->comment('改善对策');
            // $table->date('expected_completion_date')->nullable()->comment('预计改善完成日期');
            $table->text('special_details')->nullable()->comment('特采详情');
            $table->text('special_scope')->nullable()->comment('特采范围');

            // 审批流程相关
            $table->string('status', 20)->default('pending')->comment('状态：pending-待审批，step1-采购审批中，step2-品质经理审批中，step3-PMC审批中，step4-副总经理审批中，approved-已通过，rejected-已驳回，returned-已退回，cancelled-已取消');
            $table->unsignedTinyInteger('current_step')->default(0)->comment('当前审批步骤：0-待提交，1-采购，2-品质经理，3-PMC，4-副总经理，5-已完成');
            $table->unsignedInteger('current_approver_id')->nullable()->comment('当前审批人ID');
            $table->string('current_approver_name', 50)->nullable()->comment('当前审批人姓名');

            // 结果信息
            $table->text('final_remark')->nullable()->comment('最终备注');

            // 创建人信息
            $table->unsignedInteger('creator_id')->comment('创建人ID');
            $table->string('creator_name', 50)->nullable()->comment('创建人姓名');

            $table->timestamps();

            // 索引
            $table->index('order_no');
            $table->index('qc_order_id');
            $table->index('status');
            $table->index('current_step');
            $table->index('current_approver_id');
            $table->index('creator_id');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('oa_qc_special_order');
    }
}
