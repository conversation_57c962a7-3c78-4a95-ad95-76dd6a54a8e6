<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitRedmineColorTrackers extends Migration
{
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('trackers')) {
            Schema::table('trackers', function (Blueprint $table) {
                if (!Schema::hasColumn('trackers', 'color')) {
                    $table->string('color', 16)->default('#409EFF')
                        ->comment('事项类型颜色,十六进制');
                }
                if (!Schema::hasColumn('trackers', 'identifier')) {
                    $table->string('identifier', 16)->nullable()
                        ->comment('事项类型识别符');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('trackers', function (Blueprint $table) {
            if (Schema::hasColumn('trackers', 'color')) {
                $table->dropColumn('color');
            }
            if (Schema::hasColumn('trackers', 'identifier')) {
                $table->dropColumn('identifier');
            }
        });
    }
}
