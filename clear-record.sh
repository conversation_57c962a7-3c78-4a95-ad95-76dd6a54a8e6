#!/bin/bash

# 获取当前日期和时间
current_datetime=$(date +"%Y-%m-%d %H:%M:%S")

# 获取当前月份的第一天
current_date=$(date +%Y-%m-01)
# 计算上一个月的日期
last_month=$(date -d "$current_date - 1 month" +%Y-%m)

# 指定日志目录的路径
log_directory="./runtime/logs"
# 指定日志文件的路径
log_file="${log_directory}/cleanup_log.txt"

# 构造需要删除的文件夹路径
folder_to_delete="${log_directory}/${last_month}"

# 确保日志目录存在
mkdir -p "$log_directory"

# 检查日志文件是否存在，如果不存在则创建
if [ ! -f "$log_file" ]; then
    touch "$log_file"
fi

# 定义一个函数，用于将带有时间戳的消息写入日志文件
log_message() {
    echo "$current_datetime $1" >> "$log_file"
}

# 检查文件夹是否存在
if [ -d "$folder_to_delete" ]; then
    log_message "正在删除上一个月的日志文件夹: $folder_to_delete"
    # 删除文件夹及其内容
    rm -rf "$folder_to_delete"
    log_message "删除完成。"
else
    log_message "文件夹 $folder_to_delete 不存在，无需删除。"
fi

# 输出日志内容到控制台（可选）
cat "$log_file"