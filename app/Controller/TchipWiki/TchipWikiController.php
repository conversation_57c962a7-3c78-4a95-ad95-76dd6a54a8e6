<?php
    /**
     * @Copyright T-chip Team.
     * @Date 2025/04/02 17:02
     * <AUTHOR>
     * @Description
     */

    namespace App\Controller\TchipWiki;

    use App\Core\Services\TchipWiki\TchipWikiService;
use App\Core\Services\TchipWiki\DocumentStatusService;
    use App\Request\IdsRequest;
    use Hyperf\Di\Annotation\Inject;
    use App\Middleware\AuthMiddleware;
    use App\Middleware\MenuMiddleware;
    use App\Annotation\ControllerNameAnnotation;
    use Hyperf\HttpServer\Annotation\AutoController;
    use Hyperf\HttpServer\Annotation\Middleware;
    use Qbhy\HyperfAuth\AuthManager;

    /**
     * @ControllerNameAnnotation("Wiki库")
     * @AutoController(prefix="/tchip_wiki/index")
     */
    class TchipWikiController extends \App\Controller\BaseController
    {
        /**
         * @Inject()
         * @var AuthManager
         */
        protected $auth;
        /**
         * @Inject()
         * @var TchipWikiService
         */
        protected $service;

        /**
         * @Inject()
         * @var DocumentStatusService
         */
        protected $documentStatusService;

        //////////////////////////////////////////////
        /// Wiki目录
        /**
         * @ControllerNameAnnotation("Wiki目录-列表查询")
         */
        public function getWikiCatalogList(): \Psr\Http\Message\ResponseInterface
        {
            list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
            $sort = $this->request->input('sort', 'catalog_id');
            $page = $this->request->input('page', null);
            if (is_null($page)) {
                $page = $this->request->input('pageNo', 1); // 如果page没有，再取pageNo，默认1
            }
            $result = $this->service->getWikiCatalogList($filter, $op, $sort, $order, (int)$limit, $page ,$search, $searchFields);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("Wiki目录-新增/编辑")
         */
        public function doWikiCatalogEdit(): \Psr\Http\Message\ResponseInterface
        {
            $catalog_id = (int) $this->request->input('catalog_id', 0);
            $params = $this->request->all();
            unset($params['catalog_id']);
            $result = $this->service->doWikiCatalogEdit($catalog_id, $params);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("Wiki目录-删除")
         */
        public function doWikiCatalogDelete(): \Psr\Http\Message\ResponseInterface
        {
            $validated = make(IdsRequest::class)->validated();
            $result = $this->service->doWikiCatalogDelete($validated['ids']);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("Wiki目录-拖动重排")
         */
        public function wikiCatalogRearrangement(): \Psr\Http\Message\ResponseInterface
        {
            $dragNodeId = (int) $this->request->input('drag_node_id', 0);
            $targetNode = $this->request->input('target_node');
            $dropToGap = (int) $this->request->input('drop_to_gap', 0);
            $dropPosition = (int) $this->request->input('drop_position', 0);
            $result = $this->service->wikiCatalogRearrangement($dragNodeId, $targetNode, $dropToGap, $dropPosition);
            return $this->response->success($result);
        }
        /// Wiki目录 END
        //////////////////////////////////////////////

        //////////////////////////////////////////////
        /// Wiki文档
        /**
         * @ControllerNameAnnotation("Wiki文档-列表查询")
         */
        public function getList(): \Psr\Http\Message\ResponseInterface
        {
            list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
            $sort = $this->request->input('sort', 'doc_id');
            $result = $this->service->getList($filter, $op, $sort, $order, (int)$limit, $search, $searchFields);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("Wiki文档-增改")
         */
        public function doEdit(): \Psr\Http\Message\ResponseInterface
        {
            $id = (int) $this->request->input('doc_id', 0);
            $params = $this->request->all();
            unset($params['doc_id']);
            $result = $this->service->doEdit($id, $params);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("Wiki文档-批量增改")
         */
        public function doBatchEdit(): \Psr\Http\Message\ResponseInterface
        {
            $documents = $this->request->input('documents', []);
            $result = $this->service->doBatchEdit($documents);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("Wiki文档-删除")
         */
        public function doDelete(): \Psr\Http\Message\ResponseInterface
        {
            $validated = make(IdsRequest::class)->validated();
            $result = $this->service->doDelete($validated['ids']);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("Wiki文档-批量置顶")
         */
        public function doWikiDocumentBatchPin(): \Psr\Http\Message\ResponseInterface
        {
            $validated = make(IdsRequest::class)->validated();
            $pinned_reason = $this->request->input('pinned_reason', '');
            $result = $this->service->doWikiDocumentBatchPin($validated['ids'], $pinned_reason);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("Wiki文档-批量取消置顶")
         */
        public function doWikiDocumentBatchUnpin(): \Psr\Http\Message\ResponseInterface
        {
            $validated = make(IdsRequest::class)->validated();
            $result = $this->service->doWikiDocumentBatchUnpin($validated['ids']);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("Wiki文档-最近更新的Wiki文档列表获取")
         */
        public function getWikiRecentUpdate(): \Psr\Http\Message\ResponseInterface
        {
            list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
            $result = $this->service->getWikiRecentUpdate($filter, $op, $sort, $order, (int)$limit, $search, $searchFields);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("Wiki文档-全局搜索")
         */
        public function getWikiSearch(): \Psr\Http\Message\ResponseInterface
        {
            $keyword = $this->request->input('keywords', '');
            list($filter, $op, $sort, $order, $limit) = $this->getParams();
            $result = $this->service->getWikiSearch($keyword, $filter, $op, $sort, $order, $limit);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("Wiki文档-导出为PDF")
         */
        public function wikiExport(): \Psr\Http\Message\ResponseInterface
        {
            $doc_id = (int) $this->request->input('doc_id', 0);
            $result = $this->service->wikiExport($doc_id);
            
            // 检查是否为文件流类型的响应
            if (isset($result['file_content']) && isset($result['mime_type'])) {
                return $this->createFileStreamResponse($result);
            }
            
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("Wiki目录-导出目录下所有文档")
         */
        public function wikiExportCatalogRecursive(): \Psr\Http\Message\ResponseInterface
        {
            $catalog_id = (int) $this->request->input('catalog_id', 0);
            $includeStructure = (bool) $this->request->input('include_structure', true);
            
            $result = $this->service->wikiExportCatalogRecursive($catalog_id, $includeStructure);
            
            // 如果返回的是文件流，使用文件下载响应
            if (isset($result['file_content']) && isset($result['mime_type'])) {
                return $this->createFileStreamResponse($result);
            }
            
            return $this->response->success($result);
        }

        /**
         * 创建文件流响应
         *
         * @param array $fileData 包含文件内容、名称和MIME类型的数组
         * @return \Psr\Http\Message\ResponseInterface
         */
        private function createFileStreamResponse(array $fileData): \Psr\Http\Message\ResponseInterface
        {
            $fileContent = $fileData['file_content'];
            $fileName = $fileData['file_name'];
            $mimeType = $fileData['mime_type'];
            
            // 获取响应实例
            $response = make(\Hyperf\HttpServer\Contract\ResponseInterface::class);
            
            // 设置适当的响应头
            return $response->withHeader('Content-Type', $mimeType)
                ->withHeader('Content-Disposition', 'attachment; filename*=UTF-8\'\'' . rawurlencode($fileName))
                ->withHeader('Content-Transfer-Encoding', 'binary')
                ->withHeader('Cache-Control', 'no-cache, no-store, max-age=0, must-revalidate')
                ->withBody(new \Hyperf\HttpMessage\Stream\SwooleStream($fileContent));
        }

        /**
         * @ControllerNameAnnotation("Wiki文档-获取所有文档")
         */
        public function getAllWikiDocument(): \Psr\Http\Message\ResponseInterface
        {
            list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
            $result = $this->service->getAllWikiDocument($filter, $op, $sort, $order, (int)$limit, $search, $searchFields);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("Wiki文档-导入项目Wiki")
         */
        public function importProjectWiki(): \Psr\Http\Message\ResponseInterface
        {
            $ids = $this->request->input('ids', []);
            $catalog_id = $this->request->input('catalog_id', 0);
            $result = $this->service->importProjectWiki($ids, $catalog_id);
            return $this->response->success($result);
        }

        /// Wiki文档 END
        //////////////////////////////////////////////


        //////////////////////////////////////////////
        /// Wiki文档点赞
        /**
         * @ControllerNameAnnotation("Wiki文档点赞-列表查询")
         */
        public function getWikiLikeList(): \Psr\Http\Message\ResponseInterface
        {
            list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
            $result = $this->service->getWikiLikeList($filter, $op, $sort, $order, (int)$limit, $search, $searchFields);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("Wiki文档点赞-取消点赞")
         */
        public function doWikiLikeDelete(): \Psr\Http\Message\ResponseInterface
        {
            $validated = make(IdsRequest::class)->validated();
            $result = $this->service->doWikiLikeDelete($validated['ids']);
            return $this->response->success($result);
        }
        
        /**
         * @ControllerNameAnnotation("Wiki文档点赞-新增/编辑")
         */
        public function doWikiLikeEdit(): \Psr\Http\Message\ResponseInterface
        {
            $id = (int) $this->request->input('like_id', 0);
            $params = $this->request->all();
            unset($params['like_id']);
            $result = $this->service->doWikiLikeEdit($id, $params);
            return $this->response->success($result);
        }
        /// Wiki文档点赞 END
        //////////////////////////////////////////////


        //////////////////////////////////////////////
        /// Wiki文档评论
        /**
         * @ControllerNameAnnotation("Wiki文档评论-列表查询")
         */
        public function getWikiCommentList(): \Psr\Http\Message\ResponseInterface
        {
            list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
            $result = $this->service->getWikiCommentList($filter, $op, $sort, $order, (int)$limit, $search, $searchFields);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("Wiki文档评论-新增/编辑")
         */
        public function doWikiCommentEdit(): \Psr\Http\Message\ResponseInterface
        {
            $comment_id = (int) $this->request->input('comment_id', 0);
            $params = $this->request->all();
            unset($params['comment_id']);
            $result = $this->service->doWikiCommentEdit($comment_id, $params);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("Wiki文档评论-删除")
         */
        public function doWikiCommentDelete(): \Psr\Http\Message\ResponseInterface
        {
            $validated = make(IdsRequest::class)->validated();
            $result = $this->service->doWikiCommentDelete($validated['ids']);
            return $this->response->success($result);
        }

        
         /**
         * @ControllerNameAnnotation("Wiki文档评论-回复新增/编辑")
         */
        public function doWikiReplyEdit(): \Psr\Http\Message\ResponseInterface
        {
            $reply_id = (int) $this->request->input('reply_id', 0);
            $params = $this->request->all();
            unset($params['reply_id']);
            $result = $this->service->doWikiReplyEdit($reply_id, $params);
            return $this->response->success($result);
        }



        /**
         * @ControllerNameAnnotation("Wiki文档评论-回复删除")
         */
        public function doWikiReplyDelete(): \Psr\Http\Message\ResponseInterface
        {
            $validated = make(IdsRequest::class)->validated();
            $result = $this->service->doWikiReplyDelete($validated['ids']);
            return $this->response->success($result);
        }
        /// Wiki文档评论 END
        //////////////////////////////////////////////
        

        //////////////////////////////////////////////
        /// Wiki空间
        /**
         * @ControllerNameAnnotation("Wiki空间-列表查询")
         */
        public function getWikiSpaceList(): \Psr\Http\Message\ResponseInterface
        {
            list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
            $sort = $this->request->input('sort', 'space_id');
            $result = $this->service->getWikiSpaceList($filter, $op, $sort, $order, (int)$limit, $search, $searchFields);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("Wiki空间-增改")
         * @Middleware(AuthMiddleware::class)
         * @Middleware(MenuMiddleware::class)
         */
        public function doSpaceEdit(): \Psr\Http\Message\ResponseInterface
        {
            $space_id = (int) $this->request->input('space_id', 0);
            $params = $this->request->all();
            unset($params['space_id']);
            $result = $this->service->doSpaceEdit($space_id, $params);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("Wiki空间-删除")
         * @Middleware(AuthMiddleware::class)
         * @Middleware(MenuMiddleware::class)
         */
        public function doSpaceDelete(): \Psr\Http\Message\ResponseInterface
        {
            $validated = make(IdsRequest::class)->validated();
            $result = $this->service->doSpaceDelete($validated['ids']);
            return $this->response->success($result);
        }
        /// Wiki空间 END
        //////////////////////////////////////////////


        //////////////////////////////////////////////
        /// 人员群组
        /**
         * @ControllerNameAnnotation("人员群组-列表查询")
         */
        public function getUserGroupList(): \Psr\Http\Message\ResponseInterface
        {
            list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
            $sort = $this->request->input('sort', 'group_id');
            $result = $this->service->getUserGroupList($filter, $op, $sort, $order, (int)$limit, $search, $searchFields);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("人员群组-增改")
         * @Middleware(AuthMiddleware::class)
         * @Middleware(MenuMiddleware::class)
         */
        public function doUserGroupEdit(): \Psr\Http\Message\ResponseInterface
        {
            $group_id = (int) $this->request->input('group_id', 0);
            $params = $this->request->all();
            unset($params['group_id']);
            $result = $this->service->doUserGroupEdit($group_id, $params);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("人员群组-删除")
         * @Middleware(AuthMiddleware::class)
         * @Middleware(MenuMiddleware::class)
         */
        public function doUserGroupDelete(): \Psr\Http\Message\ResponseInterface
        {
            $validated = make(IdsRequest::class)->validated();
            $result = $this->service->doUserGroupDelete($validated['ids']);
            return $this->response->success($result);
        }
        /// 人员群组 END
        //////////////////////////////////////////////


        //////////////////////////////////////////////
        /// 成员管理
        /**
         * @ControllerNameAnnotation("Wiki目录成员管理-列表查询")
         */
        public function getCatalogMemberList(): \Psr\Http\Message\ResponseInterface
        {
            list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
            $sort = $this->request->input('sort', 'member_id');
            $result = $this->service->getCatalogMemberList($filter, $op, $sort, $order, (int)$limit, $search, $searchFields);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("Wiki目录成员管理-新增/编辑")
         */
        public function doCatalogMemberEdit(): \Psr\Http\Message\ResponseInterface
        {
            $member_id = (int) $this->request->input('member_id', 0);
            $params = $this->request->all();
            unset($params['member_id']);
            $result = $this->service->doCatalogMemberEdit($member_id, $params);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("Wiki目录成员管理-获取成员角色")
         */
        public function getCatalogMemberRole(): \Psr\Http\Message\ResponseInterface
        {
            $id = (int) $this->request->input('id', 0);
            $catalog_id = (int) $this->request->input('catalog_id', 0);
            $associated_field = $this->request->input('associated_field', 'user_id');
            $result = $this->service->getCatalogMemberRole($id, $catalog_id, $associated_field);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("Wiki目录成员管理-删除")
         */
        public function doCatalogMemberDelete(): \Psr\Http\Message\ResponseInterface
        {
            $validated = make(IdsRequest::class)->validated();
            $result = $this->service->doCatalogMemberDelete($validated['ids']);
            return $this->response->success($result);
        }


        /**
         * @ControllerNameAnnotation("Wiki目录成员管理-批量添加")
         */
        public function doCatalogMemberMultiAdd(): \Psr\Http\Message\ResponseInterface
        {
            $catalog_id = (int) $this->request->input('catalog_id', 0);
            $ids = $this->request->input('ids', []);
            $associated_field = $this->request->input('associated_field', 'user_id');
            $result = $this->service->doCatalogMemberMultiAdd($catalog_id, $ids, $associated_field);
            return $this->response->success($result);
        }


        /**
         * @ControllerNameAnnotation("Wiki空间成员管理-列表查询")
         */
        public function getSpaceMemberList(): \Psr\Http\Message\ResponseInterface
        {
            list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
            $sort = $this->request->input('sort', 'member_id');
            $result = $this->service->getSpaceMemberList($filter, $op, $sort, $order, (int)$limit, $search, $searchFields);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("Wiki空间成员管理-增改")
         */
        public function doSpaceMemberEdit(): \Psr\Http\Message\ResponseInterface
        {
            $member_id = (int) $this->request->input('member_id', 0);
            $params = $this->request->all();
            unset($params['member_id']);
            $result = $this->service->doSpaceMemberEdit($member_id, $params);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("Wiki空间成员管理-批量添加")
         */
        public function doSpaceMemberMultiAdd(): \Psr\Http\Message\ResponseInterface
        {
            $space_id = (int) $this->request->input('space_id', 0);
            $ids = $this->request->input('ids', []);
            $associated_field = $this->request->input('associated_field', 'user_id');
            $result = $this->service->doSpaceMemberMultiAdd($space_id, $ids, $associated_field);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("Wiki空间成员管理-删除")
         */
        public function doSpaceMemberDelete(): \Psr\Http\Message\ResponseInterface
        {
            $validated = make(IdsRequest::class)->validated();
            $result = $this->service->doSpaceMemberDelete($validated['ids']);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("Wiki空间成员管理-获取成员角色")
         */
        public function getSpaceMemberRole(): \Psr\Http\Message\ResponseInterface
        { 
            $id = (int) $this->request->input('id', 0);
            $space_id = (int) $this->request->input('space_id', 0);
            $associated_field = $this->request->input('associated_field', 'user_id');
            $result = $this->service->getSpaceMemberRole($id, $space_id, $associated_field);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("群组成员管理-列表查询")
         */
        public function getGroupMemberList(): \Psr\Http\Message\ResponseInterface
        {
            list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
            $sort = $this->request->input('sort', 'mapping_id');
            $result = $this->service->getGroupMemberList($filter, $op, $sort, $order, (int)$limit, $search, $searchFields);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("群组成员管理-增改")
         */
        public function doGroupMemberEdit(): \Psr\Http\Message\ResponseInterface
        {
            $mapping_id = (int) $this->request->input('mapping_id', 0);
            $params = $this->request->all();
            unset($params['mapping_id']);
            $result = $this->service->doGroupMemberEdit($mapping_id, $params);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("群组成员管理-批量添加")
         */
        public function doGroupMemberMulti(): \Psr\Http\Message\ResponseInterface
        {
            $group_id = (int) $this->request->input('group_id', 0);
            $user_ids = $this->request->input('user_ids', []);
            $result = $this->service->doGroupMemberMulti($group_id, $user_ids);
            return $this->response->success($result);
        }
        /**
         * @ControllerNameAnnotation("群组成员管理-删除")
         */
        public function doGroupMemberDelete(): \Psr\Http\Message\ResponseInterface
        {
            $validated = make(IdsRequest::class)->validated();
            $result = $this->service->doGroupMemberDelete($validated['ids']);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("群组成员管理-获取群组成员权限角色")
         */
        public function getGroupMemberRole(): \Psr\Http\Message\ResponseInterface
        {
            $user_id = (int) $this->request->input('user_id', 0);
            $group_id = (int) $this->request->input('group_id', 0);
            $result = $this->service->getGroupMemberRole($user_id, $group_id );
            return $this->response->success($result);
        }
        /// 成员管理 END
        //////////////////////////////////////////////


        //////////////////////////////////////////////
        /// 历史记录
        /**
         * @ControllerNameAnnotation("Wiki历史记录-列表查询")
         */
        public function getWikiHistoryList(): \Psr\Http\Message\ResponseInterface
        {
            $doc_id = (int) $this->request->input('doc_id', 0);
            $result = $this->service->getWikiHistoryList($doc_id);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("Wiki历史记录-恢复到指定版本")
         */
        public function restoreWikiVersion(): \Psr\Http\Message\ResponseInterface
        {
            $version_id = (int) $this->request->input('version_id', 0);
            $result = $this->service->restoreWikiVersion($version_id);
            return $this->response->success($result);
        }
        
        /// 历史记录 END
        //////////////////////////////////////////////


        //////////////////////////////////////////////
        /// 文档状态管理
        /**
         * @ControllerNameAnnotation("文档状态-设置审核状态")
         */
        public function setDocumentAuditStatus(): \Psr\Http\Message\ResponseInterface
        {
            $doc_id = (int) $this->request->input('doc_id', 0);
            $audit_status = (int) $this->request->input('audit_status', 0); // 0:待审核 1:通过 2:拒绝
            $reason = $this->request->input('reason', '');
            $operator_id = $this->auth->user()->getId();
            $add_point = (bool) $this->request->input('add_point', false);
            
            $result = $this->documentStatusService->setAuditStatus($doc_id, $audit_status, $operator_id, $reason, $add_point);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("文档状态-设置精华认证")
         */
        public function setDocumentPremiumStatus(): \Psr\Http\Message\ResponseInterface
        {
            $doc_id = (int) $this->request->input('doc_id', 0);
            $is_premium = (bool) $this->request->input('is_premium', false);
            $reason = $this->request->input('reason', '');
            $operator_id = $this->auth->user()->getId();
            $add_point = (bool) $this->request->input('add_point', true);

            $result = $this->documentStatusService->setPremiumStatus($doc_id, $is_premium, $operator_id, $reason, $add_point);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("文档状态-设置培训认证")
         */
        public function setDocumentTrainingStatus(): \Psr\Http\Message\ResponseInterface
        {
            $doc_id = (int) $this->request->input('doc_id', 0);
            $is_training = (bool) $this->request->input('is_training', false);
            $reason = $this->request->input('reason', '');
            $operator_id = $this->auth->user()->getId();
            $add_point = (bool) $this->request->input('add_point', true);

            $result = $this->documentStatusService->setTrainingStatus($doc_id, $is_training, $operator_id, $reason, $add_point);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("文档状态-批量审核文档")
         */
        public function batchAuditDocuments(): \Psr\Http\Message\ResponseInterface
        {
            $doc_ids = $this->request->input('doc_ids', []);
            $audit_status = (int) $this->request->input('audit_status', 1);
            $reason = $this->request->input('reason', '');
            $operator_id = $this->auth->user()->getId();

            $result = $this->documentStatusService->batchAuditDocuments($doc_ids, $audit_status, $operator_id, $reason);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("文档状态-查询文档状态信息")
         */
        public function getDocumentStatus(): \Psr\Http\Message\ResponseInterface
        {
            $doc_id = (int) $this->request->input('doc_id', 0);
            $result = $this->documentStatusService->getDocumentStatus($doc_id);
            return $this->response->success($result);

        }

        /**
         * @ControllerNameAnnotation("文档状态-获取状态历史")
         */
        public function getDocumentStatusHistory(): \Psr\Http\Message\ResponseInterface
        {
            $doc_id = (int) $this->request->input('doc_id', 0);
            $result = $this->documentStatusService->getDocumentStatusHistory($doc_id);
            return $this->response->success($result);
        }
        /// 文档状态管理 END
        //////////////////////////////////////////////


        /////////////////////////////////////////////
        /// 文档标签
        /**
         * @ControllerNameAnnotation("文档标签-列表查询")
         */
        public function getDocTagList(): \Psr\Http\Message\ResponseInterface
        {
            list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
            $result = $this->service->getDocTagList($filter, $op, $sort, $order, (int)$limit, $search, $searchFields);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("文档标签-新增/编辑") 
         */
        public function doDocTagEdit(): \Psr\Http\Message\ResponseInterface
        {
            $id = (int) $this->request->input('id', 0);
            $params = $this->request->all();
            unset($params['id']);
            $result = $this->service->doDocTagEdit($id, $params); 
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("文档标签-删除")
         */
        public function doDocTagDelete(): \Psr\Http\Message\ResponseInterface
        {
            $validated = make(IdsRequest::class)->validated();
            $result = $this->service->doDocTagDelete($validated['ids']);
            return $this->response->success($result);
        }
        /// 文档标签 END
        //////////////////////////////////////////////
    }