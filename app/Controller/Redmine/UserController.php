<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/14 下午7:58
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Redmine;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Redmine\GroupsUsersService;
use App\Core\Services\Redmine\UserService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * @ControllerNameAnnotation("Redmine接口-用户")
 * @AutoController(prefix="/redmine/user/index")
 * @Middleware(AuthMiddleware::class)
 */
class UserController extends \App\Controller\BaseController
{
    /**
     * @Inject
     * @var GroupsUsersService
     */
    protected $groupUsersService;

    /**
     * @Inject
     * @var UserService
     */
    protected $service;

    public function myGroupUser()
    {
        $result = $this->groupUsersService->myGroupUser();
        return $this->response->success($result);
    }

    /**
     * 获取用户处理事项，关注等统计数据
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function getCountInfo()
    {
        $result = $this->service->getCountInfo();
        return $this->response->success($result);
    }

}