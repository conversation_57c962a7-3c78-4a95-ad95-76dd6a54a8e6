<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/17 下午4:38
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\TongjiBd;

use App\Core\Services\TongjiBd\TongjiBdReportService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use App\Request\TongjiBd\ReportRequest;

/**
 * 访问百度原生接口控制器
 * @AutoController()
 */
class ReportController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var TongjiBdReportService
     */
    protected $tongjiBdReportService;

    /**
     * 网站概况(趋势数据)
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function getTimeTrendRpt()
    {
        $siteId = $this->request->input('site_id');
        $stDate = $this->request->input('start_date');
        $enDate = $this->request->input('end_date');
        $result = $this->tongjiBdReportService->getTimeTrendRpt($siteId, $stDate, $enDate);
        return $this->response->success($result);
    }

    /**
     * 趋势分析
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function trendTimeA()
    {
        $siteId = $this->request->input('site_id');
        $result = $this->tongjiBdReportService->trendTimeA($siteId);
        return $this->response->success($result);
    }

    /**
     * 来源分析
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function overviewGetCommonTrackRpt()
    {
        $siteId = $this->request->input('site_id');
        $result = $this->tongjiBdReportService->overviewGetCommonTrackRpt($siteId);
        return $this->response->success($result);
    }

    /**
     * 来源分析
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function sourceAllA()
    {
        $siteId = $this->request->input('site_id');
        $time   = date('Ymd', strtotime('-1 day'));
        $param  = [ 'viewType' => $this->tongjiBdReportService->source_view_site, 'clientDevice' => 'pc'];
        $result = $this->tongjiBdReportService->sourceAllA($siteId, $time, null, $param);
        return $this->response->success($result);
    }

    /**
     * 受访页面
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function visitToppageA()
    {
        $siteId = $this->request->input('site_id');
        $time   = date('Ymd', strtotime('-1 day'));
        $param  = [ 'viewType' => $this->tongjiBdReportService->source_view_site, 'clientDevice' => 'pc'];
        $result = $this->tongjiBdReportService->sourceAllA($siteId, $time, null, $param);
        return $this->response->success($result);
    }
}