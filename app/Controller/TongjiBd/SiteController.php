<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/17 下午2:36
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\TongjiBd;

use App\Core\Services\TongjiBd\TongjiBdSiteService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;

/**
 * @AutoController()
 */
class SiteController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var TongjiBdSiteService
     */
    protected $tongjiBdSiteService;

    public function syncSiteList()
    {
        $result = $this->tongjiBdSiteService->getSiteList();
        return $this->response->success($result);
    }

    public function getSiteList()
    {
        $result = $this->tongjiBdSiteService->siteList();
        return $this->response->success($result);
    }
}