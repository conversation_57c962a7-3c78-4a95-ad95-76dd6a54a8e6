<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/31 下午4:58
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Marketing;

use App\Core\Services\Marketing\MarketingProductMonthReportService;
use App\Request\Marketing\BrandCodeDateRequest;
use App\Request\Marketing\ProductNameDateRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;

/**
 * @AutoController()
 */
class MarketingProductMonthReportController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var MarketingProductMonthReportService
     */
    protected $service;


    public function monthReport(BrandCodeDateRequest $request)
    {
        $validated = $request->validated();
        return $this->response->success($this->service->monthReport($validated['brand_code'], $validated['date']));
    }

    public function statisticsSales(BrandCodeDateRequest $request)
    {
        $validated = $request->validated();
        $productName = $this->request->input('product_name');
        return $this->response->success($this->service->statisticsSales($validated['brand_code'], $validated['date'], $productName));
    }

    public function statisticsSalesByDay(BrandCodeDateRequest $request)
    {
        $validated = $request->validated();
        $productName = $this->request->input('product_name');
        return $this->response->success($this->service->statisticsSalesByDay($validated['brand_code'], $validated['date'], $productName));
    }

    public function assignProductSales(ProductNameDateRequest $request)
    {
        $validated = $request->validated();
        return $this->response->success($this->service->assignProductSales($validated['product_name'], $validated['date']));
    }

    public function monthProductReport(BrandCodeDateRequest $request)
    {
        $validated = $request->validated();
        return $this->response->success($this->service->monthProductReport($validated['brand_code'], $validated['date']));
    }

}