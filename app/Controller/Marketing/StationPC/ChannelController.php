<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/23 上午11:38
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Marketing\StationPC;

use App\Constants\AutoUserCountCode;
use App\Core\Services\Marketing\ChannelService;
use App\Core\Services\Marketing\ContentService;
use App\Request\MarketingContentRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use Psr\Http\Message\ResponseInterface;

/**
 * 推广频道
 * @AutoController(prefix="/marketing/stationpc/channel/index")
 * @Middleware(AuthMiddleware::class)
 */
class ChannelController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var ChannelService
     */
    protected $service;

    /**
     * 采集用户数可执行动作列表
     * @return ResponseInterface
     */
    public function getAutoUserCountFunc(): ResponseInterface
    {
        $result = AutoUserCountCode::AUTO_USER_COUNT;
        return $this->response->success($result);
    }

    /**
     * 采集具体某个频道用户数
     * @return ResponseInterface
     */
    public function doAutoUserCountFunc(): ResponseInterface
    {
        $channelId = $this->request->input('channel_id');
        $result = $this->service->collectUserCountFunc($channelId);
        return $this->response->success($result);
    }

    /**
     * 采集所有频道用户数
     * @return ResponseInterface
     */
    public function doAutoUserCount(): ResponseInterface
    {
        $result = $this->service->collectUserCount();
        return $this->response->success($result);
    }
}