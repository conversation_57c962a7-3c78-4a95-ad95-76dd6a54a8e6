<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/9/8 上午11:54
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Marketing\StationPC;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Marketing\AppReportService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;

/**
 * @ControllerNameAnnotation("StationPC应用")
 * @AutoController(prefix="/marketing/stationpc/app")
 */
class AppController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var AppReportService
     */
    protected $service;

    public function getPanelData()
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->service->getPanelData($filter, $op, $sort, $order, $limit);
        return $this->response->success($result);
    }

    public function getTrendData()
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->service->getTrendData($filter, $op, $sort, $order, $limit);
        return $this->response->success($result);
    }

    public function getTableData()
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->service->getTableData($filter, $op, $sort, $order, $limit);
        return $this->response->success($result);
    }
}