<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/13 下午4:16
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Marketing\StationPC;

use App\Core\Services\Marketing\IndexService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Psr\Http\Message\ResponseInterface;

/**
 * 品牌运营-StationPC-运营数据
 * @AutoController(prefix="/marketing/stationpc/index")
 */
class IndexController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var IndexService
     */
    protected $marketingIndexService;

    /**
     * 首页四格数据
     * @return ResponseInterface
     */
    public function topCard(): ResponseInterface
    {
        $result = [
            [
                'title' => '用户数',
                'icon' => 'user-search-line',
                'config' => [
                    'endVal' => $this->marketingIndexService->topCardUser(),
                    'ext' => [
                        'text' => '本月新增',
                        'value' => $this->marketingIndexService->topCardUserMonth()
                    ]
                ]

            ],
            [
                'title' => '推广内容',
                'icon' => 'ball-pen-line',
                'config' => [
                    'endVal' => $this->marketingIndexService->topCardContentCount(),
                    'ext' => [
                        'text' => '本月新增',
                        'value' => $this->marketingIndexService->topCardContentCountMonth()
                    ]
                ]
            ],
            [
                'title' => '曝光量',
                'icon' => 'globe-line',
                'config' => [
                    'endVal' => $this->marketingIndexService->topCardContentExposure(),
                    'ext' => [
                        'text' => '本月新增',
                        'value' => $this->marketingIndexService->topCardContentExposureMonth()
                    ]
                ]
            ],
            [
                'title' => '设备数',
                'icon' => 'device-line',
                'config' => [
                    'endVal' => $this->marketingIndexService->topCardDevice(),
                    'ext' => [
                        'text' => '本月新增',
                        'value' => $this->marketingIndexService->topCardDeviceMonthAdd()
                    ]
                ]
            ],
        ];
        return $this->response->success($result);
    }

    /**
     * 用户列表Top10
     * @return ResponseInterface
     */
    public function getChannelList(): ResponseInterface
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->marketingIndexService->getChannelList($filter, $op, $sort, $order, $limit);
        return $this->response->success($result);
    }

    /**
     * 推广内容Top10
     * @return ResponseInterface
     */
    public function getContentList(): ResponseInterface
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->marketingIndexService->getContentList($filter, $op, $sort, $order, $limit);
        return $this->response->success($result);
    }

    /**
     * 推广内容分布
     * @return ResponseInterface
     */
    public function getContentRatio(): ResponseInterface
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->marketingIndexService->getContentRatio($filter, $op, $sort, $order, $limit);
        return $this->response->success($result);
    }

    /**
     * 曝光量趋势
     * @return ResponseInterface
     */
    public function getExposureTrend(): ResponseInterface
    {
        $result = $this->marketingIndexService->getExposureTrend();
        return $this->response->success($result);
    }

    /**
     * 用户增长趋势
     * @return ResponseInterface
     */
    public function userIncreaseTrend(): ResponseInterface
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->marketingIndexService->userIncreaseTrend($filter, $op);
        return $this->response->success($result);
    }

    /**
     * 用户增长趋势
     * @return ResponseInterface
     */
    public function userIncreaseDayTrend(): ResponseInterface
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->marketingIndexService->userIncreaseTrend($filter, $op, 'day');
        return $this->response->success($result);
    }

}