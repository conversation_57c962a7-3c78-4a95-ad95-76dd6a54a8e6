<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/31 下午4:58
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Marketing;

use App\Core\Services\Marketing\MarketingBrandMonthReportService;
use App\Request\Marketing\BrandCodeDateRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;

/**
 * @AutoController()
 */
class MarketingBrandMonthReportController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var MarketingBrandMonthReportService
     */
    protected $service;

    public function getSummary(BrandCodeDateRequest $request)
    {
        $validated = $request->validated();
        return $this->response->success($this->service->getSummary($validated['brand_code'], $validated['date']));
    }

    /**
     * 获取品牌数据汇总(所有历史数据)
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function getSummaryTotal()
    {
        $brand_code = $this->request->input('brand_code');

        // 如果 date 为空，设置默认值
            $startDate = '2000-01-01';
            $endDate = date('Y-m-d'); // 获取今天的日期
            $date = $startDate . ' - ' . $endDate;

        return $this->response->success($this->service->getSummaryTotal($brand_code, $date));
    }


    /**
     * 月度推广预算表头数据
     * @param BrandCodeDateRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function getMonthReport(BrandCodeDateRequest $request)
    {
        $validated = $request->validated();
        return $this->response->success($this->service->getMonthReport($validated['brand_code'], $validated['date']));
    }

    /**
     * 获取编辑计划数据
     * @param BrandCodeDateRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function getBrandPromtion(BrandCodeDateRequest $request)
    {
        $validated = $request->validated();
        return $this->response->success($this->service->getBrandPromtion($validated['brand_code'], $validated['date']));
    }

    /**
     * 保存计划数据
     * @param BrandCodeDateRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function saveBrandPromtion(BrandCodeDateRequest $request)
    {
        $validated = $request->validated();
        $values = $this->request->all();
        return $this->response->success($this->service->saveBrandPromtion($validated['brand_code'], $validated['date'], $values));
    }

    /**
     * 保存品牌销售目标数据
     * @param BrandCodeDateRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function saveBrandReport(BrandCodeDateRequest $request)
    {
        $validated = $request->validated();
        $values = $this->request->all();
        return $this->response->success($this->service->saveBrandReport($validated['brand_code'], $validated['date'], $values));
    }
}