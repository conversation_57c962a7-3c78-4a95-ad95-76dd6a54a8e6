<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/31 下午4:58
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Marketing;

use App\Core\Services\Marketing\MarketingPlatformService;
use App\Request\Marketing\BrandCodeDateRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;

/**
 * @AutoController()
 */
class MarketingPlatformController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var MarketingPlatformService
     */
    protected $service;

    public function savePlatform()
    {
        $items = $this->request->input('items');
        return $this->response->success($this->service->savePlatform($items));
    }

}