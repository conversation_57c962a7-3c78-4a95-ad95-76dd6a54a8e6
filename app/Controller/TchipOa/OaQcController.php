<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/22 上午10:12
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\TchipOa;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\TchipOa\OaQcService;
use App\Core\Services\TchipOa\OaQcSamplingService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * @ControllerNameAnnotation("QC管理")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class OaQcController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var OaQcService
     */
    protected $service;

    /**
     * @Inject()
     * @var OaQcSamplingService
     */
    protected $SamplingService;


    /**
     * @ControllerNameAnnotation("批量修改")
     */
    public function handleMulti()
    {
        $items = $this->request->input('items', []);
        return $this->response->success($this->service->handleMulti($items));
    }

    /**
     * @ControllerNameAnnotation("获取pmpc分类列表")
     */
    public function getPmpcCategoryList()
    {
        return $this->response->success($this->service->getPmpcCategoryList());
    }

    /**
     * @ControllerNameAnnotation("导出Excel")
     */
    public function exportExcel()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        return $this->service->exportExcel($filter, $op, $sort, $order, $limit);
    }

    /**
     * @ControllerNameAnnotation("获取质检列表")
     * 20250418 添加供应商筛选条件
     */
    public function getList()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getList($filter, $op, $sort, $order, (int) $limit, $search, $searchFields);
        return $this->response->success($result);
    }

    /**
     * @ControllerNameAnnotation("获取供应商列表")
     */
    public function getSupplierList()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getAllSuppliers($filter, $op, $sort, $order, (int) $limit, $search, $searchFields);
        return $this->response->success($result);
    }

    /**
     * @ControllerNameAnnotation("获取归属地列表")
     */
    public function getAttributionList()
    {
        return $this->response->success($this->service->getAttributionList());
    }

    /**
     * @ControllerNameAnnotation("获取不合格数量")
     */
    public function getDefectiveNum()
    {
        return $this->response->success($this->service->getDefectiveNum());
    }

     /**
     * @ControllerNameAnnotation("获取抽样参数")
     */
    public function getSamplingParams()
    {
        $returnQuantity = $this->request->input('return_quantity');
        $defectType = $this->request->input('defect_type');
        $inspectionLevel = $this->request->input('inspection_level', 'Normal-2');

        $result = $this->SamplingService->getSamplingParams($returnQuantity, $defectType, $inspectionLevel);
        return $this->response->success($result);

    }

    /**
     * @ControllerNameAnnotation("获取检查水平列表")
     */
    public function getInspectionLevels()
    {
        $result = $this->SamplingService->getInspectionLevels();
        return $this->response->success($result);
    }
}