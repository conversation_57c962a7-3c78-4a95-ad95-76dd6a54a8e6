<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2025/01/01
 * <AUTHOR>
 * @Description 特采订单管理
 *
 */

namespace App\Controller\TchipOa;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\TchipOa\OaQcSpecialOrderService;
use App\Exception\AppException;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use App\Middleware\MenuMiddleware;

/**
 * @ControllerNameAnnotation("特采订单管理")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class OaQcSpecialOrderController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var OaQcSpecialOrderService
     */
    protected $service;

    /**
     * @ControllerNameAnnotation("获取特采订单列表")
     * @Middleware(MenuMiddleware::class)
     */
    public function getList()
    {
        try {
            list($filter, $op, $sort, $order, $limit) = $this->getParams();
            $result = $this->service->getList($filter, $op, $sort, $order, (int)$limit);
            return $this->response->success($result);
        } catch (\Exception $e) {
            return $this->response->error('获取失败：' . $e->getMessage());
        }
    }

    /**
     * @ControllerNameAnnotation("获取特采订单详情")
     * @Middleware(MenuMiddleware::class)
     */
    public function show()
    {
        try {
            $id = $this->request->input('id');
            $result = $this->service->getDetail($id);
            if (!$result) {
                return $this->response->error('订单不存在');
            }

            return $this->response->success($result);
        } catch (\Exception $e) {
            return $this->response->error('获取失败：' . $e->getMessage());
        }
    }
    
    /**
     * @ControllerNameAnnotation("审批特采订单")
     * @Middleware(MenuMiddleware::class)
     */
    public function approve()
    {
        try {

            $id = (int)$this->request->input('id');
            $action = $this->request->input('action');
            $comment = $this->request->input('comment', '');
            $rejectToStep = $this->request->input('reject_to_step');

            $result = $this->service->approve($id, $action, $comment, $rejectToStep);

            $actionText = $action === 'approve' ? '审批通过' : '审批驳回';
            return $this->response->success($actionText . '成功');
        } catch (AppException $e) {
            return $this->response->error($e->getMessage());
        } catch (\Exception $e) {
            return $this->response->error('审批失败：' . $e->getMessage());
        }
    }

    /**
     * @ControllerNameAnnotation("取消特采订单")
     * @Middleware(MenuMiddleware::class)
     */
    public function cancel()
    {
        try {
            $id = (int)$this->request->input('id');
            $reason = $this->request->input('reason', '');

            if (!$id) {
                return $this->response->error('订单ID不能为空');
            }

            $result = $this->service->cancel($id, $reason);
            return $this->response->success('取消成功');
        } catch (AppException $e) {
            return $this->response->error($e->getMessage());
        } catch (\Exception $e) {
            return $this->response->error('取消失败：' . $e->getMessage());
        }
    }

    /**
     * @ControllerNameAnnotation("检查QC记录是否已有特采订单")
     */
    public function checkExists()
    {
        try {
            $qcOrderId = (int)$this->request->input('qc_order_id');
            $result = $this->service->checkSpecialOrderExists($qcOrderId);
            return $this->response->success($result);
        } catch (\Exception $e) {
            return $this->response->error('检查失败：' . $e->getMessage());
        }
    }

    /**
     * @ControllerNameAnnotation("从QC记录创建特采订单")
     * @Middleware(MenuMiddleware::class)
     */
    public function createFromQc()
    {
        try {
            $qcOrderId = (int)$this->request->input('qc_order_id');
            if (!$qcOrderId) {
                return $this->response->error('QC订单ID不能为空');
            }

            // 获取申请信息
            $applicationData = [
                'special_reason' => $this->request->input('special_reason'),
                'special_scope' => $this->request->input('special_scope'),
            ];

            $result = $this->service->createFromQc($qcOrderId, $applicationData);
            return $this->response->success($result);
        } catch (AppException $e) {
            return $this->response->error($e->getMessage());
        } catch (\Exception $e) {
            return $this->response->error('创建失败：' . $e->getMessage());
        }
    }

    /**
     * @ControllerNameAnnotation("重新提交被退回的订单")
     * @Middleware(MenuMiddleware::class)
     */
    public function resubmit()
    {
        try {

            $id = (int)$this->request->input('id');
            $updateData = [
                'special_reason' => $this->request->input('special_reason'),
                'special_scope' => $this->request->input('special_scope'),
            ];

            // 过滤空值
            $updateData = array_filter($updateData, function($value) {
                return $value !== null && $value !== '';
            });

            $result = $this->service->resubmit($id, $updateData);
            return $this->response->success('重新提交成功', $result);
        } catch (AppException $e) {
            return $this->response->error($e->getMessage());
        } catch (\Exception $e) {
            return $this->response->error('重新提交失败：' . $e->getMessage());
        }
    }

    /**
     * @ControllerNameAnnotation("更新特采订单信息")
     * @Middleware(MenuMiddleware::class)
     */
    public function update()
    {
        try {
            $id = (int)$this->request->input('id');
            $requestData = $this->request->all();

            $result = $this->service->updateOrderInfo($id, $requestData);
            return $this->response->success('更新成功', $result);
        } catch (AppException $e) {
            return $this->response->error($e->getMessage());
        } catch (\Exception $e) {
            return $this->response->error('更新失败：' . $e->getMessage());
        }
    }
}