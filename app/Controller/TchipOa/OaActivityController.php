<?php
    /**
     * @Copyright T-chip Team.
     * @Date 2024/03/18 17:36
     * <AUTHOR>
     * @Description
     */

    namespace App\Controller\TchipOa;
    use Hyperf\Di\Annotation\Inject;
    use Hyperf\HttpServer\Annotation\AutoController;
    use Hyperf\HttpServer\Annotation\Middleware;
    use App\Middleware\AuthMiddleware;
    use Psr\Http\Message\ResponseInterface;
    use App\Annotation\ControllerNameAnnotation;

    use App\Core\Services\TchipOa\OaActivityService;

    /**
     * @ControllerNameAnnotation("活动模块")
     * @AutoController()
     */


    class OaActivityController extends \App\Controller\BaseController
    {
        /**
         * @Inject
         * @var OaActivityService
         */
        protected $service;

        public function getList(): ResponseInterface
        {
            list($filter, $op, $sort, $order, $limit) = $this->getParams();
            $result = $this->service->getList($filter, $op, $sort, $order, $limit);
            return $this->response->success($result);
        }

    }