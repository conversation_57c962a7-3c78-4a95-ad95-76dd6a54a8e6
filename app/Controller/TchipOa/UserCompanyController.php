<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/22 上午10:12
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\TchipOa;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\TchipOa\UserCompanyService;
use App\Core\Services\TchipOa\UserPartService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use Psr\Http\Message\ResponseInterface;

/**
 * @ControllerNameAnnotation("OA公司管理")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class UserCompanyController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var UserCompanyService
     */
    protected $service;

    /**
     * @Inject()
     * @var UserPartService
     */
    protected $userPartService;

    public function structType()
    {
        $type = $this->request->input('type');
        $result = $this->userCompanyService->structList($type);
        return $this->response->success($result);
    }

    public function getTreeList()
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->service->getTreeList($filter, $op, $sort, $order);
        return $this->response->success($result);
    }
}