<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/22 上午10:12
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\TchipOa;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\TchipOa\OaQcErpService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * @ControllerNameAnnotation("ERPQC列表")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class OaQcErpController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var OaQcErpService
     */
    protected $service;

    public function getStatusList()
    {
        return $this->response->success($this->service->getStatusList());
    }

    public function getHanldeList()
    {
        $status = $this->request->input('status');
        return $this->response->success($this->service->getHanldeList($status));
    }

    public function getExamineNum()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $num = make(\App\Core\Services\TchipOa\OaQcExamineNumCnfService::class)->getExamineNum($filter, $op);
        return $this->response->success(['num' => $num]);
    }

    public  function checkDuplicate()
    {
        $params = $this->request->input('params');
        $result = $this->service->checkDuplicate($params);
        return $this->response->success($result);
    }

    public  function getFactoryLocName()
    {
        $result = $this->service->getFactoryLocName();
        return $this->response->success($result);
    }

    // 获取ERP产品列表
    public function getErpProductList()
    {
        return $this->response->success($this->service->getErpProductList());
    }

    //获取供应商详情
    public function getErpSupplierInfo()
    {
        $order_factory_code = $this->request->input('order_factory_code');
        return $this->response->success($this->service->getErpSupplierInfo($order_factory_code));
    }
}