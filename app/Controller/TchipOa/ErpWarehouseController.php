<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/22 上午10:12
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\TchipOa;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\TchipOa\ErpWarehouseService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * @ControllerNameAnnotation("ERP仓库管理")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class ErpWarehouseController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var ErpWarehouseService
     */
    protected $service;

    public function sync()
    {
        $result = $this->service->sync();
        return $this->response->success($result);
    }

    public function getList()
    {
        return parent::getList(); // TODO: Change the autogenerated stub
    }
}