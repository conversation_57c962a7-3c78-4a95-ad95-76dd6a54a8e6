<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/22 上午10:12
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\TchipOa;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\TchipOa\OaProductBorrowDetailsService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * @ControllerNameAnnotation("产品借用详情管理")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class OaProductBorrowDetailsController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var OaProductBorrowDetailsService
     */
    protected $service;

    public function getReport()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getReport($filter, $op, $sort, $order, $limit);
        return $this->response->success($result);
    }

    public function getReportDetails()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getReportDetails($filter, $op);
        return $this->response->success($result);
    }
}