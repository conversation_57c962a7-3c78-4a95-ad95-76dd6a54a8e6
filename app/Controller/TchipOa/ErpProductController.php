<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/22 上午10:12
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\TchipOa;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\TchipOa\ErpProductService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * @ControllerNameAnnotation("ERP产品管理")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class ErpProductController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var ErpProductService
     */
    protected $service;

    public function getOverViewByCode()
    {
        $code = $this->request->input('code');
        return $this->response->success($this->service->getOverViewByCode($code));
    }

    /**
     * 导入丝印
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function excelImport()
    {
        $items = $this->request->file('file');
        return $this->response->success($this->service->excelImport($items));
    }

    /**
     * 导入免检料号
     * @return \Psr\Http\Message\ResponseInterface
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function importExempt()
    {
        $items = $this->request->file('file');
        return $this->response->success($this->service->importExempt($items));
    }
}