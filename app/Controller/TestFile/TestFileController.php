<?php
declare(strict_types=1);

namespace App\Controller\TestFile;

use App\Controller\BaseController;
use App\Core\Services\TestFile\FileManagerService;
use App\Core\Services\TestFile\ReorganizeService;
use App\Constants\StatusCode;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\PostMapping;
use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\HttpServer\Contract\ResponseInterface;
use Carbon\Carbon;

/**
 * @Controller(prefix="/api/testfile")
 */
class TestFileController extends BaseController
{
    /**
     * @Inject
     * @var FileManagerService
     */
    protected $fileService;
    
    /**
     * @Inject
     * @var ReorganizeService
     */
    protected $reorganizeService;
    
    /**
     * 获取树形目录（懒加载版本）
     * 支持分页、排序、过滤等参数
     * @GetMapping(path="tree")
     */
    public function getTree()
    {
        // 使用统一的 getParams 方法获取参数
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $search = $this->request->input('search', '');
        $sort = $this->request->input('sort', 'product');
        $order = $this->request->input('order', 'ASC');
        
        // 获取树形特有的参数
        $node = $this->request->input('node', '');
        $depth = (int) $this->request->input('depth', 1);
        $forTree = (bool) $this->request->input('for_tree', false);
        $page = (int) $this->request->input('page', 1);
        
        // 获取额外的筛选参数
        $fileType = $this->request->input('file_type', '');
        $agingFilters = $this->request->input('aging_filters', []);
        $factoryFilters = $this->request->input('factory_filters', []);

        try {
            // 调用 service 方法，传递分离的参数
            $result = $this->fileService->getTreeData(
                $node,
                $depth,
                $filter,
                $op,
                $sort,
                $order,
                $limit,
                $page,
                $forTree,
                $search,
                $fileType,
                $agingFilters,
                $factoryFilters
            );
            return $this->response->success($result);
        } catch (\Exception $e) {
            return $this->response->error(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }
    
    /**
     * 获取完整目录树（一次性加载所有层级）
     * @GetMapping(path="tree/full")
     */
    public function getFullTree()
    {
        // 使用统一的 getParams 方法获取参数
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $search = $this->request->input('search', '');
        
        // 获取额外的筛选参数
        $fileType = $this->request->input('file_type', '');
        $agingFilters = $this->request->input('aging_filters', []);
        $factoryFilters = $this->request->input('factory_filters', []);
        
        try {
            // 调用 service 方法获取完整树结构
            $result = $this->fileService->getFullTreeData(
                $filter,
                $op,
                $search,
                $fileType,
                $agingFilters,
                $factoryFilters
            );
            return $this->response->success($result);
        } catch (\Exception $e) {
            return $this->response->error(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }
    
    /**
     * 获取文件列表
     * @GetMapping(path="files")
     */
    public function getFiles()
    {
        $params = $this->request->all();
        
        try {
            $result = $this->fileService->getFileList($params);
            return $this->response->success($result);
        } catch (\Exception $e) {
            return $this->response->error(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }
    
    /**
     * 获取文件详情
     * @GetMapping(path="files/{id}")
     */
    public function getFileDetail(int $id)
    {
        try {
            $file = $this->fileService->getFileById($id);
            
            if (!$file) {
                return $this->response->error(StatusCode::ERR_SERVER, '文件不存在');
            }
            
            return $this->response->success($file);
        } catch (\Exception $e) {
            return $this->response->error(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }
    
    /**
     * 删除文件
     * @DeleteMapping(path="files/{id}")
     */
    public function deleteFile(int $id)
    {
        try {
            $result = $this->fileService->deleteFile($id);
            
            if (!$result) {
                return $this->response->error(StatusCode::ERR_EXCEPTION, '删除失败');
            }
            
            return $this->response->success(['deleted' => true]);
        } catch (\Exception $e) {
            return $this->response->error(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }
    
    /**
     * 下载文件
     * @GetMapping(path="files/{id}/download")
     */
    public function downloadFile(int $id, ResponseInterface $response)
    {
        try {
            return $this->fileService->downloadFile($id, $response);
        } catch (\Exception $e) {
            return $this->response->error(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }
    
    /**
     * 获取文件内容（用于预览）
     * @GetMapping(path="files/{id}/content")
     */
    public function getFileContent(int $id)
    {
        try {
            $file = $this->fileService->getFileById($id);
            
            if (!$file) {
                return $this->response->error(StatusCode::ERR_SERVER, '文件不存在');
            }
            
            // 获取文件路径
            $filePath = $file['dst_path'] ?? null;
            if (!$filePath || !file_exists($filePath)) {
                return $this->response->error(StatusCode::ERR_SERVER, '文件路径无效或文件不存在');
            }
            
            // 检查文件大小，限制预览大文件
            $maxSize = 5 * 1024 * 1024; // 5MB
            $fileSize = filesize($filePath);
            if ($fileSize > $maxSize) {
                return $this->response->error(StatusCode::ERR_SERVER, '文件过大，无法预览（限制5MB以内）');
            }
            
            // 检查文件类型，只允许预览文本类文件
            $allowedExtensions = ['txt', 'log', 'json', 'xml', 'csv', 'md', 'yml', 'yaml', 'ini', 'conf', 'cfg'];
            $fileExtension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
            
            if (!in_array($fileExtension, $allowedExtensions)) {
                // 对于非文本文件，返回基本信息而不是内容
                return $this->response->success([
                    'content' => '',
                    'type' => 'binary',
                    'message' => '该文件不支持预览',
                    'file_info' => [
                        'name' => $file['filename'],
                        'size' => $fileSize,
                        'extension' => $fileExtension,
                        'created_at' => $file['created_at'] ?? null
                    ]
                ]);
            }
            
            // 读取文件内容
            $content = file_get_contents($filePath);
            
            // 检测并转换编码为UTF-8
            $encoding = mb_detect_encoding($content, ['UTF-8', 'GBK', 'GB2312', 'ASCII'], true);
            if ($encoding && $encoding !== 'UTF-8') {
                $content = mb_convert_encoding($content, 'UTF-8', $encoding);
            }
            
            return $this->response->success([
                'content' => $content,
                'type' => 'text',
                'encoding' => $encoding,
                'file_info' => [
                    'name' => $file['filename'],
                    'size' => $fileSize,
                    'extension' => $fileExtension,
                    'created_at' => $file['created_at'] ?? null
                ]
            ]);
        } catch (\Exception $e) {
            return $this->response->error(StatusCode::ERR_EXCEPTION, '读取文件内容失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 手动触发重排
     * @PostMapping(path="reorganize")
     */
    public function triggerReorganize()
    {
        $params = $this->request->all();
        
        try {
            $result = $this->reorganizeService->execute($params);
            
            return $this->response->success([
                'task_id' => $result['task_id'],
                'message' => '重排任务已创建'
            ]);
        } catch (\Exception $e) {
            return $this->response->error(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }
    
    /**
     * 获取重排任务列表
     * @GetMapping(path="reorganize/tasks")
     */
    public function getTasks()
    {
        $params = $this->request->all();
        
        try {
            $result = $this->reorganizeService->getTaskList($params);
            return $this->response->success($result);
        } catch (\Exception $e) {
            return $this->response->error(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }
    
    /**
     * 获取任务详情
     * @GetMapping(path="reorganize/tasks/{id}")
     */
    public function getTaskDetail(int $id)
    {
        try {
            $task = $this->reorganizeService->getTaskById($id);
            
            if (!$task) {
                return $this->response->error(StatusCode::ERR_SERVER, '任务不存在');
            }
            
            return $this->response->success($task);
        } catch (\Exception $e) {
            return $this->response->error(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }
    
    /**
     * 获取统计数据
     * @GetMapping(path="statistics")
     */
    public function getStatistics()
    {
        try {
            $result = $this->fileService->getStatistics();
            return $this->response->success($result);
        } catch (\Exception $e) {
            return $this->response->error(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }
    
    /**
     * 获取老化测试统计数据
     * @GetMapping(path="statistics/aging-test")
     */
    public function getAgingTestStatistics()
    {
        $params = $this->request->all();
        
        try {
            $result = $this->fileService->getAgingTestStatistics($params);
            return $this->response->success($result);
        } catch (\Exception $e) {
            return $this->response->error(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }
    
    /**
     * 获取厂测统计数据
     * @GetMapping(path="statistics/factory-test")
     */
    public function getFactoryTestStatistics()
    {
        $params = $this->request->all();
        
        try {
            $result = $this->fileService->getFactoryTestStatistics($params);
            return $this->response->success($result);
        } catch (\Exception $e) {
            return $this->response->error(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }
    
    /**
     * 获取产品列表
     * @GetMapping(path="products")
     */
    public function getProducts()
    {
        $page = (int) $this->request->input('page', 1);
        $pageSize = (int) $this->request->input('page_size', 20);
        
        try {
            $result = $this->fileService->getProducts($page, $pageSize);
            return $this->response->success($result);
        } catch (\Exception $e) {
            return $this->response->error(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }
    
    /**
     * 添加产品
     * @PostMapping(path="products")
     */
    public function createProduct()
    {
        $data = $this->request->all();
        
        try {
            $product = $this->fileService->createProduct($data);
            return $this->response->success($product);
        } catch (\Exception $e) {
            return $this->response->error(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }
    
    /**
     * 删除产品
     * @DeleteMapping(path="products/{id}")
     */
    public function deleteProduct(int $id)
    {
        try {
            $result = $this->fileService->deleteProduct($id);
            
            if (!$result) {
                return $this->response->error(StatusCode::ERR_SERVER, '产品不存在');
            }
            
            return $this->response->success(['deleted' => true]);
        } catch (\Exception $e) {
            return $this->response->error(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }
}
