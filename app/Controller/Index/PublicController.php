<?php
/**
 * @Copyright T-chip Team.
 * @Date 2023/3/31 下午2:37
 * <AUTHOR> X
 * @Description
 */

namespace App\Controller\Index;

use App\Annotation\ControllerNameAnnotation;
use App\Controller\BaseController;
use App\Core\Services\Index\PublicService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use Hyperf\Utils\ApplicationContext;

/**
 * @ControllerNameAnnotation("公共模块")
 * @AutoController()
 */
class PublicController extends BaseController
{
    /**
     * @Inject()
     * @var PublicService
     */
    protected $service;

    /**
     * @ControllerNameAnnotation("全局搜索")
     * @Middleware(AuthMiddleware::class)
     */
    public function globalSearch()
    {
        $key = $this->request->input('key');
        $page = $this->request->input('pageNo');
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->globalSearch($key, $filter, $op, $sort, $order, $limit, $page);
        return $this->response->success($result ?? null);
    }

    /**
     * 清空全系统缓存
     * @return void
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function clearCaches()
    {
        $cache = ApplicationContext::getContainer()->get(\Psr\SimpleCache\CacheInterface::class);
        return $this->response->success('清理成功', $cache->clear());
    }
}