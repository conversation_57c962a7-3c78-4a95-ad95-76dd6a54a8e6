<?php
/**
 * @Copyright T-chip Team.
 * @Date 2023/3/31 下午2:37
 * <AUTHOR> X
 * @Description
 */

namespace App\Controller\Index;

use App\Controller\BaseController;
use App\Core\Services\Index\FeedbackService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use App\Annotation\ControllerNameAnnotation;

/**
 * @ControllerNameAnnotation("首页反馈")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class FeedbackController extends BaseController
{
    /**
     * @Inject()
     * @var FeedbackService
     */
    protected $service;

    /**
     * @ControllerNameAnnotation("我的反馈列表")
     */
    public function myList()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->myList($filter, $op, $sort, $order, $limit);
        return $this->response->success($result);
    }
}