<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/8/17 上午10:46
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\MpWx;

use App\Core\Services\MpWx\FreepublishMpWxService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;

/**
 * @AutoController()
 */
class FreepublishController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var FreepublishMpWxService
     */
    protected $service;

    public function batchget()
    {
        $result = $this->service->batchget();
        return $this->response->success($result);
    }
}