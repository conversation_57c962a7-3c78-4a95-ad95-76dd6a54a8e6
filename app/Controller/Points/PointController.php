<?php

declare(strict_types=1);

namespace App\Controller\Points;

use App\Core\Utils\Response;
use App\Controller\BaseController;
use App\Core\Services\Points\PointService;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\PostMapping;
use Hyperf\HttpServer\Annotation\PutMapping;
use Hyperf\Di\Annotation\Inject;
use Psr\Http\Message\ResponseInterface;
use OpenApi\Annotations as OA;

/**
 * 积分管理控制器
 * @Controller(prefix="/api/points")
 * @OA\Tag(
 *     name="积分系统",
 *     description="用户积分相关接口"
 * )
 */
class PointController extends BaseController
{
    /**
     * @Inject
     * @var Response
     */
    protected $response;

    /**
     * @Inject
     * @var PointService
     */
    protected $pointService;

    /**
     * 获取当前用户积分信息
     * @GetMapping(path="my")
     * @OA\Get(
     *     path="/api/points/my",
     *     summary="获取当前用户积分信息",
     *     tags={"积分系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function getMyPoints(): ResponseInterface
    {
        $userId = $this->getCurrentUserId();
        $data = $this->pointService->getMyPointsDetail($userId);
        return $this->response->success($data);
    }

    /**
     * 获取积分记录列表
     * @GetMapping(path="records")
     * @OA\Get(
     *     path="/api/points/records",
     *     summary="获取积分记录列表",
     *     tags={"积分系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function getPointRecords(): ResponseInterface
    {
        $userId = $this->getCurrentUserId();
        $params = $this->request->all();
        $records = $this->pointService->getUserPointRecords($userId, $params);
        return $this->response->success($records);
    }

    /**
     * 获取积分统计信息
     * @GetMapping(path="statistics")
     * @OA\Get(
     *     path="/api/points/statistics",
     *     summary="获取积分统计信息",
     *     tags={"积分系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function getPointStatistics(): ResponseInterface
    {
        $userId = $this->getCurrentUserId();
        $statistics = $this->pointService->getUserPointStatistics($userId);
        return $this->response->success($statistics);
    }

    /**
     * 获取积分排行榜
     * @GetMapping(path="rankings")
     * @OA\Get(
     *     path="/api/points/rankings",
     *     summary="获取积分排行榜",
     *     tags={"积分系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function getPointRankings(): ResponseInterface
    {
        $params = $this->request->all();
        $rankings = $this->pointService->getPointRankings($params);
        return $this->response->success($rankings);
    }

    /**
     * 手动添加积分（管理员功能）
     * @PostMapping(path="admin/add")
     * @OA\Post(
     *     path="/api/points/admin/add",
     *     summary="手动添加积分",
     *     tags={"积分系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function addPoints(): ResponseInterface
    {
        $this->checkAdminPermission();
        $params = $this->validateRequest([
            'user_id' => 'required|integer',
            'points' => 'required|integer|min:1',
            'reason' => 'required|string|max:255',
            'point_type' => 'sometimes|string|max:50'
        ]);
        
        $result = $this->pointService->addPointsByAdmin(
            $params['user_id'],
            $params['points'],
            $params['reason'],
            $this->getCurrentUserId(),
            $params['point_type']
        );
        
        return $this->response->success($result, '积分添加成功');
    }

    /**
     * 扣除积分（管理员功能）
     * @PostMapping(path="admin/deduct")
     * @OA\Post(
     *     path="/api/points/admin/deduct",
     *     summary="扣除积分",
     *     tags={"积分系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function deductPoints(): ResponseInterface
    {
        $this->checkAdminPermission();
        $params = $this->validateRequest([
            'user_id' => 'required|integer',
            'points' => 'required|integer|min:1',
            'reason' => 'required|string|max:255'
        ]);

        $result = $this->pointService->deductPointsByAdmin(
            $params['user_id'],
            $params['points'],
            $params['reason'],
            $this->getCurrentUserId()
        );

        return $this->response->success($result, '积分扣除成功');
    }


    /**
     * 获取积分配置列表（管理员功能）
     * @GetMapping(path="admin/configs")
     * @OA\Get(
     *     path="/api/points/admin/configs",
     *     summary="获取积分配置列表",
     *     tags={"积分系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function getPointConfigs(): ResponseInterface
    {
        $this->checkAdminPermission();
        $configs = $this->pointService->getPointConfigs();
        return $this->response->success($configs);
    }

    /**
     * 更新积分配置（管理员功能）
     * @PostMapping(path="admin/configs/{id:\d+}")
     * @OA\Put(
     *     path="/api/points/admin/configs/{id}",
     *     summary="更新积分配置",
     *     tags={"积分系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function updatePointConfig(int $id): ResponseInterface
    {
        $this->checkAdminPermission();
        $params = $this->validateRequest([
            'action_name' => 'sometimes|string|max:100',
            'points' => 'sometimes|integer',
            'max_daily_count' => 'sometimes|integer|nullable',
            'max_daily_points' => 'sometimes|integer|nullable',
            'first_time_bonus' => 'sometimes|integer',
            'category' => 'sometimes|string|max:50',
            'priority' => 'sometimes|integer',
            'description' => 'sometimes|string|nullable',
            'is_active' => 'sometimes|boolean'
        ]);

        $result = $this->pointService->updatePointConfig($id, $params);
        return $this->response->success($result, '积分配置更新成功');
    }

    /**
     * 批量初始化用户积分（管理员功能）
     * @PostMapping(path="admin/batch-init")
     * @OA\Post(
     *     path="/api/points/admin/batch-init",
     *     summary="批量初始化用户积分",
     *     tags={"积分系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function batchInitUserPoints(): ResponseInterface
    {
        $this->checkAdminPermission();
        $params = $this->request->all();
        $userIds = $params['user_ids'] ?? [];
        
        $results = $this->pointService->batchInitUserPoints($userIds);
        return $this->response->success($results, '批量初始化完成');
    }

    /**
     * 获取用户积分详情（管理员功能）
     * @GetMapping(path="admin/user/{userId:\d+}/points")
     * @OA\Get(
     *     path="/api/points/admin/user/{userId}/points",
     *     summary="获取用户积分详情",
     *     tags={"积分系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function getUserPointDetail(int $userId): ResponseInterface
    {
        $this->checkAdminPermission();
        $data = $this->pointService->getUserPointDetail($userId);
        return $this->response->success($data);
    }

    /**
     * 验证并修复用户周期积分数据（管理员功能）
     * @PostMapping(path="admin/fix-period-points")
     * @OA\Post(
     *     path="/api/points/admin/fix-period-points",
     *     summary="修复用户周期积分数据",
     *     tags={"积分系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function fixPeriodPoints(): ResponseInterface
    {
        $this->checkAdminPermission();
        $params = $this->request->all();
        
        $userId = $params['user_id'] ?? null;
        $limit = isset($params['limit']) ? (int)$params['limit'] : 100;

        if ($userId) {
            // 修复指定用户
            $result = $this->pointService->validateAndFixPeriodPoints((int)$userId);
            return $this->response->success($result, '用户周期积分数据检查完成');
        } else {
            // 批量修复
            $results = $this->pointService->batchValidateAndFixPeriodPoints($limit);
            
            $stats = [
                'total_users' => count($results),
                'success_count' => count(array_filter($results, fn($r) => $r['success'])),
                'fixed_count' => count(array_filter($results, fn($r) => $r['success'] && $r['fixes_count'] > 0)),
                'error_count' => count(array_filter($results, fn($r) => !$r['success'])),
                'results' => $results
            ];
            
            return $this->response->success($stats, '批量周期积分数据检查完成');
        }
    }

    /**
     * 获取管理员积分统计信息
     * @GetMapping(path="admin/statistics")
     * @OA\Get(
     *     path="/api/points/admin/statistics",
     *     summary="获取管理员积分统计信息",
     *     tags={"积分系统"},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="页码",
     *         @OA\Schema(type="integer", default=1)
     *     ),
     *     @OA\Parameter(
     *         name="pageSize",
     *         in="query", 
     *         description="每页数量",
     *         @OA\Schema(type="integer", default=20)
     *     ),
     *     @OA\Parameter(
     *         name="username",
     *         in="query",
     *         description="用户名搜索",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="minPoints",
     *         in="query",
     *         description="最小积分",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="maxPoints",
     *         in="query",
     *         description="最大积分",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="level",
     *         in="query",
     *         description="用户等级",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function getAdminPointStatistics(): ResponseInterface
    {
        $this->checkAdminPermission();
        $params = $this->request->all();
        $statistics = $this->pointService->getAdminPointStatistics($params);
        return $this->response->success($statistics);
    }

    /**
     * 获取知识豆查阅数据
     * @GetMapping(path="admin/view")
     * @OA\Get(
     *     path="/api/points/admin/view",
     *     summary="获取知识豆查阅数据",
     *     tags={"积分系统"},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="页码",
     *         @OA\Schema(type="integer", default=1)
     *     ),
     *     @OA\Parameter(
     *         name="pageSize",
     *         in="query", 
     *         description="每页数量",
     *         @OA\Schema(type="integer", default=20)
     *     ),
     *     @OA\Parameter(
     *         name="startDate",
     *         in="query",
     *         description="开始日期 (YYYY-MM-DD)",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="endDate",
     *         in="query",
     *         description="结束日期 (YYYY-MM-DD)",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="quarterYear",
     *         in="query",
     *         description="季度年份",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="quarter",
     *         in="query",
     *         description="季度 (1-4)",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="departmentIds",
     *         in="query",
     *         description="部门ID (逗号分隔)",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="userId",
     *         in="query",
     *         description="用户ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function getPointsViewData(): ResponseInterface
    {
        $this->checkAdminPermission();
        $params = $this->request->all();
        $data = $this->pointService->getPointsViewData($params);
        return $this->response->success($data);
    }

}