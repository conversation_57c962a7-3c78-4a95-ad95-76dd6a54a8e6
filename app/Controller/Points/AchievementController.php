<?php

declare(strict_types=1);

namespace App\Controller\Points;

use App\Core\Utils\Response;
use App\Controller\BaseController;
use App\Core\Services\Points\AchievementService;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\PostMapping;
use Hyperf\HttpServer\Annotation\PutMapping;
use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\Di\Annotation\Inject;
use Psr\Http\Message\ResponseInterface;
use OpenApi\Annotations as OA;

/**
 * 成就系统控制器
 * @Controller(prefix="/api/achievements")
 * @OA\Tag(
 *     name="成就系统",
 *     description="用户成就相关接口"
 * )
 */
class AchievementController extends BaseController
{

    /**
     * @Inject
     * @var Response
     */
    protected $response;

    /**
     * @Inject
     * @var AchievementService
     */
    protected $achievementService;

    /**
     * 获取成就列表
     * @GetMapping(path="")
     * @OA\Get(
     *     path="/api/achievements",
     *     summary="获取成就列表",
     *     tags={"成就系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function getAchievements(): ResponseInterface
    {
        $userId = $this->getCurrentUserId();
        $params = $this->request->all();
        $achievements = $this->achievementService->getUserAchievements($userId, $params);
        return $this->response->success($achievements);
    }

    /**
     * 获取我的成就
     * @GetMapping(path="my-achievements")
     * @OA\Get(
     *     path="/api/achievements/my-achievements",
     *     summary="获取我的成就",
     *     tags={"成就系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function getMyAchievements(): ResponseInterface
    {
        $userId = $this->getCurrentUserId();
        $params = $this->request->all();
        $data = $this->achievementService->getMyAchievements($userId, $params);
        return $this->response->success($data);
    }

    /**
     * 获取成就详情
     * @GetMapping(path="detail/{id:\d+}")
     * @OA\Get(
     *     path="/api/achievements/detail/{id}?user_id={user_id}",
     *     summary="获取成就详情",
     *     tags={"成就系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function getAchievementDetail(int $id): ResponseInterface
    {
        $userId = (int) $this->request->query('user_id', $this->getCurrentUserId());
        $achievement = $this->achievementService->getAchievementDetailWithUserProgress($userId, $id);
        return $this->response->success($achievement);
    }

    /**
     * 获取成就排行榜
     * @GetMapping(path="rankings")
     * @OA\Get(
     *     path="/api/achievements/rankings",
     *     summary="获取成就排行榜",
     *     tags={"成就系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function getAchievementRankings(): ResponseInterface
    {
        $params = $this->request->all();
        $rankings = $this->achievementService->getAchievementRankings($params);
        return $this->response->success($rankings);
    }

    /**
     * 获取成就分类
     * @GetMapping(path="categories")
     * @OA\Get(
     *     path="/api/achievements/categories",
     *     summary="获取成就分类",
     *     tags={"成就系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function getAchievementCategories(): ResponseInterface
    {
        $categories = $this->achievementService->getAchievementCategories();
        return $this->response->success($categories);
    }

    /**
     * 手动检查成就触发（管理员功能）
     * @PostMapping(path="admin/check/{userId:\d+}")
     * @OA\Post(
     *     path="/api/achievements/admin/check/{userId}",
     *     summary="检查用户成就",
     *     tags={"成就系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function checkUserAchievements(int $userId): ResponseInterface
    {
        $this->checkAdminPermission();
        $result = $this->achievementService->checkUserAchievements($userId);
        return $this->response->success($result);
    }



    /**
     * 获取成就统计信息（管理员功能）
     * @GetMapping(path="admin/achievement-statistics")
     * @OA\Get(
     *     path="/api/achievements/admin/achievement-statistics",
     *     summary="获取成就统计",
     *     tags={"成就系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function getAchievementStatistics(): ResponseInterface
    {
        $this->checkAdminPermission();
        $statistics = $this->achievementService->getAchievementStatistics();
        return $this->response->success($statistics);
    }

    /**
     * 创建成就（管理员功能）
     * @PostMapping(path="admin/achievements")
     * @OA\Post(
     *     path="/api/achievements/admin/achievements",
     *     summary="创建成就",
     *     tags={"成就系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function createAchievement(): ResponseInterface
    {
        $this->checkAdminPermission();
        $params = $this->validateRequest([
            'code' => 'required|string|max:50|unique:achievements,code',
            'name' => 'required|string|max:100',
            'description' => 'required|string',
            'category' => 'required|string|max:50',
            'difficulty' => 'required|integer|in:1,2,3,4,5',
            'condition_type' => 'required|string|max:50',
            'condition_value' => 'required|integer|min:1',
            'reward_points' => 'required|integer|min:0',
            'icon' => 'required|string|max:255',
            'badge_image' => 'required|string|max:255',
            'tips' => 'sometimes|string|nullable',
            'is_hidden' => 'sometimes|boolean',
            'is_repeatable' => 'sometimes|boolean',
            'unlock_level' => 'sometimes|integer|min:1',
            'display_order' => 'sometimes|integer',
            'is_active' => 'sometimes|boolean'
        ]);

        $achievement = $this->achievementService->createAchievement($params);
        return $this->response->success($achievement, '成就创建成功');
    }

    /**
     * 更新成就（管理员功能）
     * @PutMapping(path="admin/achievements/{id:\d+}")
     * @OA\Put(
     *     path="/api/achievements/admin/achievements/{id}",
     *     summary="更新成就",
     *     tags={"成就系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function updateAchievement(int $id): ResponseInterface
    {
        $this->checkAdminPermission();
        $params = $this->validateRequest([
            'name' => 'sometimes|string|max:100',
            'description' => 'sometimes|string',
            'category' => 'sometimes|string|max:50',
            'difficulty' => 'sometimes|integer|in:1,2,3,4,5',
            'condition_type' => 'sometimes|string|max:50',
            'condition_value' => 'sometimes|integer|min:1',
            'reward_points' => 'sometimes|integer|min:0',
            'icon' => 'sometimes|string|max:255',
            'badge_image' => 'sometimes|string|max:255',
            'tips' => 'sometimes|string|nullable',
            'is_hidden' => 'sometimes|boolean',
            'is_repeatable' => 'sometimes|boolean',
            'unlock_level' => 'sometimes|integer|min:1',
            'display_order' => 'sometimes|integer',
            'is_active' => 'sometimes|boolean'
        ]);

        $achievement = $this->achievementService->updateAchievement($id, $params);
        return $this->response->success($achievement, '成就更新成功');
    }

    /**
     * 删除成就（管理员功能）
     * @DeleteMapping(path="admin/achievements/{id:\d+}")
     * @OA\Delete(
     *     path="/api/achievements/admin/achievements/{id}",
     *     summary="删除成就",
     *     tags={"成就系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function deleteAchievement(int $id): ResponseInterface
    {
        $this->checkAdminPermission();
        $result = $this->achievementService->deleteAchievement($id);
        return $this->response->success($result, '成就删除成功');
    }

    /**
     * 获取成就管理列表（管理员功能）
     * @GetMapping(path="admin/achievements")
     * @OA\Get(
     *     path="/api/achievements/admin/achievements",
     *     summary="获取管理员成就列表",
     *     tags={"成就系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function getAdminAchievementList(): ResponseInterface
    {
        $this->checkAdminPermission();
        $params = $this->request->all();
        $achievements = $this->achievementService->getAdminAchievementList($params);
        return $this->response->success($achievements);
    }

    /**
     * 初始化用户成就（管理员功能）
     * @PostMapping(path="admin/initialize-user-achievements")
     * @OA\Post(
     *     path="/api/achievements/admin/initialize-user-achievements",
     *     summary="初始化用户成就",
     *     tags={"成就系统"},
     *     @OA\Parameter(
     *         name="user_id",
     *         in="query",
     *         description="用户ID，为空时初始化所有用户",
     *         required=false,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="force_update",
     *         in="query",
     *         description="是否强制更新已存在记录",
     *         required=false,
     *         @OA\Schema(type="boolean")
     *     ),
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function initializeUserAchievements(): ResponseInterface
    {
        $this->checkAdminPermission();
        
        $userId = $this->request->input('user_id');
        $forceUpdate = (bool) $this->request->input('force_update', false);
        
        if ($userId) {
            $userId = (int) $userId;
        }

        $result = $this->achievementService->initializeUserAchievements($userId, $forceUpdate);
        
        $message = $userId ? '指定用户成就初始化完成' : '所有用户成就初始化完成';
        return $this->response->success($result, $message);
    }

    /**
     * 同步用户成就（管理员功能）
     * @PostMapping(path="admin/sync-achievements")
     * @OA\Post(
     *     path="/api/achievements/admin/sync-achievements",
     *     summary="同步用户成就",
     *     tags={"成就系统"},
     *     @OA\Parameter(
     *         name="achievement_ids",
     *         in="query",
     *         description="指定成就ID列表，为空时同步所有成就",
     *         required=false,
     *         @OA\Schema(type="array", @OA\Items(type="integer"))
     *     ),
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function syncAchievementsForAllUsers(): ResponseInterface
    {
        $this->checkAdminPermission();
        
        $achievementIds = $this->request->input('achievement_ids', []);
        if (is_string($achievementIds)) {
            $achievementIds = explode(',', $achievementIds);
        }
        $achievementIds = array_filter(array_map('intval', $achievementIds));

        $result = $this->achievementService->syncAchievementsForAllUsers($achievementIds);
        
        $message = empty($achievementIds) ? '所有成就同步完成' : '指定成就同步完成';
        return $this->response->success($result, $message);
    }
}