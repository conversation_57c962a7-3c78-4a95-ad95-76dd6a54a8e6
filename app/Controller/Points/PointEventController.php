<?php

declare(strict_types=1);

namespace App\Controller\Points;

use App\Core\Utils\Response;
use App\Controller\BaseController;
use App\Core\Services\Points\PointEventService;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\PostMapping;
use Hyperf\HttpServer\Annotation\PutMapping;
use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\Di\Annotation\Inject;
use Psr\Http\Message\ResponseInterface;
use OpenApi\Annotations as OA;

/**
 * 积分活动控制器
 * @Controller(prefix="/api/point-events")
 * @OA\Tag(
 *     name="积分活动",
 *     description="积分活动相关接口"
 * )
 */
class PointEventController extends BaseController
{
    /**
     * @Inject
     * @var Response
     */
    protected $response;

    /**
     * @Inject
     * @var PointEventService
     */
    protected $pointEventService;

    /**
     * 获取当前进行中的活动
      * @GetMapping(path="current")
     */
    public function getCurrentEvents(): ResponseInterface
    {
        $events = $this->pointEventService->getCurrentEvents();
        return $this->response->success($events);
    }

    /**
     * 获取活动列表  
     * @GetMapping(path="")
     * @OA\Get(
     *     path="/api/point-events",
     *     summary="获取积分活动列表",
     *     tags={"积分活动"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function getEvents(): ResponseInterface
    {
        $params = $this->request->all();
        $events = $this->pointEventService->getEvents($params);
        return $this->response->success($events);
    }

    /**
     * 获取活动详情  
     * @GetMapping(path="detail/{id:\d+}")
     * @OA\Get(
     *     path="/api/point-events/detail/{id}",
     *     summary="获取活动详情",
     *     tags={"积分活动"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function getEventDetail(int $id): ResponseInterface
    {
        $userId = $this->getCurrentUserId();
        $event = $this->pointEventService->getEventDetail($userId, $id);
        return $this->response->success($event);
    }

    /**
     * 检查用户参与资格
      * @GetMapping(path="eligibility/{id:\d+}")
     */
    public function checkParticipationEligibility(int $id): ResponseInterface
    {
        $userId = $this->getCurrentUserId();
        $result = $this->pointEventService->canUserParticipate($userId, $id);
        return $this->response->success($result);
    }

    /**
     * 获取活动类型选项
      * @GetMapping(path="types")
     */
    public function getEventTypes(): ResponseInterface
    {
        $types = $this->pointEventService->getEventTypes();
        return $this->response->success($types);
    }

    /**
     * 获取可用的积分行为类型
      * @GetMapping(path="action-types")
     */
    public function getActionTypes(): ResponseInterface
    {
        $actionTypes = $this->pointEventService->getActionTypes();
        return $this->response->success($actionTypes);
    }

    /**
     * 获取活动统计信息（管理员功能）
      * @GetMapping(path="admin/stats/{id:\d+}")
     */
    public function getEventStatistics(int $id): ResponseInterface
    {
        $this->checkAdminPermission();
        $statistics = $this->pointEventService->getEventStatistics($id);
        return $this->response->success($statistics);
    }

    /**
     * 创建活动（管理员功能）  
     * @PostMapping(path="admin/events")
     * @OA\Post(
     *     path="/api/point-events/admin/events",
     *     summary="创建积分活动",
     *     tags={"积分活动"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function createEvent(): ResponseInterface
    {
        $this->checkAdminPermission();
        $params = $this->validateRequest([
            'name' => 'required|string|max:100',
            'description' => 'required|string',
            'event_type' => 'required|string|in:multiplier,bonus,special',
            'action_types' => 'required|array',
            'multiplier' => 'sometimes|numeric|min:0.1',
            'bonus_points' => 'sometimes|integer|min:0',
            'max_participants' => 'sometimes|integer|nullable',
            'start_time' => 'required|date',
            'end_time' => 'required|date',
            'conditions' => 'sometimes|array',
            'rewards' => 'sometimes|array',
            'is_active' => 'sometimes|boolean'
        ]);

        $event = $this->pointEventService->createEvent($params);
        return $this->response->success($event, '活动创建成功');
    }

    /**
     * 更新活动（管理员功能）  * @PutMapping(path="admin/events/{id:\d+}")
     * @OA\Put(
     *     path="/api/point-events/admin/events/{id}",
     *     summary="更新积分活动",
     *     tags={"积分活动"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function updateEvent(int $id): ResponseInterface
    {
        $this->checkAdminPermission();
        $params = $this->validateRequest([
            'name' => 'sometimes|string|max:100',
            'description' => 'sometimes|string',
            'event_type' => 'sometimes|string|in:multiplier,bonus,special',
            'action_types' => 'sometimes|array',
            'multiplier' => 'sometimes|numeric|min:0.1',
            'bonus_points' => 'sometimes|integer|min:0',
            'max_participants' => 'sometimes|integer|nullable',
            'start_time' => 'sometimes|date',
            'end_time' => 'sometimes|date',
            'conditions' => 'sometimes|array',
            'rewards' => 'sometimes|array',
            'is_active' => 'sometimes|boolean'
        ]);

        $event = $this->pointEventService->updateEvent($id, $params);
        return $this->response->success($event, '活动更新成功');
    }

    /**
     * 结束活动（管理员功能）
      * @PostMapping(path="admin/events/{id:\d+}/end")
     */
    public function endEvent(int $id): ResponseInterface
    {
        $this->checkAdminPermission();
        $result = $this->pointEventService->endEvent($id);
        return $this->response->success($result, $result['message']);
    }

    /**
     * 删除活动（管理员功能）  * @DeleteMapping(path="admin/events/{id:\d+}")
     * @OA\Delete(
     *     path="/api/point-events/admin/events/{id}",
     *     summary="删除积分活动",
     *     tags={"积分活动"},
     *     @OA\Response(response=200, description="删除成功")
     * )
     */
    public function deleteEvent(int $id): ResponseInterface
    {
        $this->checkAdminPermission();
        $result = $this->pointEventService->deleteEvent($id);
        return $this->response->success($result, '活动删除成功');
    }

    /**
     * 获取活动管理列表（管理员功能） 
     * @GetMapping(path="admin/events")
     * @OA\Get(
     *     path="/api/point-events/admin/events",
     *     summary="获取管理员活动列表",
     *     tags={"积分活动"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function getAdminEventList(): ResponseInterface
    {
        $this->checkAdminPermission();
        $params = $this->request->all();
        $events = $this->pointEventService->getAdminEventList($params);
        return $this->response->success($events);
    }

    /**
     * 预览活动效果（管理员功能）
      * @PostMapping(path="admin/preview-effect")
     */
    public function previewEventEffect(): ResponseInterface
    {
        $this->checkAdminPermission();
        $params = $this->validateRequest([
            'event_type' => 'required|string|in:multiplier,bonus,special',
            'action_types' => 'required|array',
            'multiplier' => 'sometimes|numeric|min:0.1',
            'bonus_points' => 'sometimes|integer|min:0',
            'conditions' => 'sometimes|array'
        ]);

        $preview = $this->pointEventService->previewEventEffect($params);
        return $this->response->success($preview);
    }
}