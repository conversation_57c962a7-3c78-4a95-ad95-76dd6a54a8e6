<?php

declare(strict_types=1);

namespace App\Controller\Points;

use App\Core\Utils\Response;
use App\Controller\BaseController;
use App\Core\Services\Points\LevelService;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Annotation\PostMapping;
use Hyperf\HttpServer\Annotation\PutMapping;
use Hyperf\HttpServer\Annotation\DeleteMapping;
use Hyperf\Di\Annotation\Inject;
use Psr\Http\Message\ResponseInterface;
use OpenApi\Annotations as OA;

/**
 * 等级系统控制器
 * @Controller(prefix="/api/levels")
 * @OA\Tag(
 *     name="等级系统",
 *     description="用户等级相关接口"
 * )
 */
class LevelController extends BaseController
{

    /**
     * @Inject
     * @var Response
     */
    protected $response;

    /**
     * @Inject
     * @var LevelService
     */
    protected $levelService;

    /**
     * 获取等级配置列表
     * @GetMapping(path="")
     * @OA\Get(
     *     path="/api/levels",
     *     summary="获取等级配置列表",
     *     tags={"等级系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function getLevels(): ResponseInterface
    {
        $levels = $this->levelService->getLevelConfigs();
        return $this->response->success($levels);
    }

    /**
     * 获取我的等级信息
     * @GetMapping(path="my-level")
     * @OA\Get(
     *     path="/api/levels/my-level",
     *     summary="获取我的等级信息",
     *     tags={"等级系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function getMyLevel(): ResponseInterface
    {
        $userId = $this->getCurrentUserId();
        $levelInfo = $this->levelService->getUserLevelInfo($userId);
        return $this->response->success($levelInfo);
    }

    /**
     * 获取等级分布统计
     * @GetMapping(path="distribution")
     * @OA\Get(
     *     path="/api/levels/distribution",
     *     summary="获取等级分布统计",
     *     tags={"等级系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function getLevelDistribution(): ResponseInterface
    {
        $distribution = $this->levelService->getLevelDistribution();
        return $this->response->success($distribution);
    }

    /**
     * 获取升级历史
     * @GetMapping(path="upgrade-history")
     * @OA\Get(
     *     path="/api/levels/upgrade-history",
     *     summary="获取升级历史",
     *     tags={"等级系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function getUpgradeHistory(): ResponseInterface
    {
        $userId = $this->getCurrentUserId();
        $history = $this->levelService->getUserUpgradeHistory($userId);
        return $this->response->success($history);
    }

    /**
     * 获取升级路径预览
     * @GetMapping(path="upgrade-path")
     * @OA\Get(
     *     path="/api/levels/upgrade-path",
     *     summary="获取升级路径预览",
     *     tags={"等级系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function getUpgradePath(): ResponseInterface
    {
        $userId = $this->getCurrentUserId();
        $params = $this->request->all();
        $targetLevel = $params['target_level'] ?? null;
        
        $path = $this->levelService->getUpgradePath($userId, $targetLevel);
        return $this->response->success($path);
    }

    /**
     * 检查用户权限
     * @GetMapping(path="privileges/{privilege:[a-z_]+}")
     * @OA\Get(
     *     path="/api/levels/privileges/{privilege}",
     *     summary="检查用户权限",
     *     tags={"等级系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function checkPrivilege(string $privilege): ResponseInterface
    {
        $userId = $this->getCurrentUserId();
        $result = $this->levelService->checkUserPrivilege($userId, $privilege);
        return $this->response->success($result);
    }

    /**
     * 获取可用特权列表
     * @GetMapping(path="privileges")
     * @OA\Get(
     *     path="/api/levels/privileges",
     *     summary="获取可用特权列表",
     *     tags={"等级系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function getAvailablePrivileges(): ResponseInterface
    {
        $privileges = $this->levelService->getAvailablePrivileges();
        return $this->response->success($privileges);
    }

    /**
     * 手动升级用户等级（管理员功能）
     * @PostMapping(path="admin/upgrade/{userId:\d+}")
     * @OA\Post(
     *     path="/api/levels/admin/upgrade/{userId}",
     *     summary="手动升级用户等级",
     *     tags={"等级系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function upgradeUserLevel(int $userId): ResponseInterface
    {
        $this->checkAdminPermission();
        $result = $this->levelService->checkAndUpgradeLevel($userId);
        return $this->response->success($result, $result['upgraded'] ? '等级升级成功' : '无需升级');
    }

    /**
     * 获取等级管理列表（管理员功能）
     * @GetMapping(path="admin/levels")
     * @OA\Get(
     *     path="/api/levels/admin/levels",
     *     summary="获取等级管理列表",
     *     tags={"等级系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function getAdminLevelList(): ResponseInterface
    {
        $this->checkAdminPermission();
        $levels = $this->levelService->getAdminLevelList();
        return $this->response->success($levels);
    }

    /**
     * 创建等级配置（管理员功能）
     * @PostMapping(path="admin/levels")
     * @OA\Post(
     *     path="/api/levels/admin/levels",
     *     summary="创建等级配置",
     *     tags={"等级系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function createLevel(): ResponseInterface
    {
        $this->checkAdminPermission();
        $params = $this->validateRequest([
            'level' => 'required|integer|min:1',
            'level_name' => 'required|string|max:50',
            'level_title' => 'required|string|max:100',
            'min_points' => 'required|integer|min:0',
            'max_points' => 'sometimes|integer|nullable',
            'color' => 'required|string|max:20',
            'icon' => 'required|string|max:255',
            'badge_image' => 'required|string|max:255',
            'privileges' => 'sometimes|array',
            'upgrade_reward' => 'sometimes|integer|min:0',
            'description' => 'sometimes|string|nullable',
            'is_active' => 'sometimes|boolean',
            'sort_order' => 'sometimes|integer'
        ]);

        $level = $this->levelService->createLevel($params);
        return $this->response->success($level, '等级配置创建成功');
    }

    /**
     * 更新等级配置（管理员功能）
     * @PutMapping(path="admin/levels/{id:\d+}")
     * @OA\Put(
     *     path="/api/levels/admin/levels/{id}",
     *     summary="更新等级配置",
     *     tags={"等级系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function updateLevel(int $id): ResponseInterface
    {
        $this->checkAdminPermission();
        $params = $this->validateRequest([
            'level_name' => 'sometimes|string|max:50',
            'level_title' => 'sometimes|string|max:100',
            'min_points' => 'sometimes|integer|min:0',
            'max_points' => 'sometimes|integer|nullable',
            'color' => 'sometimes|string|max:20',
            'icon' => 'sometimes|string|max:255',
            'badge_image' => 'sometimes|string|max:255',
            'privileges' => 'sometimes|array',
            'upgrade_reward' => 'sometimes|integer|min:0',
            'description' => 'sometimes|string|nullable',
            'is_active' => 'sometimes|boolean',
            'sort_order' => 'sometimes|integer'
        ]);

        $level = $this->levelService->updateLevel($id, $params);
        return $this->response->success($level, '等级配置更新成功');
    }

    /**
     * 删除等级配置（管理员功能）
     * @DeleteMapping(path="admin/levels/{id:\d+}")
     * @OA\Delete(
     *     path="/api/levels/admin/levels/{id}",
     *     summary="删除等级配置",
     *     tags={"等级系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function deleteLevel(int $id): ResponseInterface
    {
        $this->checkAdminPermission();
        $result = $this->levelService->deleteLevel($id);
        return $this->response->success($result, '等级配置删除成功');
    }

    /**
     * 获取用户等级详情（管理员功能）
     * @GetMapping(path="admin/user/{userId:\d+}/level")
     * @OA\Get(
     *     path="/api/levels/admin/user/{userId}/level",
     *     summary="获取用户等级详情",
     *     tags={"等级系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function getUserLevelDetail(int $userId): ResponseInterface
    {
        $this->checkAdminPermission();
        $data = $this->levelService->getUserLevelDetail($userId);
        return $this->response->success($data);
    }

    /**
     * 批量升级用户等级（管理员功能）
     * @PostMapping(path="admin/batch-upgrade")
     * @OA\Post(
     *     path="/api/levels/admin/batch-upgrade",
     *     summary="批量升级用户等级",
     *     tags={"等级系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function batchUpgradeUsers(): ResponseInterface
    {
        $this->checkAdminPermission();
        $params = $this->request->all();
        $userIds = $params['user_ids'] ?? [];
        
        $results = $this->levelService->batchUpgradeUsers($userIds);
        return $this->response->success($results, '批量升级处理完成');
    }

    /**
     * 获取等级统计信息（管理员功能）
     * @GetMapping(path="admin/level-statistics")
     * @OA\Get(
     *     path="/api/levels/admin/level-statistics",
     *     summary="获取等级统计信息",
     *     tags={"等级系统"},
     *     @OA\Response(response=200, description="操作成功")
     * )
     */
    public function getLevelStatistics(): ResponseInterface
    {
        $this->checkAdminPermission();
        $statistics = $this->levelService->getLevelStatistics();
        return $this->response->success($statistics);
    }
}