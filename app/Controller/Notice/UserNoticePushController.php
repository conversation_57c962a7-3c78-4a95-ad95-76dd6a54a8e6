<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/10/26 下午5:24
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Notice;

use App\Annotation\ControllerNameAnnotation;
use App\Constants\StatusCode;
use App\Controller\BaseController;
use App\Core\Services\Notice\UserNoticePushService;
use App\Exception\AppException;
use Qbhy\HyperfAuth\AuthManager;
use App\Request\Product\ProductRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use Psr\Http\Message\ResponseInterface;
use App\Request\Notice\UserNoticeListRequest;

/**
 * @ControllerNameAnnotation("用户消息管理")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class UserNoticePushController extends BaseController
{
    /**
     * @var UserNoticePushService
     * @Inject()
     */
    protected $service;

    /**
     * @var AuthManager
     * @Inject()
     */
    protected $authManager;

    public function getUserNoticeList()
    {
        // $validated = $request->validated();
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        if (empty($filter['notice_mode'])) {
            throw new AppException(StatusCode::ERR_SERVER, sprintf(__('common.Missing_parameter:%s'), 'notice_mode'));
        }
        $userId = $this->authManager->user()->getId();
        $filter['user_id'] = $userId;
        //var_dump($filter);
        $result = $this->service->getUserNoticeList($filter, $op, $sort, $order, (int) $limit, $search, $searchFields);
        return $this->response->success($result);
    }

    public function doEdit()
    {
        $id = (int) $this->request->input('id', 0);
        $params = $this->request->all();
        unset($params['id']);
        $result = $this->service->doEdit($id, $params);
        return $this->response->success($result);
    }

    public function getUserUnreadNoticeCount()
    {
        $userId = $this->authManager->user()->getId();
        $count = $this->service->getUserUnreadNoticeCount($userId);
        return $this->response->success($count);
    }

    public function getUserNoticeType()
    {
        $userId = $this->authManager->user()->getId();
        $noticeType = $this->service->getUserNoticeType($userId);
        return $this->response->success($noticeType);
    }

}