<?php
/**
 * @Copyright T-chip Team.
 * @Date 2023/3/28 上午10:30
 * <AUTHOR> X
 * @Description
 */

namespace App\Controller\TchipBbs;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\TchipBbs\UserService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use App\Middleware\BbsUserMiddleware;

/**
 * @ControllerNameAnnotation("文化用户操作")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class UserController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var UserService
     */
    protected $service;

    public function getList()
    {
        return parent::getList(); // TODO: Change the autogenerated stub
    }

    /**
     * @Middleware(BbsUserMiddleware::class)
     */
    public function doEdit()
    {
        return parent::doEdit(); // TODO: Change the autogenerated stub
    }

    public function overView()
    {
        return parent::overView(); // TODO: Change the autogenerated stub
    }
}