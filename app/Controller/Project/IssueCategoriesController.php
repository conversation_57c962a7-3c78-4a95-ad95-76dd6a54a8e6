<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/22 上午10:12
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Project;

use App\Annotation\ControllerNameAnnotation;
use App\Constants\StatusCode;
use App\Core\Services\Project\IssueCategoriesService;
use App\Core\Services\Project\IssueStatusesService;
use App\Core\Services\Project\ProjectService;
use App\Core\Services\Redmine\AttachmentService;
use App\Core\Services\Project\AttachmentService as proAttachmentService;
use App\Exception\AppException;
use App\Request\IdsRequest;
use App\Request\Project\Issue\DoMultiCreateRequest;
use App\Request\Project\NewIssueStatusRequest;
use App\Request\Project\Issue\IssueRelationRequest;
use App\Request\Project\TrackerIdRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use Psr\Http\Message\ResponseInterface;

/**
 * @ControllerNameAnnotation("项目模块管理")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class IssueCategoriesController extends \App\Controller\BaseController
{

    /**
     * @Inject()
     * @var IssueCategoriesService
     */
    protected $service;

    public function doPosition()
    {
        $positions = $this->request->input('position', null);
        if (empty($positions) || !is_array($positions)) {
            throw new AppException(StatusCode::ERR_SERVER, __('common.Missing_parameter'));
        }
        $result = $this->service->doPosition($positions);
        return $this->response->success($result);
    }
}