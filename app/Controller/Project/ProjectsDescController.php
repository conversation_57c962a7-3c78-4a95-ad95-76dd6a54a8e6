<?php
namespace App\Controller\Project;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Project\ProjectsDescService;
use App\Request\IdRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use Psr\Http\Message\ResponseInterface;

/**
 * @ControllerNameAnnotation("项目上线属性")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class ProjectsDescController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var ProjectsDescService
     */
    protected $service;

    public function doResendDesc()
    {
        $validated = make(IdRequest::class)->validated();
        $values = $this->request->all();
        unset($values['id']);
        $result = $this->service->doResendDesc($validated['id'], $values);
        return $this->response->success($result);
    }
}