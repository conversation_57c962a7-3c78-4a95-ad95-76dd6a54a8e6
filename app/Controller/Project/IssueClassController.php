<?php
    /**
     * @Copyright T-chip Team.
     * @Date 2025/01/07 09:31
     * <AUTHOR>
     * @Description
     */


    namespace App\Controller\Project;

    use App\Annotation\ControllerNameAnnotation;
    use App\Core\Services\Project\IssueClassService;
    use Psr\Http\Message\ResponseInterface;
    use Hyperf\Di\Annotation\Inject;
    use Hyperf\HttpServer\Annotation\AutoController;
    use Hyperf\HttpServer\Annotation\Middleware;
    use App\Middleware\AuthMiddleware;

    /**
     * @ControllerNameAnnotation("事项分类")
     * @AutoController(prefix="/project/issue_class/index")
     * @Middleware(AuthMiddleware::class)
     */
    class IssueClassController extends \App\Controller\BaseController
    {
        /**
         * @Inject()
         * @var IssueClassService
         */
        protected $service;


        public function getList()
        {
            list($filter, $op, $sort, $order, $limit) = $this->getParams();
            $result = $this->service->getList($filter, $op, $sort, $order, $limit);
            return $this->response->success($result);
        }

        /**
         * @return ResponseInterface
         */
        public function doEdit(): ResponseInterface
        {
            $id = $this->request->input('id',0);
            $params = $this->request->all();
            unset($params['id']);
            $result = $this->service->doEdit($id, $params);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("事项分类-重新排序函数")
         */
        public function rearrangement()
        {
            $dragNodeId = $this->request->input('drag_node_id');
            $targetNode = $this->request->input('target_node_data');
            $dropToGap = $this->request->input('drop_to_gap');
            $dropPosition = $this->request->input('drop_position');
            $result = $this->service->rearrangement($dragNodeId, $targetNode, $dropToGap, $dropPosition);
            return $this->response->success($result);
        }
    }