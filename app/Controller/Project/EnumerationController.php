<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/29 下午3:58
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Project;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Utils\ProjectEnumeration;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;

/**
 * @ControllerNameAnnotation("Redmin枚举")
 * @AutoController()
 */
class EnumerationController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var ProjectEnumeration
     */
    protected $projectEnumeration;

    public function getList()
    {
        $type = $this->request->input('type', 'IssuePriority');
        $result = $this->projectEnumeration->getEnum($type);
        return $this->response->success($result);
    }
}