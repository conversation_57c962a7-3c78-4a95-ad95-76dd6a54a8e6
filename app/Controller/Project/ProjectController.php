<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/22 上午10:12
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Project;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Project\ProjectService;
use App\Core\Services\Redmine\ProjectService as RedmineProjectService;
use App\Model\Redmine\ProjectModel;
use App\Model\TchipBi\UserModel;
use App\Request\Project\OverViewRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use Psr\Http\Message\ResponseInterface;

/**
 * @ControllerNameAnnotation("项目管理")
 * @AutoController(prefix="/project/project/index")
 * @Middleware(AuthMiddleware::class)
 */
class ProjectController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var ProjectService
     */
    protected $service;

    /**
     * @Inject()
     * @var RedmineProjectService
     */
    protected $redmineProjectService;

    public function getList()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $isTree = $this->request->input('is_tree');
        $isTree = strtolower($isTree) === 'no' ? false : true;
        $result = $this->service->getList($filter, $op, $sort, $order, (int) $limit, $search, $searchFields, $isTree);
        return $this->response->success($result);
    }

    /**
     * 新增与编辑
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function doEdit()
    {
        $id = $this->request->input('id',0);
        $params = $this->request->all();
        unset($params['id']);
        $result = $this->service->doEdit($id, $params);
        return $this->response->success($result);
    }

    public function doEditUnit()
    {
        $id = $this->request->input('id',0);
        $params = $this->request->all();
        unset($params['id']);
        $result = $this->service->doEditUnit($id, $params);
        return $this->response->success($result);
    }

    public function overView()
    {
        $overViewRequest = make(OverViewRequest::class);
        $validated = $overViewRequest->validated();
        $result = $this->service->getOverView((int)$validated['project_id']);
        return $this->response->success($result);
    }

    /**
     * @ControllerNameAnnotation("归档操作")
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function archiving()
    {
        $id = $this->request->input('id',0);
        $result = $this->service->archivingProject($id);
        return $this->response->success($result);
    }

    public function doDelete()
    {
        $id = $this->request->input('id',0);
        $result = $this->service->doDelete($id);
        return $this->response->success($result);
    }

    public function doDeleteUnit()
    {
        $id = $this->request->input('id',0);
        $result = $this->service->doDeleteUnit($id);
        return $this->response->success($result);
    }

    public function getJoinedProjectList()
    {
        $id = $this->request->input('user_id',0);
        $result = $this->service->getJoinedProjectList($id);
        return $this->response->success($result);
    }

    public function getUnitList()
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->service->getUnitList($filter, $op, $sort, $order, (int) $limit);
        return $this->response->success($result);
    }

    /**
     * @ControllerNameAnnotation("上传附件")
     * @return ResponseInterface
     */
    public function uploadFile(): ResponseInterface
    {
        $id = $this->request->input('id',0);
        $params = $this->request->all();
        if (isset($params['id'])) {
            unset($params['id']);
        }
        $ruselt = $this->redmineProjectService->uploadFile($id, $params);
        return $this->response->success($ruselt);
    }
}