<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/22 上午10:12
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Project;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Project\FlowTemplateService;
use App\Request\Project\TypeRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * @ControllerNameAnnotation("项目流程模板管理")
 * @AutoController()
 */
class FlowTemplateController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var FlowTemplateService
     */
    protected $service;

    public function getDefaultTemplate(TypeRequest $request)
    {
        $validated = $request->validated();
        return $this->response->success($this->service->getDefaultTemplate($validated['type']));
    }

}