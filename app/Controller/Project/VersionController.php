<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/8/23 下午3:20
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Project;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Project\VersionsService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;

/**
 * @ControllerNameAnnotation("项目版本")
 * @AutoController(prefix="/project/version/index")
 */
class VersionController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var VersionsService
     */
    protected $service;

    public function getList()
    {
        return parent::getList(); // TODO: Change the autogenerated stub
    }
}