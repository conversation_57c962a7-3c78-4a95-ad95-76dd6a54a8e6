<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/22 上午10:12
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Project;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Project\RoleService;
use App\Core\Services\Redmine\AttachmentService;
use App\Request\Project\NewIssueStatusRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use Psr\Http\Message\ResponseInterface;

/**
 * @ControllerNameAnnotation("Redmin角色管理")
 * @AutoController(prefix="/project/role/index")
 * @Middleware(AuthMiddleware::class)
 */
class RoleController extends \App\Controller\BaseController
{

    /**
     * @Inject()
     * @var RoleService
     */
    protected $service;

    public function getList()
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->service->getList($filter, $op, $sort, $order, $limit);
        return $this->response->success($result);
    }
}