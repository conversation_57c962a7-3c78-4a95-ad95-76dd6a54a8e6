<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/7/4 下午5:21
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Project;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Project\ProjectsTrackersService;
use App\Request\Project\ProjectsWatchersRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * @ControllerNameAnnotation("项目跟进成员")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class ProjectsTrackersController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var ProjectsTrackersService
     */
    protected $service;

    /**
     * @ControllerNameAnnotation("添加跟进成员")
     */
    public function doWatchers()
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->service->getList($filter, $op, $sort, $order, $limit);
        return $this->response->success($result);
    }

    /**
     * @ControllerNameAnnotation("获取项目事项类型列表")
     */
    public function getProjectTrackers()
    {
        $projectId = $this->request->input('project_id');
        if (!$projectId) {
            return $this->response->error('项目ID不能为空');
        }
        
        $result = $this->service->getProjectTrackersWithTemplates($projectId);
        return $this->response->success($result);
    }

    /**
     * @ControllerNameAnnotation("更新事项类型模板绑定")
     */
    public function updateTrackerTemplate()
    {
        $projectId = $this->request->input('project_id');
        $trackerId = $this->request->input('tracker_id');
        $templateId = $this->request->input('template_id');

        if (!$projectId || !$trackerId) {
            return $this->response->error('项目ID和事项类型ID不能为空');
        }

        $result = $this->service->updateTrackerTemplate($projectId, $trackerId, $templateId);

        if ($result) {
            return $this->response->success([], '模板绑定更新成功');
        } else {
            return $this->response->error('模板绑定更新失败');
        }
    }
}