<?php
/**
 * @Copyright T-chip Team.
 * @Date 2023/5/22 上午9:59
 * <AUTHOR>
 * @Description
 */


namespace App\Controller\Project;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Project\CustomFieldsService;
use App\Request\Project\Custom\CustomFieldsRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use App\Middleware\MenuMiddleware;

/**
 * @ControllerNameAnnotation("项目自定义属性")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class CustomFieldsController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var CustomFieldsService
     */
    protected $service;

    public function getProjectCustomFieldList()
    {
        $request = make(CustomFieldsRequest::class);
        $validated = $request->validated();
        $result = $this->service->getProjectCustomFieldList($validated['project_id'], $validated['tracker_id']);
        return $this->response->success($result);
    }
}