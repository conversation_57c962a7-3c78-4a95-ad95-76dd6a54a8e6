<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/7/4 下午5:21
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Project;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Project\AttachmentService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * @ControllerNameAnnotation("项目附件管理")
 * @AutoController(prefix="/project/attachment/index")
 * @Middleware(AuthMiddleware::class)
 */
class AttachmentController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var AttachmentService
     */
    protected $service;
}