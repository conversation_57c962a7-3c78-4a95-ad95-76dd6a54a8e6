<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2025/7/14
 * <AUTHOR>
 * @Description
 *
 */
namespace App\Controller\Project;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Project\IssueStatusesService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use Psr\Http\Message\ResponseInterface;

/**
 * @ControllerNameAnnotation("事项状态")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class IssueStatusesController extends \App\Controller\BaseController
{

    /**
     * @Inject()
     * @var IssueStatusesService
     */
    protected $service;

    /**
     * @ControllerNameAnnotation("获取所有未完成状态IDS")
     * @return ResponseInterface
     */
   public function getToDoStatusIds()
   {
       return $this->response->success($this->service->getToDoStatusIds());
   }

    /**
     * @ControllerNameAnnotation("获取所有关闭状态IDS")
     * @return ResponseInterface
     */
    public function getCloseStatusIds()
    {
        return $this->response->success($this->service->getCloseStatusIds());
    }

}