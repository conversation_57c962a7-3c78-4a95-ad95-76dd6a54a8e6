<?php
    /**
     * @Copyright T-chip Team.
     * @Date 2024/05/08 10:34
     * <AUTHOR>
     * @Description
     */

    namespace App\Controller\Project;
    use App\Annotation\ControllerNameAnnotation;
    use App\Core\Services\Project\FilesDocProductService;
    use Hyperf\Di\Annotation\Inject;
    use Hyperf\HttpServer\Annotation\AutoController;
    use Hyperf\HttpServer\Annotation\Middleware;
    use App\Middleware\AuthMiddleware;
    use Psr\Http\Message\ResponseInterface;

    /**
     * @ControllerNameAnnotation("产品资料文档")
     * @AutoController()
     */
    class FilesDocProductController extends \App\Controller\BaseController
    {
        /**
         * @Inject()
         * @var FilesDocProductService
         */
        protected $service;
        public function getList()
        {
            list($filter, $op, $sort, $order, $limit) = $this->getParams();
            $result = $this->service->getList($filter, $op, $sort, $order, (int)$limit);
            return $this->response->success($result);
        }



        /**
         * @return ResponseInterface
         */
        public function doEdit(): ResponseInterface
        {
            $id = $this->request->input('id',0);
            $params = $this->request->all();
            unset($params['id']);
            $result = $this->service->doEdit($id, $params);
            return $this->response->success($result);
        }

        public function doDelete(){
            $ids = $this->request->input('ids');
            $result = $this->service->doDelete($ids);
            return $result ? $this->response->success($result) : $this->response->error('删除失败');
        }

        public function getProductFileVersion(): ResponseInterface
        {
            $id = $this->request->input('product_id',0);
            $result = $this->service->getProductFileVersion($id);
            return $this->response->success($result);
        }

        public function doEditProductFileVersion(): ResponseInterface
        {
            $id = $this->request->input('id',0);
            $params = $this->request->all();
            unset($params['id']);
            $result = $this->service->doEditProductFileVersion($id, $params);
            return $this->response->success($result);
        }

        public function doDeleteProductFileVersion()
        {
            $id = $this->request->input('id');
            $result = $this->service->doDeleteProductFileVersion($id);
            return $this->response->success($result);
        }
    }