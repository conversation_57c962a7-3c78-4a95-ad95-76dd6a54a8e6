<?php
    /**
     * @Copyright T-chip Team.
     * @Date 2025/05/27 16:27
     * <AUTHOR>
     * @Description
     */

    namespace App\Controller\Project\CheckList;

    use App\Core\Services\Project\CheckList\CheckListService;
    use App\Annotation\ControllerNameAnnotation;
    use App\Request\IdsRequest;
    use Psr\Http\Message\ResponseInterface;
    use Hyperf\Di\Annotation\Inject;
    use Hyperf\HttpServer\Annotation\AutoController;
    use Hyperf\HttpServer\Annotation\Middleware;
    use App\Middleware\AuthMiddleware;
    use App\Middleware\MenuMiddleware;

    /**
     * @ControllerNameAnnotation("检查清单")
     * @AutoController(prefix="/project/issue/checklist")
     */
    class CheckListController extends \App\Controller\BaseController
    {
        /**
         * @Inject()
         * @var CheckListService
         */
        protected $service;

        //////////////////////////////////////////////
        /// 清单模板相关接口
        
        /**
         * @ControllerNameAnnotation("模板-列表查询")
         */
        public function getTemplateList(): ResponseInterface
        {
            list($filter, $op, $sort, $order, $limit) = $this->getParams();
            $page = $this->request->input('page', 1);
            $result = $this->service->getTemplateList($filter, $op, $sort, $order, (int)$limit, $page);
            return $this->response->success($result);
        }
        
        /**
         * @ControllerNameAnnotation("模板-详情")
         */
        public function getTemplateDetail(): ResponseInterface
        {
            $templateId = (int)$this->request->input('id', 0);
            $result = $this->service->getTemplateDetail($templateId);
            return $this->response->success($result);
        }
        
        /**
         * @ControllerNameAnnotation("模板-新增/编辑")
         */
        public function saveTemplate(): ResponseInterface
        {
            $templateId = (int)$this->request->input('id', 0);
            $templateData = $this->request->input('template_data', []);
            $items = $this->request->input('items', []);
            $result = $this->service->saveTemplate($templateId, $templateData, $items);
            return $this->response->success($result);
        }
        
        /**
         * @ControllerNameAnnotation("模板-删除")
         */
        public function deleteTemplate(): ResponseInterface
        {
            $validated = make(IdsRequest::class)->validated();
            $ids = $validated['ids'];

            // 校验是否为空数组
            if (empty($ids) || !is_array($ids)) {
                return $this->response->error(  '请选择要删除的模板，传入ids数组', 422);
            }

            $result = true;
            foreach ($ids as $id) {
                $this->service->deleteTemplate($id);
            }
            return $this->response->success($result);
        }

        //////////////////////////////////////////////
        /// 检查清单相关接口
        
        /**
         * @ControllerNameAnnotation("检查清单-列表查询")
         */
        public function getChecklistList(): ResponseInterface
        {
            list($filter, $op, $sort, $order, $limit) = $this->getParams();
            $page = $this->request->input('page', 1);
            $result = $this->service->getChecklistList($filter, $op, $sort, $order, (int)$limit, $page);
            return $this->response->success($result);
        }
        
        /**
         * @ControllerNameAnnotation("检查清单-详情")
         */
        public function getChecklistDetail(): ResponseInterface
        {
            $checklistId = (int)$this->request->input('id', 0);
            $result = $this->service->getChecklistDetail($checklistId);
            return $this->response->success($result);
        }
        
        ///**
        // * @ControllerNameAnnotation("检查清单-从模板创建")
        // */
        //public function createChecklistFromTemplate(): ResponseInterface
        //{
        //    $templateId = (int)$this->request->input('template_id', 0);
        //    $checklistData = $this->request->input('checklist_data', []);
        //    $result = $this->service->createChecklistFromTemplate($templateId, $checklistData);
        //    return $this->response->success($result);
        //}
        //
        /**
         * @ControllerNameAnnotation("检查清单-新增/编辑")
         */
        public function saveChecklist(): ResponseInterface
        {
            $checklistId = (int)$this->request->input('id', 0);
            $checklistData = $this->request->input('checklist_data', []);
            $items = $this->request->input('items', []);
            $result = $this->service->saveChecklist($checklistId, $checklistData, $items);
            return $this->response->success($result);
        }
        
        /**
         * @ControllerNameAnnotation("检查清单-更新项目状态")
         */
        public function updateChecklistItemStatus(): ResponseInterface
        {
            $itemId = (int)$this->request->input('id', 0);
            $status = (int)$this->request->input('status', 0);
            $result = $this->service->updateChecklistItemStatus($itemId, $status);
            return $this->response->success($result);
        }
        
        /**
         * @ControllerNameAnnotation("检查清单-删除")
         */
        public function deleteChecklist(): ResponseInterface
        {
            $validated = make(IdsRequest::class)->validated();
            $ids = $validated['ids'];

            // 校验是否为空数组
            if (empty($ids) || !is_array($ids)) {
                return $this->response->error(  '请选择要删除的检查清单，传入ids数组', 422);
            }


            $result = true;
            foreach ($ids as $id) {
                $this->service->deleteChecklist($id);
            }
            return $this->response->success($result);
        }
        
        /**
         * @ControllerNameAnnotation("检查清单-更新项目排序")
         */
        public function updateChecklistItemsSort(): ResponseInterface
        {
            $checklistId = (int)$this->request->input('checklist_id', 0);
            $itemIds = $this->request->input('item_ids', []);
            
            // 校验参数
            if (empty($checklistId)) {
                return $this->response->error('检查清单ID不能为空', 422);
            }
            
            if (empty($itemIds) || !is_array($itemIds)) {
                return $this->response->error('项目ID数组不能为空', 422);
            }
            
            $result = $this->service->updateChecklistItemsSort($checklistId, $itemIds);
            return $this->response->success($result);
        }

        //////////////////////////////////////////////
        /// 字段设置相关接口
        
        /**
         * @ControllerNameAnnotation("字段设置-获取")
         */
        public function getFieldSettings(): ResponseInterface
        {
            list($filter, $op, $sort, $order, $limit) = $this->getParams();
            $page = $this->request->input('page', 1);
            $result = $this->service->getFieldSettings($filter, $op, $sort, $order, (int)$limit, $page);
            return $this->response->success($result);
        }
        
        /**
         * @ControllerNameAnnotation("字段设置-保存")
         */
        public function saveFieldSettings(): ResponseInterface
        {
            $settings = $this->request->input('settings', []);
            $result = $this->service->saveFieldSettings($settings);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("字段设置-删除")
         */
        public function deleteFieldSetting(): ResponseInterface
        {
            $settingIds = $this->request->input('ids', []);
            if (empty($settingIds) || !is_array($settingIds)) {
                return $this->response->error('请选择要删除的字段设置，传入ids数组', 422);
            }

            $result = true;
            foreach ($settingIds as $settingId) {
                $this->service->deleteFieldSetting($settingId);
            }
            return $this->response->success($result);
        }
        
        ///**
        // * @ControllerNameAnnotation("字段设置-初始化默认设置")
        // */
        //public function initDefaultFieldSettings(): ResponseInterface
        //{
        //    $projectId = $this->request->input('project_id');
        //    $projectId = $projectId !== null ? (int)$projectId : null;
        //    $result = $this->service->initDefaultFieldSettings($projectId);
        //    return $this->response->success($result);
        //}
    }