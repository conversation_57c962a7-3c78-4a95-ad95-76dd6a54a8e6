<?php
/**
 * @Copyright T-chip Team.
 * @Date 2022/9/7 上午10:12
 * <AUTHOR>
 * @Description
 */
namespace App\Controller\Project;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Project\CategoryService;
use App\Core\Services\Redmine\AttachmentService;
use App\Request\Project\Category\CategoryEditRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use App\Middleware\MenuMiddleware;
use Psr\Http\Message\ResponseInterface;

/**
 * @ControllerNameAnnotation("Redmin分类管理")
 * @AutoController(prefix="/project/category/index")
 * @Middleware(AuthMiddleware::class)
 */
class CategoryController extends \App\Controller\BaseController
{

    /**
     * @Inject()
     * @var CategoryService
     */
    protected $service;

    public function getList()
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->service->getList($filter, $op, $sort, $order, $limit);
        return $this->response->success($result);
    }

    public function tree()
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->service->getTree($filter, $op, $sort, $order, $limit);
        return $this->response->success($result);
    }

    public function lists()
    {
        $type = $this->request->input('type');
        if (!$type) {
            $this->response->error('type is not null');
        }
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $sort = $this->request->input('sort', 'sort');
        $order = $this->request->input('order', 'DESC');
        $result = $this->service->lists($type, $filter, $op, $sort, $order);
        return $this->response->success($result);
    }

    /**
     * @ControllerNameAnnotation("新增编辑")
     * @Middleware(MenuMiddleware::class)
     * @return ResponseInterface
     */
    public function doEdit(): ResponseInterface
    {
        $request = make(CategoryEditRequest::class);
        $request->validated();
        $id = (int) $this->request->input('id',0);
        $params = $this->request->all();
        unset($params['id']);
        $result = $this->service->doEdit($id, $params);
        return $this->response->success($result);
    }

    public function doMultiple()
    {
        $values = $this->request->input('values', []);
        $this->service->doMultiple($values);
        return $this->response->success();
    }

}