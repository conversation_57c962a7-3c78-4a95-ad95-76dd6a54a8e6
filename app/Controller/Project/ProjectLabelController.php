<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2025/2/27 上午10:44
 * <AUTHOR> Team
 * @Description
 *
 */

namespace App\Controller\Project;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Project\ProjectLabelService;
use App\Core\Services\Redmine\ProjectService as RedmineProjectService;
use App\Model\Redmine\ProjectModel;
use App\Model\TchipBi\UserModel;
use App\Request\Project\OverViewRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use Psr\Http\Message\ResponseInterface;

/**
 * @ControllerNameAnnotation("项目标签")
 * @AutoController(prefix="/project/projectLabel/index")
 */
class ProjectLabelController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var ProjectLabelService
     */
    protected $service;

    /**
     * @Inject()
     * @var RedmineProjectService
     */
    protected $redmineProjectService;

    public function getList()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getList($filter, $op, $sort, $order, (int) $limit, $search, $searchFields);
        return $this->response->success($result);
    }

    /**
     * 新增与编辑
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function doEdit()
    {
        $id = $this->request->input('id',0);
        $params = $this->request->all();
        unset($params['id']);
        $result = $this->service->doEdit($id, $params);
        return $this->response->success($result);
    }

    public function getLabelList()
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->service->getLabelList($filter, $op, $sort, $order, (int) $limit, );
        return $this->response->success($result);
    }

    public function doMapEdit()
    {
        $id = $this->request->input('id',0);
        $params = $this->request->all();
        unset($params['id']);
        $result = $this->service->doMapEdit($id, $params);
        return $this->response->success($result);
    }

    public function doMapDelete()
    {
        $ids = $this->request->input('ids');
        $result = $this->service->doMapDelete($ids);
        return $result ? $this->response->success($result) : $this->response->error('删除失败');
    }


}