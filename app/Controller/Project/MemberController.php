<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/29 下午5:04
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Project;

use App\Annotation\ControllerNameAnnotation;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use App\Core\Services\Project\MemberService;

/**
 * @ControllerNameAnnotation("项目成员")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class MemberController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var MemberService
     */
    protected $service;

    public function getList()
    {
        return parent::getList(); // TODO: Change the autogenerated stub
    }

    /**
     * @ControllerNameAnnotation("删除项目成员")
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function doDeleteByProjectUser()
    {
        $projectId = $this->request->input('project_id', 0);
        $userId = $this->request->input('user_id', 0);
        $result = $this->service->doDeleteByProjectUser($userId, $projectId);
        return $this->response->success($result);
    }

    /**
     * @ControllerNameAnnotation("添加项目成员")
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function doEditByProjectUser()
    {
        $id = $this->request->input('project_id',0);
        if (!$id) {
            return $this->response->error('project_id is missing');
        }
        $params = $this->request->all();
        unset($params['id']);
        $result = $this->service->doEditByProjectUser($id, $params);
        return $this->response->success($result);
    }
}