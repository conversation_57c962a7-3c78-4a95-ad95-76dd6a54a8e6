<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/7/4 下午5:21
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Project;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Project\ProjectsWatchersService;
use App\Request\Project\ProjectsWatchersRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * @ControllerNameAnnotation("项目关注管理")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class ProjectsWatchersController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var ProjectsWatchersService
     */
    protected $service;

    /**
     * @ControllerNameAnnotation("添加关注成员")
     */
    public function doWatchers(ProjectsWatchersRequest $projectsWatchersRequest)
    {
        $validated = $projectsWatchersRequest->validated();
        $result = $this->service->doWatchers($validated['project_id'], $validated['watcher']);
        return $this->response->success($result);
    }
}