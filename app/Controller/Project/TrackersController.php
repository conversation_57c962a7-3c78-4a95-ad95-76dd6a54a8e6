<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/7/4 下午5:21
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Project;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Project\TrackersService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * @ControllerNameAnnotation("事项类型管理")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class TrackersController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var TrackersService
     */
    protected $service;

    /**
     * @ControllerNameAnnotation("创建/编辑自定义事项类型")
     */
    public function doEdit()
    {
        // 获取参数
        $trackerId = $this->request->input('tracker_id', 0); // 0表示创建，>0表示编辑
        $projectId = $this->request->input('project_id');
        $params = $this->request->all();

        // 移除不需要的参数
        unset($params['tracker_id'], $params['project_id']);
        try {
            $result = $this->service->doEdit($trackerId, $params, $projectId);

            $message = $trackerId > 0 ? '事项类型更新成功' : '事项类型创建成功';
            return $this->response->success($result, $message);
        } catch (\Exception $e) {
            $action = $trackerId > 0 ? '更新' : '创建';
            return $this->response->error($action . '失败: ' . $e->getMessage());
        }
    }

    /**
     * @ControllerNameAnnotation("删除自定义事项类型")
     */
    public function doDelete()
    {
        $trackerId = $this->request->input('tracker_id');
        $projectId = $this->request->input('project_id');

        if (!$trackerId) {
            return $this->response->error('事项类型ID不能为空');
        }

        if (!$projectId) {
            return $this->response->error('项目ID不能为空');
        }

        try {
            $result = $this->service->deleteCustomTracker($trackerId, $projectId);
            return $this->response->success($result, '事项类型删除成功');
        } catch (\Exception $e) {
            return $this->response->error('删除失败: ' . $e->getMessage());
        }
    }

    /**
     * @ControllerNameAnnotation("检查事项类型是否可删除")
     */
    public function checkDeletable()
    {
        $trackerId = $this->request->input('tracker_id');
        $projectId = $this->request->input('project_id');

        if (!$trackerId) {
            return $this->response->error('事项类型ID不能为空');
        }

        if (!$projectId) {
            return $this->response->error('项目ID不能为空');
        }

        try {
            $result = $this->service->checkTrackerDeletable($trackerId, $projectId);
            return $this->response->success($result);
        } catch (\Exception $e) {
            return $this->response->error('检查失败: ' . $e->getMessage());
        }
    }
}