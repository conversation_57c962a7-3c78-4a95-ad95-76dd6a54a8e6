<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/21 下午4:51
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Setting;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Setting\UserManagerService;
use App\Model\TchipBi\UserModel;
use App\Request\User\UserIdRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use App\Middleware\MenuMiddleware;
use Psr\Http\Message\ResponseInterface;

/**
 * @ControllerNameAnnotation("用户管理")
 * @AutoController(prefix="/userManagement")
 * @Middleware(AuthMiddleware::class)
 */
class UserManagementController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var UserManagerService
     */
    protected $service;

    /**
     * @Middleware(MenuMiddleware::class)
     */
    public function initUserPassword()
    {
        $this->service->initUserPassword();
        return $this->response->success();
    }

    /**
     * 重置密码
     * @Middleware(AuthMiddleware::class)
     * @param UserIdRequest $request
     * @return ResponseInterface
     */
    public function initPassword(UserIdRequest $request): ResponseInterface
    {
        $validated = $request->validated();
        $userService = make(\App\Core\Services\UserService::class);
        $result    = $userService->initPassword($validated['user_id']);
        return $this->response->success($result, '初始化成功,请到邮箱查看密码');
    }

    public function getList()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getList($filter, $op, $sort, $order, (int) $limit, $search, $searchFields);
        return $this->response->success($result);
    }

    public function updatePersonInfo()
    {
        $id = (int) $this->request->input('id', 0);
        $params = $this->request->all();
        unset($params['id']);
        $result = $this->service->updatePersonInfo($id, $params);
        return $this->response->success($result);
    }

}