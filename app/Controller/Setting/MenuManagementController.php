<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/21 下午5:29
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Setting;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Setting\AuthMenuService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Psr\Http\Message\ResponseInterface;

/**
 * @ControllerNameAnnotation("菜单管理")
 * @AutoController(prefix="/menuManagement")
 */
class MenuManagementController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var AuthMenuService
     */
    protected $service;

    /**
     * @return ResponseInterface
     */
    public function getList()
    {
        $result['total'] = 999;
        $result['list'] = [
            [
                'id'       => 'root',
                'label'    => '全部角色',
                'children' => [
                    [
                        'id'    => '@id',
                        'role'  => 'admin',
                        'label' => 'admin角色',
                    ],
                    [
                        'id'    => '@id',
                        'role'  => 'editor',
                        'label' => 'editor角色',
                    ],
                ],
            ],
        ];
        return $this->response->success($result);
    }

    /**
     * @return ResponseInterface
     */
    public function doEdit()
    {
        $params = $this->request->all();
        $id = $this->request->input('id', 0);
        $result = $this->service->doEdit($id, $params);
        return $this->response->success($result);
    }
}