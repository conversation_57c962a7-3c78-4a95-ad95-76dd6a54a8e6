<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/21 下午5:29
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Setting;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Setting\ConfigService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * @ControllerNameAnnotation("配置管理")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class ConfigController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var ConfigService
     */
    protected $service;

    public function getSettingValue()
    {
        $name = $this->request->input('name');
        $type = $this->request->input('value_type', null);
        $result = $this->service->statusList($name, $type);
        return $this->response->success($result);
    }

    public function doEditByName()
    {
        $name = $this->request->input('name');
        $values = $this->request->input('values', null);
        $result = $this->service->doEditByName($name, $values);
        return $this->response->success($result);
    }
}