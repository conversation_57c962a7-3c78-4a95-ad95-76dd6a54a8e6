<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/21 下午4:51
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Setting;

use App\Annotation\ControllerNameAnnotation;
use App\Model\TchipBi\UserModel;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use App\Middleware\MenuMiddleware;

/**
 * @ControllerNameAnnotation("用户消息订阅管理")
 * @AutoController(prefix="/userNoticeSubscribe")
 * @Middleware(AuthMiddleware::class)
 */
class UserNoticeSubscribeController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var \App\Core\Services\Notice\UserNoticeSubscribeService
     */
    protected $service;

    public function doMultiSave()
    {
        $params = $this->request->all();
        $result = $this->service->doMultiSave($params);
        return $this->response->success($result);
    }

    public function getList()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getList($filter, $op, $sort, $order, (int) $limit, $search, $searchFields);
        return $this->response->success($result);
    }

}