<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2023/4/15 上午10:56
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Setting;

use App\Annotation\ControllerNameAnnotation;
use App\Middleware\AuthMiddleware;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;

/**
 * @ControllerNameAnnotation("消息通知管理")
 * @AutoController(prefix="/noticeManagement")
 * @Middleware(AuthMiddleware::class)
 */
class NoticeManagementController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var \App\Core\Services\Notice\NoticeService
     */
    protected $service;

    public function sendNow()
    {
        $method = $this->request->input('method');
        $params = $this->request->input('params');
        if (method_exists($this->service, $method)) {
            if ($params) {
                call_user_func([$this->service, $method], ...$params);
            } else {
                call_user_func([$this->service, $method]);
            }
            return $this->response->success();
        }
        return $this->response->error('method not exist');
    }

    public function getList()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getList($filter, $op, $sort, $order, (int) $limit, $search, $searchFields);
        return $this->response->success($result);
    }

    /**
     * 获取所有消息通知
     * 包含人员、部门、角色。部门和角色会筛选出用户的id
     */
    public function getListNew()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getListNew($filter, $op, $sort, $order, (int) $limit, $search, $searchFields);
        return $this->response->success($result);
    }
}