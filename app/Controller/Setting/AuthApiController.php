<?php
/**
 * @Copyright T-chip Team.
 * @Date 2023/4/4 下午2:40
 * <AUTHOR> X
 * @Description
 */

namespace App\Controller\Setting;

use App\Core\Services\Setting\AuthApiService;
use App\Middleware\AuthMiddleware;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\MenuMiddleware;
use App\Annotation\ControllerNameAnnotation;

/**
 * @ControllerNameAnnotation("接口管理")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class AuthApiController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var AuthApiService
     */
    protected $service;

    /**
     * 获取系统中所有控制器
     * @return void
     */
    public function getControllerList()
    {
        $result = $this->service->controllerList();
        return $this->response->success($result);
    }

    /**
     * @ControllerNameAnnotation("一键生成API")
     * @Middleware(MenuMiddleware::class)
     */
    public function createApis()
    {
        $controllers = $this->request->input('controllers');
        $result = $this->service->createApis($controllers);
        return $this->response->success($result);
    }

    /**
     * @ControllerNameAnnotation("删除API")
     * @Middleware(MenuMiddleware::class)
     */
    public function doDelete()
    {
        $ids = $this->request->input('ids');
        $result = $this->service->delete($ids);
        return $this->response->success($result);
    }
}