<?php
/*
 * @Description: 消息通知分类管理
 * @Version: 1.0
 * @Autor: 张权江
 * @Date: 2025-04-29 09:52:42
 * @LastEditors: 张权江
 * @LastEditTime: 2025-04-29 09:54:57
 */

namespace App\Controller\Setting;

use App\Annotation\ControllerNameAnnotation;
use App\Middleware\AuthMiddleware;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;

/**
 * @ControllerNameAnnotation("消息通知分类管理")
 * @AutoController(prefix="/noticeCategoryManagement")
 * @Middleware(AuthMiddleware::class)
 */
class NoticeCategoryController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var \App\Core\Services\Notice\NoticeCategoryService
     */
    protected $service;
}