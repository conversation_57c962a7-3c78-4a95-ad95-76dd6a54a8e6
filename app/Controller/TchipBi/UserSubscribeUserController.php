<?php
declare(strict_types=1);
namespace App\Controller\TchipBi;

use App\Core\Services\TchipBi\UserSubscribeUserService;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\Di\Annotation\Inject;
use App\Request\TchipBi\UserSubscribeUser\DoSubscribeRequest;

/**
 * @AutoController()
 */
class UserSubscribeUserController extends \App\Controller\BaseController
{
    /**
     * @Inject
     * @var UserSubscribeUserService
     */
    public $service;

    public function getSubscribeList()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getSubscribeList($filter, $op, $sort, $order, (int) $limit);
        return $this->response->success($result);
    }

    public function doSubscribe(DoSubscribeRequest $request)
    {
        $items = $request->validated();
        return $this->response->success($this->service->doSubscribe($items['user_id'], $items['subscribed_user_id'], $items['module'], (int) $items['action'], $items['type'] ?? 'subscribe'));
    }

}
