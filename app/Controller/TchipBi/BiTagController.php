<?php
    declare(strict_types=1);
    namespace App\Controller\TchipBi;

    use App\Core\Services\TchipBi\BiTagService;
    use App\Request\IdsRequest;
    use Hyperf\HttpServer\Annotation\Middleware;
    use Hyperf\HttpServer\Annotation\AutoController;
    use Hyperf\Di\Annotation\Inject;
    use App\Annotation\ControllerNameAnnotation;
    use App\Middleware\AuthMiddleware;
    use App\Middleware\MenuMiddleware;
    
    /**
     * @ControllerNameAnnotation("BiTag")
     * @AutoController(prefix="/bi_tag/index")
     * @Middleware(AuthMiddleware::class)
     * @Middleware(MenuMiddleware::class)
     */
    class BiTagController extends \App\Controller\BaseController
    {
        /**
         * @Inject
         * @var BiTagService
         */
        public $service;

        /**
         * @ControllerNameAnnotation("BiTag-列表查询")
         */
        public function getList()
        {
            list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
            $result = $this->service->getList($filter, $op, $sort, $order, (int) $limit, $search, $searchFields);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("BiTag-新增/编辑")
         */
        public function doEdit()
        {
            $id = (int) $this->request->input('id', 0);
            $params = $this->request->all();
            unset($params['id']);
            $result = $this->service->doEdit($id, $params);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("BiTag-删除")
         */
        public function doDelete()
        {
            $validated = make(IdsRequest::class)->validated();
            $result = $this->service->doDelete($validated['ids']);
            return $this->response->success($result);
        }



    }
