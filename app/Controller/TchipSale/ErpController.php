<?php
/**
 * @Copyright T-chip Team.
 * @Date 2023/5/9 下午5:18
 * <AUTHOR>
 * @Description
 */


namespace App\Controller\TchipSale;

use App\Annotation\ControllerNameAnnotation;
use App\Constants\StatusCode;
use App\Core\Services\TchipSale\ErpService;
use App\Exception\AppException;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;


/**
 * @ControllerNameAnnotation("ERP管理")
 * @AutoController()
 */
class ErpController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var ErpService
     */
    protected $service;

    public function getGoodsByName()
    {
        $name = $this->request->input('name');
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        if (!$name) {
            throw new AppException(StatusCode::ERR_SERVER, 'param name is required');
        }
        $result = $this->service->getGoodsByName($name, $filter, $op);
        return $this->response->success($result);
    }

    public function getGoodsByMaterial()
    {
        $material = $this->request->input('material');
        if (!$material) {
            throw new AppException(StatusCode::ERR_SERVER, 'param name is required');
        }
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getGoodsByMaterial($material, $filter, $op);
        return $this->response->success($result);
    }

    public function getGoodsByCodes()
    {
        $material = $this->request->input('material');
        if (!$material) {
            throw new AppException(StatusCode::ERR_SERVER, 'param name is required');
        }
        $material = !is_array($material) ? explode(',', $material) : $material;
        $result = $this->service->getGoodsByCodes($material);
        return $this->response->success($result);
    }

    public function getGoodsBorrowDetails()
    {
        $material = $this->request->input('material');
        if (!$material) {
            throw new AppException(StatusCode::ERR_SERVER, 'param name is required');
        }
        $result = $this->service->getGoodsBorrowDetails($material);
        return $this->response->success($result);
    }
}