<?php
/**
 * @Copyright T-chip Team.
 * @Date 2023/5/9 下午5:18
 * <AUTHOR>
 * @Description
 */


namespace App\Controller\TchipSale\Erp;

use App\Annotation\ControllerNameAnnotation;
use App\Constants\StatusCode;
use App\Core\Services\TchipSale\Erp\OuthelpCreatehService;
use App\Exception\AppException;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;

class ErpBaseController extends \App\Controller\BaseController
{
    public function overView()
    {
        $id = $this->request->input('id');
        $filter = $this->request->input('filter', []);
        $op = $this->request->input('op', []);
        return $this->response->success($this->service->overView($id, $filter, $op));
    }
}