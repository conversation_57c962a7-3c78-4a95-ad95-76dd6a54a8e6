<?php
/**
 * @Copyright T-chip Team.
 * @Date 2023/5/9 下午5:18
 * <AUTHOR>
 * @Description
 */


namespace App\Controller\TchipSale\Erp;

use App\Annotation\ControllerNameAnnotation;
use App\Constants\StatusCode;
use App\Core\Services\TchipSale\Erp\StockGoodsService;
use App\Exception\AppException;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;


/**
 * @ControllerNameAnnotation("ERP管理")
 * @AutoController()
 */
class StockGoodsController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var StockGoodsService
     */
    protected $service;

    public function getList()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        if (empty($filter['SPEC'])) {

        }
        $result = $this->service->getPage($filter, $op, $sort, $order);
        return $this->response->success($result);
    }

}