<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/22 上午10:12
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\TchipSale;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\TchipSale\OrderService;
use App\Request\Project\OverViewRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;

/**
 * @ControllerNameAnnotation("销售订单管理")
 * @AutoController()
 */
class OrderController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var OrderService
     */
    protected $service;

    public function orderMsgToUser()
    {
        $userId = $this->request->input('user_id', '');
        $result = $this->service->orderMsgToUser($userId);
        return $this->response->success($result);
    }
}