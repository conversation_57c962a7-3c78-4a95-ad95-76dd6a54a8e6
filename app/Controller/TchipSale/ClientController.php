<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/22 上午10:12
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\TchipSale;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\TchipSale\ClientService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;

/**
 * @ControllerNameAnnotation("销售客户管理")
 * @AutoController()
 */
class ClientController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var ClientService
     */
    protected $service;

    public function getList()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getList($filter, $op, $sort, $order, (int) $limit, $search, $searchFields);
        return $this->response->success($result);
    }

}