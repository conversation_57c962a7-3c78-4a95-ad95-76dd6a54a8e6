<?php
/*
 * @Description: 天启文化控制器
 * @Version: 1.0
 * @Autor: 张权江
 * @Date: 2025-03-18 10:53:03
 * @LastEditors: 张权江
 * @LastEditTime: 2025-04-03 09:35:42
 */

namespace App\Controller\TchipCulture;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\TchipCulture\CultureService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use App\Middleware\MenuMiddleware;

/**
 * @ControllerNameAnnotation("文化管理")
 * @Middleware(AuthMiddleware::class)
 * @AutoController()
 */
class CultureController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var CultureService
     */
    protected $service;

    /**
     * @ControllerNameAnnotation("获取人事文档列表")
     * @Middleware(MenuMiddleware::class)
     */
    public function getCultureList()
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        
        return $this->response->success($this->service->getList($filter, $op, 'published_at', 'DESC', $limit));
    }

    /**
     * @ControllerNameAnnotation("获取人事文档详情")
     * @Middleware(MenuMiddleware::class)
     */
    public function getCultureDetail()
    {
        $id = $this->request->input('id', 0);
        
        return $this->response->success($this->service->getDetail($id));
    }

    /**
     * @ControllerNameAnnotation("新增人事文档")
     * @Middleware(MenuMiddleware::class)
     */
    public function saveCulture()
    {
        $id = $this->request->input('id', 0);
        $data = $this->request->input('data', []);
        
        return $this->response->success($this->service->doEdit($id, $data));
    }

    /**
     * @ControllerNameAnnotation("删除人事文档")
     * @Middleware(MenuMiddleware::class)
     */
    public function deleteCulture()
    {
        $ids = $this->request->input('id');
        return $this->response->success($this->service->doDelete($ids));
    }

    /**
     * @ControllerNameAnnotation("新人报道模块增加浏览量")
     */
    public function incrementViews()
    {
        $id = $this->request->input('id', 0);
        return $this->response->success($this->service->incrementViews($id));
    }

    /**
     * @ControllerNameAnnotation("获取展示的人事文档")
     */
    public function getFrontCultureList()
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        return $this->response->success($this->service->getList($filter, $op, 'published_at', 'DESC', $limit, true));
    }

    /**
     * @ControllerNameAnnotation("获取员工档案列表")
     */
    public function getUserFilesList()
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        return $this->response->success($this->service->getUserFiles($filter, $op, 'entry_date', 'DESC', $limit));
    }

    
}