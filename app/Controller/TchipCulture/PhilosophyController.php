<?php
/*
 * @Description: 公司理念控制器
 * @Version: 1.0
 * @Autor: 张权江
 * @Date: 2025-03-22 09:30:05
 * @LastEditors: 张权江
 * @LastEditTime: 2025-03-26 10:10:37
 */

namespace App\Controller\TchipCulture;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\TchipCulture\PhilosophyService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use App\Middleware\MenuMiddleware;

/**
 * @ControllerNameAnnotation("公司理念管理")
 * @Middleware(AuthMiddleware::class)
 * @AutoController()
 */
class PhilosophyController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var PhilosophyService
     */
    protected $service;

    /**
     * @ControllerNameAnnotation("获取理念详情")
     */
    public function getPhilosophyDetail()
    {
        $id = $this->request->input('id', 0);
        
        return $this->response->success($this->service->getDetail($id));
    }

    /**
     * @ControllerNameAnnotation("修改公司理念")
     * @Middleware(MenuMiddleware::class)
     */
    public function savePhilosophy()
    {
        $id = $this->request->input('id', 0);
        $data = $this->request->input('data', []);
        
        return $this->response->success($this->service->save($id, $data));
    }

    /**
     * @ControllerNameAnnotation("获取历史记录")
     * @Middleware(MenuMiddleware::class)
     */
    public function getPhilosophyHistory()
    {
        $id = $this->request->input('id', 0);
        
        return $this->response->success($this->service->getHistory($id));
    }
}