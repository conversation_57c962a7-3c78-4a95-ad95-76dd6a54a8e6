<?php

declare(strict_types=1);

namespace App\Controller\Fms;

use App\Controller\BaseController;
use App\Core\Services\Fms\FileService;
use App\Exception\AppException;
use App\Constants\StatusCode;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\HttpMessage\Upload\UploadedFile;

/**
 * FMS文件控制器
 *
 * @Controller(prefix="/api/fms/files")
 */
class FileController extends BaseController
{
    /**
     * @Inject()
     * @var FileService
     */
    protected $fileService;

    /**
     * 获取文件列表
     *
     * @RequestMapping(path="", methods="GET")
     */
    public function index(RequestInterface $request)
    {
        try {
            $params = $this->getFileParams($request, [
                'directory_id' => 'integer|nullable',
                'search' => 'string|nullable|max:255',
                'page' => 'integer|min:1',
                'page_size' => 'integer|min:1|max:100',
            ]);

            $result = $this->fileService->getFileList(
                $params['directory_id'] ?? null,
                $params['search'] ?? '',
                $params['page'] ?? 1,
                $params['page_size'] ?? 20
            );

            return $this->response->success($result);
        } catch (AppException $e) {
            return $this->response->error($e->getMessage(), $e->getCode());
        } catch (\Exception $e) {
            return $this->response->error('获取文件列表失败', StatusCode::ERR_SERVER);
        }
    }

    /**
     * 上传文件
     *
     * @RequestMapping(path="", methods="POST")
     */
    public function upload(RequestInterface $request)
    {
        try {
            $params = $this->getFileParams($request, [
                'directory_id' => 'required|integer',
                'name' => 'string|max:255',
                'description' => 'string|nullable|max:1000',
                'visibility' => 'string|in:public,department,user,private',
            ]);


            $uploadedFile = $request->file('file');
            if (!$uploadedFile instanceof UploadedFile) {
                return $this->response->error('请选择要上传的文件', StatusCode::ERR_SERVER);
            }

            $file = $this->fileService->uploadFile($uploadedFile, $params['directory_id'], $params);

            return $this->response->success($file, '文件上传成功');
        } catch (AppException $e) {
            return $this->response->error($e->getMessage(), $e->getCode());
        } catch (\Exception $e) {
            return $this->response->error('文件上传失败:' . $e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

    /**
     * 获取文件详情
     *
     * @RequestMapping(path="{id:[0-9]+}", methods="GET")
     */
    public function show(RequestInterface $request, int $id)
    {
        try {
            $file = $this->fileService->getOverView($id);

            if (!$file) {
                return $this->response->error('文件不存在', StatusCode::ERR_SERVER);
            }

            return $this->response->success($file);
        } catch (AppException $e) {
            return $this->response->error($e->getMessage(), $e->getCode());
        } catch (\Exception $e) {
            return $this->response->error('获取文件详情失败', StatusCode::ERR_SERVER);
        }
    }

    /**
     * 更新文件信息
     *
     * @RequestMapping(path="{id:[0-9]+}", methods="PUT")
     */
    public function update(RequestInterface $request, int $id)
    {
        try {
            $params = $this->getFileParams($request, [
                'name' => 'string|max:255',
                'description' => 'string|nullable|max:1000',
                'visibility' => 'string|in:public,department,user,private',
            ]);

            $file = $this->fileService->updateFile($id, $params);

            return $this->response->success($file, '文件信息更新成功');
        } catch (AppException $e) {
            return $this->response->error($e->getMessage(), $e->getCode());
        } catch (\Exception $e) {
            return $this->response->error('更新文件信息失败', StatusCode::ERR_SERVER);
        }
    }

    /**
     * 删除文件
     *
     * @RequestMapping(path="{id:[0-9]+}", methods="DELETE")
     */
    public function delete(RequestInterface $request, int $id)
    {
        try {
            $result = $this->fileService->deleteFile($id);

            if ($result) {
                return $this->response->success(null, '文件删除成功');
            } else {
                return $this->response->error('删除文件失败', StatusCode::ERR_SERVER);
            }
        } catch (AppException $e) {
            return $this->response->error($e->getMessage(), $e->getCode());
        } catch (\Exception $e) {
            return $this->response->error('删除文件失败', StatusCode::ERR_SERVER);
        }
    }

    /**
     * 下载文件
     *
     * @RequestMapping(path="{id:[0-9]+}/download", methods="GET")
     */
    public function download(RequestInterface $request, int $id)
    {
        try {
            $fileInfo = $this->fileService->downloadFile($id);

            return $this->response->download($fileInfo['file_path'], $fileInfo['original_name']);
        } catch (AppException $e) {
            return $this->response->error($e->getMessage(), $e->getCode());
        } catch (\Exception $e) {
            return $this->response->error('文件下载失败', StatusCode::ERR_SERVER);
        }
    }

    /**
     * 移动文件
     *
     * @RequestMapping(path="{id:[0-9]+}/move", methods="POST")
     */
    public function move(RequestInterface $request, int $id)
    {
        try {
            $params = $this->getFileParams($request, [
                'directory_id' => 'required|integer',
            ]);

            $file = $this->fileService->moveFile($id, $params['directory_id']);

            return $this->response->success($file, '文件移动成功');
        } catch (AppException $e) {
            return $this->response->error($e->getMessage(), $e->getCode());
        } catch (\Exception $e) {
            return $this->response->error('移动文件失败', StatusCode::ERR_SERVER);
        }
    }

    /**
     * 复制文件
     *
     * @RequestMapping(path="{id:[0-9]+}/copy", methods="POST")
     */
    public function copy(RequestInterface $request, int $id)
    {
        try {
            $params = $this->getFileParams($request, [
                'directory_id' => 'required|integer',
                'name' => 'string|max:255',
            ]);

            $file = $this->fileService->copyFile(
                $id,
                $params['directory_id'],
                $params['name'] ?? null
            );

            return $this->response->success($file, '文件复制成功');
        } catch (AppException $e) {
            return $this->response->error($e->getMessage(), $e->getCode());
        } catch (\Exception $e) {
            return $this->response->error('复制文件失败', StatusCode::ERR_SERVER);
        }
    }



    /**
     * 获取参数并验证
     */
    protected function getFileParams(RequestInterface $request, array $rules = []): array
    {
        $params = $request->all();


        if (!empty($rules)) {
            $validator = $this->validatorFactory->make($params, $rules);
            if ($validator->fails()) {
                throw new AppException(StatusCode::ERR_SERVER, $validator->errors()->first());
            }
        }

        // 类型转换
        $castedParams = [];
        foreach ($params as $key => $value) {
            if (in_array($key, ['page', 'page_size', 'directory_id']) && is_string($value) && is_numeric($value)) {
                $castedParams[$key] = (int) $value; // 转换为 int
            }
            else {
                $castedParams[$key] = $value;
            }
        }

        return $castedParams;
    }
}