<?php

declare(strict_types=1);

namespace App\Controller\Fms;

use App\Controller\BaseController;
use App\Core\Services\Fms\DirectoryService;
use App\Exception\AppException;
use App\Constants\StatusCode;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Contract\RequestInterface;

/**
 * FMS目录控制器
 *
 * @Controller(prefix="/api/fms/directories")
 */
class DirectoryController extends BaseController
{
    /**
     * @Inject()
     * @var DirectoryService
     */
    protected $directoryService;

    /**
     * 获取目录列表
     *
     * @RequestMapping(path="", methods="GET")
     */
    public function index(RequestInterface $request)
    {
        try {
            $params = $this->getFileParams($request, [
                'parent_id' => 'integer|nullable',
                'search' => 'string|nullable|max:255',
                'page' => 'integer|min:1',
                'page_size' => 'integer|min:1|max:100',
                'union' => 'integer|nullable',
            ]);


            $result = $this->directoryService->getDirectoryList(
                $params['parent_id'] ?? null,
                $params['search'] ?? '',
                $params['page'] ?? 1,
                $params['page_size'] ?? 99999,
                $params['union'] ?? 0
            );

            return $this->response->success($result);
        } catch (AppException $e) {
            return $this->response->error($e->getMessage(), $e->getCode());
        } catch (\Exception $e) {
            return $this->response->error('获取目录列表失败' . $e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

    /**
     * 获取目录树
     *
     * @RequestMapping(path="tree", methods="GET")
     */
    public function getDirectoryTree(RequestInterface $request)
    {
        try {
            $params = $this->getfileParams($request, [
                'parent_id' => 'integer|nullable',
                'max_depth' => 'integer|min:1|max:10',
            ]);

            $tree = $this->directoryService->getDirectoryTreeOptimized(
                $params['parent_id'] ?? null,
                $params['max_depth'] ?? 10
            );

            return $this->response->success($tree);
        } catch (AppException $e) {
            return $this->response->error($e->getMessage(), $e->getCode());
        } catch (\Exception $e) {
            return $this->response->error('获取目录树失败'.$e->getMessage(), StatusCode::ERR_SERVER);
        }
    }


    /**
     * 创建目录
     *
     * @RequestMapping(path="", methods="POST")
     */
    public function create(RequestInterface $request)
    {
        try {
            $params = $this->getfileParams($request, [
                'parent_id' => 'integer|nullable',
                'name' => 'required|string|max:255',
                'description' => 'string|nullable|max:1000',
                'visibility' => 'string|in:public,department,user,private',
                'sort_order' => 'integer|min:0',
            ]);

            $directory = $this->directoryService->createDirectory($params);

            return $this->response->success($directory, '目录创建成功');
        } catch (AppException $e) {
            return $this->response->error($e->getMessage(), $e->getCode());
        } catch (\Exception $e) {
            return $this->response->error('创建目录失败', StatusCode::ERR_SERVER);
        }
    }

    /**
     * 获取目录详情
     *
     * @RequestMapping(path="{id:[0-9]+}", methods="GET")
     */
    public function getOverView(RequestInterface $request, int $id)
    {
        try {
            $directory = $this->directoryService->getOverView($id);

            if (!$directory) {
                return $this->response->error('目录不存在', StatusCode::ERR_SERVER);
            }

            return $this->response->success($directory);
        } catch (AppException $e) {
            return $this->response->error($e->getMessage(), $e->getCode());
        } catch (\Exception $e) {
            return $this->response->error('获取目录详情失败'.$e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

    /**
     * 更新目录
     *
     * @RequestMapping(path="{id:[0-9]+}", methods="PUT")
     */
    public function update(RequestInterface $request, int $id)
    {
        try {
            $params = $this->getfileParams($request, [
                'name' => 'string|max:255',
                'description' => 'string|nullable|max:1000',
                'visibility' => 'string|in:public,department,user,private',
                'sort_order' => 'integer|min:0',
            ]);

            $directory = $this->directoryService->updateDirectory($id, $params);

            return $this->response->success($directory, '目录更新成功');
        } catch (AppException $e) {
            return $this->response->error($e->getMessage(), $e->getCode());
        } catch (\Exception $e) {
            return $this->response->error('更新目录失败:' . $e->getMessage(), StatusCode::ERR_SERVER);
        }
    }

    /**
     * 删除目录
     *
     * @RequestMapping(path="{id:[0-9]+}", methods="DELETE")
     */
    public function delete(RequestInterface $request, int $id)
    {
        try {
            $result = $this->directoryService->deleteDirectory($id);

            if ($result) {
                return $this->response->success(null, '目录删除成功');
            } else {
                return $this->response->error('删除目录失败', StatusCode::ERR_SERVER);
            }
        } catch (AppException $e) {
            return $this->response->error($e->getMessage(), $e->getCode());
        } catch (\Exception $e) {
            return $this->response->error('删除目录失败', StatusCode::ERR_SERVER);
        }
    }

    /**
     * 移动目录
     *
     * @RequestMapping(path="{id:[0-9]+}/move", methods="POST")
     */
    public function move(RequestInterface $request, int $id)
    {
        try {
            $params = $this->getfileParams($request, [
                'parent_id' => 'integer|nullable',
            ]);

            $directory = $this->directoryService->moveDirectory($id, $params['parent_id'] ?? null);

            return $this->response->success($directory, '目录移动成功');
        } catch (AppException $e) {
            return $this->response->error($e->getMessage(), $e->getCode());
        } catch (\Exception $e) {
            return $this->response->error('移动目录失败', StatusCode::ERR_SERVER);
        }
    }

    /**
     * 获取参数并验证
     */
    protected function getfileParams(RequestInterface $request, array $rules = []): array
    {
        $params = $request->all();

        if (!empty($rules)) {
            $validator = $this->validatorFactory->make($params, $rules);
            if ($validator->fails()) {
                throw new AppException(StatusCode::ERR_SERVER, $validator->errors()->first());
            }
        }

        // 类型转换
        $castedParams = [];
        foreach ($params as $key => $value) {
            if (in_array($key, ['page', 'page_size', 'parent_id', 'union']) && is_string($value) && is_numeric($value)) {
                $castedParams[$key] = (int) $value; // 转换为 int
            } else {
                $castedParams[$key] = $value;
            }
        }

        return $castedParams;
    }
}