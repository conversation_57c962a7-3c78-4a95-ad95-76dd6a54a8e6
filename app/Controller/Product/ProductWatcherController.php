<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/7/4 下午5:21
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Product;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Product\ProductWatcheService;
use App\Request\Product\ProductWatcherRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * @ControllerNameAnnotation("产品关注")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class ProductWatcherController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var ProductWatcheService
     */
    protected $service;

    public function doWatcher(ProductWatcherRequest $productWatcherRequest)
    {
        $validated = $productWatcherRequest->validated();
        $result = $this->service->doWatcher($validated['product_id'], $validated['watcher']);
        return $this->response->success($result);
    }
}