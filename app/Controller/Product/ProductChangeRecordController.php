<?php

namespace App\Controller\Product;

use App\Annotation\ControllerNameAnnotation;
use App\Controller\BaseController;
use App\Core\Services\Product\ProductChangeMailService;
use App\Core\Services\Product\ProductChangeRecordService;
use App\Core\Services\TchipSale\Erp\StockGoodsService;
use App\Middleware\AuthMiddleware;
use App\Middleware\MenuMiddleware;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;

/**
 * @ControllerNameAnnotation("产品变更记录")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class ProductChangeRecordController extends BaseController
{
    /**
     * @Inject()
     * @var ProductChangeRecordService
     */
    protected $service;

    public function conf()
    {
        $data = $this->service->conf();
        return $this->response->success($data);
    }
    /**
     * @Middleware(MenuMiddleware::class)
     */
    public function getList()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $groupMode = $this->request->input('group_mode', 0);
        $result = $this->service->getList($filter, $op, $sort, $order, (int) $limit, $groupMode);
        return $this->response->success($result);
    }
    /**
     * @ControllerNameAnnotation("发送邮件通知")
     * @Middleware(MenuMiddleware::class)
     */
    public function sendMail()
    {
        $params = $this->request->all();
        $result = make(ProductChangeMailService::class)->sendMail($params);
        return $this->response->success($result);
    }

    /**
     * 获取邮件发送历史
     */
    public function getMailHistory()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = make(ProductChangeMailService::class)->getMailHistory($filter, $op, $sort, $order, (int)$limit, $search, $searchFields);
        return $this->response->success($result);
    }

    /**
     * 获取ERP产品列表
     */

    public function getErpGoods()
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->service->getErpGoods($filter, $op, $sort, $order, (int)$limit);
        return $this->response->success($result);
    }


    /**
     * 获取Sale客户列表
     */
    public function getSaleClient()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getSaleClient($filter, $op, $sort, $order, (int)$limit, $search, $searchFields);
        return $this->response->success($result);
    }
}
