<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/10/26 下午5:24
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Product;

use App\Annotation\ControllerNameAnnotation;
use App\Controller\BaseController;
use App\Core\Services\Product\ProductMilestoneService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * @ControllerNameAnnotation("产品里程碑")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class ProductMilestoneController extends BaseController
{
    /**
     * @var ProductMilestoneService
     * @Inject()
     */
    protected $service;
}