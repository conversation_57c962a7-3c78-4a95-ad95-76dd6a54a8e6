<?php
/*
 * @Description: 测试计划报告控制器
 * @Version: 1.0
 * @Autor: 张权江
 * @Date: 2025-03-21 09:42:35
 * @LastEditors: 张权江
 * @LastEditTime: 2025-04-03 15:46:28
 */

namespace App\Controller\TestPlan;

use App\Annotation\ControllerNameAnnotation;
use App\Controller\BaseController;
use App\Core\Services\TestPlan\TestPlanReportService;
use App\Middleware\AuthMiddleware;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;

/**
 * @ControllerNameAnnotation("测试计划")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class PlanReportController extends BaseController
{
    /**
     * @var TestPlanReportService
     * @Inject()
     */
    protected $service;

    /**
     * @ControllerNameAnnotation("获取测试报告")
     */
    public function getReport()
    {
        $id = $this->request->input('id', 0);
        $result = $this->service->getReport($id);
        return $this->response->success($result);
    }

    /**
     * @ControllerNameAnnotation("保存测试结论")
     */
    public function saveTestConclusion()
    {
        $id = $this->request->input('id', 0);
        $conclusion = $this->request->input('conclusion', '');
        $result = $this->service->saveTestConclusion($id, $conclusion);
        return $this->response->success($result);
    }
}
