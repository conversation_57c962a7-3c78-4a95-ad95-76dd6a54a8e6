<?php

namespace App\Controller\Production;

use App\Annotation\ControllerNameAnnotation;
use App\Constants\ProductionCode;
use App\Controller\BaseController;
use App\Core\Services\MacAddress\MacAddressService;
use App\Middleware\AuthMiddleware;
use App\Request\Production\MacAddress\AmountRequest;
use App\Request\Production\MacAddress\IdRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\MenuMiddleware;

/**
 * @ControllerNameAnnotation("MAC地址管理")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class MacAddressController extends BaseController
{
    /**
     * @Inject()
     * @var MacAddressService
     */
    protected $service;

    /**
     * @ControllerNameAnnotation("获取被创建最后一个MAC地址")
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function getLastMac()
    {
        return $this->response->success($this->service->getLastMac());
    }

    /**
     * @ControllerNameAnnotation("批量创建MAC地址")
     * @Middleware(MenuMiddleware::class)
     * @param AmountRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function createMac(AmountRequest $request)
    {
        $validated = $request->validated();
        $params = $this->request->all();
        if (!empty($params['amount'])) {
            unset($params['amount']);
        }
        return $this->response->success($this->service->createMac($validated['amount'], $params));
    }

    /**
     * @ControllerNameAnnotation("导出该订单MAC地址")
     * @Middleware(MenuMiddleware::class)
     * @param IdRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function factoryProductionExport(IdRequest $request)
    {
        $validated = $request->validated();
        $params = $this->request->all();
        $type = $params['type']??ProductionCode::CODE_USED_TYPE_PRODUCTION;
        return $this->response->success($this->service->factoryProductionExport($validated['id'],$type));
    }

    public function conf()
    {
        $conf = $this->service->conf();
        return $this->response->success($conf);
    }

    /**
     * @ControllerNameAnnotation("导出")
     * @Middleware(AuthMiddleware::class)
     */
    public function export()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->export($filter, $op, $sort, $order);
        return $result;
    }
}