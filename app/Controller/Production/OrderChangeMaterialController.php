<?php

namespace App\Controller\Production;

use App\Annotation\ControllerNameAnnotation;
use App\Controller\BaseController;
use App\Core\Services\ProductionOrder\ProductionOrderChangeMaterialService;
use App\Middleware\AuthMiddleware;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\MenuMiddleware;

/**
 * @ControllerNameAnnotation("生产订单变更物料")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class OrderChangeMaterialController extends BaseController
{
    /**
     * @var ProductionOrderChangeMaterialService
     * @Inject()
     */
    protected $service;

    public function getList()
    {
        return parent::getList();
    }
    /**
     * @Middleware(MenuMiddleware::class)
     */
    public function doEdit()
    {
        return parent::doEdit();
    }
    /**
     * @Middleware(MenuMiddleware::class)
     */
    public function doDelete()
    {
        return parent::doDelete();
    }
}