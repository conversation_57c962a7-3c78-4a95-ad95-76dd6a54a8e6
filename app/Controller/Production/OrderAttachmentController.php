<?php

namespace App\Controller\Production;

use App\Annotation\ControllerNameAnnotation;
use App\Controller\BaseController;
use App\Core\Services\ProductionOrder\ProductionOrderAttachmentService;
use App\Middleware\AuthMiddleware;
use App\Middleware\MenuMiddleware;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use Hyperf\HttpServer\Annotation\Middlewares;
use App\Middleware\ProductionLogMiddleware;

/**
 * @ControllerNameAnnotation("生产订单资料")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
    class OrderAttachmentController extends BaseController
{
    /**
     * @Inject()
     * @var ProductionOrderAttachmentService
     */
    protected $service;

    public function getList()
    {
        return parent::getList();
    }
    public function getAllList()
    {
        return parent::getAllList();
    }


    /**
     * @Middlewares({
     *     @Middleware(MenuMiddleware::class),
     *     @Middleware(ProductionLogMiddleware::class)
     * })
     */
    public function doEdit()
    {
        return parent::doEdit();
    }

    /**
     * @Middlewares({
     *     @Middleware(MenuMiddleware::class),
     *     @Middleware(ProductionLogMiddleware::class)
     * })
     */
    public function doMulti()
    {
        return parent::doMulti();
    }

    /**
     * @ControllerNameAnnotation("上传维修附件")
     * @Middlewares({
     *     @Middleware(MenuMiddleware::class),
     *     @Middleware(ProductionLogMiddleware::class)
     * })
     */
    public function uploadForRepair()
    {
        $params = $this->request->all();
        $result = $this->service->uploadForRepair($params);
        return $this->response->success($result);
    }
}