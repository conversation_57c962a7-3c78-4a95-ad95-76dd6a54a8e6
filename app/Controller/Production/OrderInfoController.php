<?php

namespace App\Controller\Production;

use App\Annotation\ControllerNameAnnotation;
use App\Controller\BaseController;
use App\Core\Services\ProductionOrder\ProductionOrderInfoService;
use App\Middleware\AuthMiddleware;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\MenuMiddleware;
use Hyperf\HttpServer\Annotation\Middlewares;
use App\Middleware\ProductionLogMiddleware;


/**
 * @ControllerNameAnnotation("生产订单信息")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class OrderInfoController extends BaseController
{
    /**
     * @var ProductionOrderInfoService
     * @Inject()
     */
    protected $service;

    /**
     * @Middlewares({
     *     @Middleware(MenuMiddleware::class),
     *     @Middleware(ProductionLogMiddleware::class)
     * })
     */
    public function doEdit()
    {
        return parent::doEdit();
    }

    /**
     * @ControllerNameAnnotation("回滚订单状态")
     * @Middlewares({
     *     @Middleware(MenuMiddleware::class),
     *     @Middleware(ProductionLogMiddleware::class)
     * })
     */
    public function undoWorkStatus()
    {
        $id = (int)$this->request->input('id', 0);
        $params = $this->request->all();
        unset($params['id']);
        $result = $this->service->undoWorkStatus($id, $params);
        return $this->response->success($result);
    }

    /**
     * @ControllerNameAnnotation("前进订单状态")
     * @Middlewares({
     *     @Middleware(MenuMiddleware::class),
     *     @Middleware(ProductionLogMiddleware::class)
     * })
     */
    public function toNextWorkStatus()
    {
        $id = (int)$this->request->input('id', 0);
        $params = $this->request->all();
        unset($params['id']);
        $result = $this->service->toNextWorkStatus($id, $params);
        return $this->response->success($result);
    }
}
