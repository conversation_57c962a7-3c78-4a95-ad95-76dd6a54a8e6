<?php

namespace App\Controller\Production;

use App\Annotation\ControllerNameAnnotation;
use App\Controller\BaseController;
use App\Core\Services\MacAddress\MacAddressService;
use App\Core\Services\SnCode\SnCodeService;
use App\Middleware\AuthMiddleware;
use App\Middleware\MenuMiddleware;
use App\Request\Production\MacAddress\AmountRequest;
use App\Request\Production\MacAddress\IdRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;

/**
 * @ControllerNameAnnotation("SN码管理")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class SnCodeController extends BaseController
{
    /**
     * @Inject()
     * @var SnCodeService
     */
    protected $service;

    /**
     * @ControllerNameAnnotation("导出")
     * @Middleware(AuthMiddleware::class)
     */
    public function export()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->export($filter, $op, $sort, $order);
        return $result;
    }
}