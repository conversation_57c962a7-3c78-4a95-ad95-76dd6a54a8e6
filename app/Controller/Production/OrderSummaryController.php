<?php

namespace App\Controller\Production;

use App\Annotation\ControllerNameAnnotation;
use App\Controller\BaseController;
use App\Core\Services\ProductionOrder\ProductionOrderSummaryService;
use App\Middleware\AuthMiddleware;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\MenuMiddleware;
use Hyperf\HttpServer\Annotation\Middlewares;
use App\Middleware\ProductionLogMiddleware;

/**
 * @ControllerNameAnnotation("生产订单生产总结")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class OrderSummaryController extends BaseController
{
    /**
     * @Inject()
     * @var ProductionOrderSummaryService
     */
    protected $service;

    public function overView()
    {
        $id = $this->request->input('id',0);
        $production_order_id = $this->request->input('production_order_id',0);
        $result = $this->service->getOverView($id,$production_order_id);
        return $this->response->success($result);
    }

    /**
     * @Middlewares({
     *     @Middleware(MenuMiddleware::class),
     *     @Middleware(ProductionLogMiddleware::class)
     * })
     */
    public function doEdit()
    {
        return parent::doEdit();
    }
}