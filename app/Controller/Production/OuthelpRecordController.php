<?php

namespace App\Controller\Production;

use App\Annotation\ControllerNameAnnotation;
use App\Controller\BaseController;
use App\Core\Services\ProductionOrder\ProductionOuthelpRecordService;
use App\Middleware\AuthMiddleware;
use App\Request\Production\ProductionOuthelpRecord\SendRecordRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Request\Production\ProductionOrderIdRequest;
use App\Middleware\MenuMiddleware;
use Hyperf\HttpServer\Annotation\Middlewares;
use App\Middleware\ProductionLogMiddleware;



/**
 * @ControllerNameAnnotation("生产备注记录")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class OuthelpRecordController extends BaseController
{
    /**
     * @Inject()
     * @var ProductionOuthelpRecordService
     */
    protected $service;

    /**
     * @ControllerNameAnnotation("添加备注记录")
     * @Middlewares({
     *     @Middleware(MenuMiddleware::class),
     *     @Middleware(ProductionLogMiddleware::class)
     * })
     */
    public function addRecord(ProductionOrderIdRequest $request)
    {
        $validated = $request->validated();
        $values = $this->request->all();
        return $this->response->success($this->service->addRecord($validated['production_order_id'], $values));
    }

    /**
     * @ControllerNameAnnotation("获取最后一次记录")
     * @param ProductionOrderIdRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function getLastOverView(ProductionOrderIdRequest $request)
    {
        $validated = $request->validated();
        $values = $this->request->all();
        return $this->response->success($this->service->getLastOverView($validated['production_order_id']));
    }

    public function sendRecord(SendRecordRequest $request)
    {
        $validated = $request->validated();
        return $this->response->success($this->service->sendRecord($validated['production_order_id'], $validated['user_ids']));
    }
    public function syncSalesRecord(ProductionOrderIdRequest $request)
    {
        $validated = $request->validated();
        return $this->response->success($this->service->syncSalesRecord($validated['production_order_id']));
    }
}
