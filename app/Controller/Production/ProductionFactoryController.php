<?php

namespace App\Controller\Production;

use App\Annotation\ControllerNameAnnotation;
use App\Controller\BaseController;
use App\Core\Services\MacAddress\MacAddressService;
use App\Core\Services\ProductionFactory\ProductionFactoryService;
use App\Core\Services\SnCode\SnCodeService;
use App\Middleware\AuthMiddleware;
use App\Middleware\MenuMiddleware;
use App\Request\Production\MacAddress\AmountRequest;
use App\Request\Production\MacAddress\IdRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;

/**
 * @ControllerNameAnnotation("加工厂管理")
 * @AutoController(prefix="/production/factory")
 * @Middleware(AuthMiddleware::class)
 */
class ProductionFactoryController extends BaseController
{
    /**
     * @Inject()
     * @var ProductionFactoryService
     */
    protected $service;
}