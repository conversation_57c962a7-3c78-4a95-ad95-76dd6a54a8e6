<?php

namespace App\Controller\Production\Assemble;

use App\Annotation\ControllerNameAnnotation;
use App\Controller\BaseController;
use App\Core\Services\AssembleOrder\AssembleOrderIqcService;
use App\Middleware\AuthMiddleware;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;

/**
 * @ControllerNameAnnotation("组装订单IQC")
 * @AutoController(prefix="/production/assemble_order_iqc")
 * @Middleware(AuthMiddleware::class)
 */
class AssembleOrderIqcController extends BaseController
{
    /**
     * @Inject()
     * @var AssembleOrderIqcService
     */
    protected $service;

}