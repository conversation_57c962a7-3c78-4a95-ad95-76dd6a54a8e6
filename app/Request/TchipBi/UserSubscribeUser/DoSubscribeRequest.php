<?php
declare(strict_types=1);

namespace App\Request\TchipBi\UserSubscribeUser;

use Hyperf\Validation\Request\FormRequest;

class DoSubscribeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'user_id'            => 'required',
            'subscribed_user_id' => 'required',
            'module'             => 'required',
            'action'             => 'required',
        ];
    }
}