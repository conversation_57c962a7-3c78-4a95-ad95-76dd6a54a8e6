<?php

namespace App\Task\TestFile;

use App\Core\Utils\Log;
use App\Core\Services\TestFile\ReorganizeService;
use Hyperf\Di\Annotation\Inject;

class FileReorganizeTask
{
    /**
     * @Inject
     * @var ReorganizeService
     */
    protected $reorganizeService;

    /**
     * 执行文件重排任务
     * @return void
     */
    public function execute()
    {
        $taskName = '测试文件目录重排任务';
        
        if (env('CRONTAB_ONOFF', false) === true) {
            Log::get('system', 'system')->info('==============================================================================');
            Log::get('system', 'system')->info("开始执行{$taskName}");
            
            try {
                // 执行重排任务，使用默认参数（今天的日期）
                $result = $this->reorganizeService->execute([
                    'task_type' => 'scheduled' // 标记为定时任务
                ]);
                
                Log::get('system', 'system')->info("任务执行成功", [
                    'task_id' => $result['task_id'],
                    'processed' => $result['processed'],
                    'total' => $result['total']
                ]);
                
            } catch (\Exception $e) {
                Log::get('system', 'system')->error("任务执行失败", [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
            
            Log::get('system', 'system')->info("任务执行结束");
            Log::get('system', 'system')->info('==============================================================================');
        } else {
            Log::get('system', 'system')->info("[{$taskName}] 系统未开启任务执行功能.");
        }
    }
}
