<?php

namespace App\Task\TchipSale;

use App\Core\Services\TchipSale\ClientService;
use App\Core\Utils\Log;

class ClientTask
{

    /**
     * 更新客户公海池
     * @return void
     */
    public function updateClientPublicPool()
    {
        $taskName = '更新客户公海池';
        if (env('CRONTAB_ONOFF', false) === true) {
            Log::get('system', 'system')->info('==============================================================================');
            Log::get('system', 'system')->info("开始执行{$taskName}");
            $result = make(ClientService::class)->updateClientPublicPool();
            $msg = $result['message'];
            unset($result['message']);
            Log::get('system', 'system')->info("执行结果: " . $msg);
            Log::get('system', 'system')->info("执行结果: " . json_encode($result));
            Log::get('system', 'system')->info("执行结束");
            Log::get('system', 'system')->info('==============================================================================');
        } else {
            Log::get('system', 'system')->info("[{$taskName}] 系统未开启任务执行功能.");
        }
    }
}