<?php

declare(strict_types=1);

namespace App\Model\FireflyService;

/**
 * @property int $uid 会员ID
 * @property int $group_id 级别ID
 */
class AuthGroupAccessModel extends FireflyServiceBaseModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'auth_group_access';

    /**
     * 主键设置（复合主键）
     * @var array
     */
    protected $primaryKey = ['uid', 'group_id'];

    /**
     * 是否自增主键
     * @var bool
     */
    public $incrementing = false;

    /**
     * 禁用时间戳自动管理（该表没有时间戳字段）
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'uid',
        'group_id'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'uid' => 'integer',
        'group_id' => 'integer',
    ];
}
