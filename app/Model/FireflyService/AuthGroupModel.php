<?php

declare(strict_types=1);

namespace App\Model\FireflyService;

/**
 * @property int $id
 * @property int $pid 父组别
 * @property string $name 组名
 * @property string $rules 规则ID
 * @property int $createtime 创建时间
 * @property int $updatetime 更新时间
 * @property string $status 状态
 */
class AuthGroupModel extends FireflyServiceBaseModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'auth_group';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'pid',
        'name',
        'rules',
        'createtime',
        'updatetime',
        'status'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'pid' => 'integer',
        'createtime' => 'integer',
        'updatetime' => 'integer',
    ];
}
