<?php

declare(strict_types=1);

namespace App\Model\FireflyService;

/**
 * @property int $id ID
 * @property string $username 用户名
 * @property string $nickname 昵称
 * @property string $password 密码
 * @property string $salt 密码盐
 * @property string $avatar 头像
 * @property string $email 电子邮箱
 * @property int $loginfailure 失败次数
 * @property int $logintime 登录时间
 * @property int $createtime 创建时间
 * @property int $updatetime 更新时间
 * @property string $token Session标识
 * @property string $status 状态
 */
class AdminModel extends FireflyServiceBaseModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'admin';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'username',
        'nickname', 
        'password',
        'salt',
        'avatar',
        'email',
        'loginfailure',
        'logintime',
        'createtime',
        'updatetime',
        'token',
        'status'
    ];

    /**
     * 禁用时间戳自动管理（该表使用createtime/updatetime字段）
     */
    public $timestamps = false;

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'loginfailure' => 'integer',
        'logintime' => 'integer',
        'createtime' => 'integer',
        'updatetime' => 'integer',
    ];
}
