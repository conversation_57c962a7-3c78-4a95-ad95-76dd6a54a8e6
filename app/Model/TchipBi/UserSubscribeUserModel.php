<?php

declare (strict_types=1);
namespace App\Model\TchipBi;

/**
 * @property int $id 
 * @property int $user_id 
 * @property int $subscribed_user_id 
 * @property string $type 
 * @property string $module 
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 */
class UserSubscribeUserModel extends \App\Model\Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'user_subscribe_user';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['user_id', 'subscribed_user_id', 'type', 'module'];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['id' => 'integer', 'user_id' => 'integer', 'subscribed_user_id' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    /* 订阅模块 - 周报 */
    const MODULE_WEEK_REPORT = 'week_report';

    protected $moduleTextList = [
        'week_report' => '周报',
    ];

    protected $typeTextList = [
        'subscribe' => '关注',
    ];

    // public function test()
    // {
    //     $this::query()->
    // }
}