<?php

namespace App\Model\TchipBi;

use Hyperf\DbConnection\Model\Model;
use Hyperf\Database\Model\Relations\BelongsTo;
use App\Constants\OaQcSpecialOrderCode;
use App\Model\TchipBi\UserModel;

class OaQcSpecialOrderApprovalModel extends Model
{
    protected $table = 'oa_qc_special_order_approvals';
    protected $primaryKey = 'id';
    protected $keyType = 'int';
    public $timestamps = true;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'order_id',
        'order_no',
        'step',
        'step_name',
        'round',
        'approver_id',
        'approver_name',
        'approver_department',
        'action',
        'comment',
        'reject_to_step',
        'reject_reason',
        'approved_at',
        'approval_duration',
        'ip_address',
        'user_agent',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'order_id' => 'integer',
        'step' => 'integer',
        'approver_id' => 'integer',
        'reject_to_step' => 'integer',
        'approval_duration' => 'integer',
        'approved_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 关联特采订单
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(OaQcSpecialOrderModel::class, 'order_id', 'id');
    }

    /**
     * 关联审批人用户信息
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(UserModel::class, 'approver_id', 'id');
    }

    /**
     * 获取动作文本
     */
    public function getActionTextAttribute(): string
    {
        return $this->action === 'approve' ? '同意' : '驳回';
    }

    /**
     * 获取步骤文本
     */
    public function getStepTextAttribute(): string
    {
        return OaQcSpecialOrderCode::getStepText($this->step);
    }

    /**
     * 检查是否为同意操作
     */
    public function isApproved(): bool
    {
        return $this->action === 'approve';
    }

    /**
     * 检查是否为驳回操作
     */
    public function isRejected(): bool
    {
        return $this->action === 'reject';
    }

    /**
     * 获取审批耗时文本
     */
    public function getApprovalDurationTextAttribute(): string
    {
        if (!$this->approval_duration) {
            return '未知';
        }

        $hours = intval($this->approval_duration / 60);
        $minutes = $this->approval_duration % 60;

        if ($hours > 0) {
            return $hours . '小时' . ($minutes > 0 ? $minutes . '分钟' : '');
        }

        return $minutes . '分钟';
    }
}
