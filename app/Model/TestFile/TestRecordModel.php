<?php

declare(strict_types=1);

namespace App\Model\TestFile;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id
 * @property int $product_id
 * @property string $sn
 * @property string $cpuid
 * @property string $test_date
 * @property string $test_datetime
 * @property array $test_types
 * @property int $file_count
 * @property int $total_size
 * @property string $factory_test_result
 * @property string $aging_test_result
 * @property string $firmware_version
 * @property string $factory_test_version
 * @property string $ddr_size
 * @property string $flash_size
 * @property string $remarks
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class TestRecordModel extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'file_test_records';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'product_id', 'sn', 'cpuid', 'test_date', 'test_datetime',
        'test_types', 'file_count', 'total_size', 'factory_test_result',
        'aging_test_result', 'firmware_version', 'factory_test_version',
        'ddr_size', 'flash_size', 'remarks'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'product_id' => 'integer',
        'test_types' => 'array',
        'file_count' => 'integer',
        'total_size' => 'integer',
        'test_date' => 'date:Y-m-d',
        'test_datetime' => 'datetime:Y-m-d H:i',
        'created_at' => 'datetime:Y-m-d H:i',
        'updated_at' => 'datetime:Y-m-d H:i'
    ];
    
    /**
     * 关联产品
     */
    public function product()
    {
        return $this->belongsTo(FileProductModel::class, 'product_id');
    }
}
