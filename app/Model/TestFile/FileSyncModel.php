<?php

declare(strict_types=1);

namespace App\Model\TestFile;

use Carbon\Carbon;
use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id
 * @property string $product
 * @property string $sn
 * @property string $date_folder
 * @property string $test_datetime
 * @property string $test_type
 * @property string $filename
 * @property int $file_size
 * @property string $file_mtime
 * @property string $src_path
 * @property string $dst_path
 * @property string $file_md5
 * @property int $sync_status
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class FileSyncModel extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'file_sync';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'product', 'sn', 'date_folder', 'test_datetime', 'test_type',
        'filename', 'file_size', 'file_mtime', 'src_path', 'dst_path',
        'file_md5', 'sync_status', 'file_type', 'is_parsed', 'parsed_at'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'file_size' => 'integer',
        'sync_status' => 'integer',
        'is_parsed' => 'integer',
        'file_mtime' => 'datetime:Y-m-d H:i',
        'parsed_at' => 'datetime:Y-m-d H:i:s',
        'created_at' => 'datetime:Y-m-d H:i',
        'updated_at' => 'datetime:Y-m-d H:i'
    ];

}
