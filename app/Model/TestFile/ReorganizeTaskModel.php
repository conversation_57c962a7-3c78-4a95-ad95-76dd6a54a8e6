<?php

declare(strict_types=1);

namespace App\Model\TestFile;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id
 * @property string $task_type
 * @property string $source_dir
 * @property string $target_dir
 * @property int $status
 * @property int $total_files
 * @property int $processed_files
 * @property string $error_message
 * @property \Carbon\Carbon $started_at
 * @property \Carbon\Carbon $completed_at
 * @property \Carbon\Carbon $created_at
 */
class ReorganizeTaskModel extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'file_reorganize_tasks';
    
    /**
     * 不使用updated_at字段
     */
    const UPDATED_AT = null;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'task_type', 'source_dir', 'target_dir', 'status',
        'total_files', 'processed_files', 'error_message',
        'started_at', 'completed_at'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'status' => 'integer',
        'total_files' => 'integer',
        'processed_files' => 'integer',
        'started_at' => 'datetime:Y-m-d H:i',
        'completed_at' => 'datetime:Y-m-d H:i',
        'created_at' => 'datetime:Y-m-d H:i'
    ];
}
