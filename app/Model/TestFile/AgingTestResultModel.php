<?php

declare(strict_types=1);

namespace App\Model\TestFile;

use Carbon\Carbon;
use Hyperf\DbConnection\Model\Model;

/**
 * 老化测试结果模型
 * 
 * @property int $id
 * @property int $file_sync_id 关联文件表ID
 * @property string $product 产品名称
 * @property string $sn 序列号
 * @property string $test_datetime 测试日期时间
 * @property string|null $runtime 运行时间格式化字符串
 * @property int|null $runtime_seconds 运行时间总秒数
 * @property \Carbon\Carbon|null $parsed_at 解析时间
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class AgingTestResultModel extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'file_aging_test_result';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'file_sync_id', 'product', 'sn', 'test_datetime',
        'runtime', 'runtime_seconds', 'parsed_at'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'file_sync_id' => 'integer',
        'runtime_seconds' => 'integer',
        'parsed_at' => 'datetime:Y-m-d H:i:s',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s'
    ];

    /**
     * 关联文件同步记录
     */
    public function fileSync()
    {
        return $this->belongsTo(FileSyncModel::class, 'file_sync_id', 'id');
    }
}
