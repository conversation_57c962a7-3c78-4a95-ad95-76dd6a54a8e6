<?php

declare(strict_types=1);

namespace App\Model\TestFile;

use Carbon\Carbon;
use Hyperf\DbConnection\Model\Model;

/**
 * 厂测结果模型
 * 
 * @property int $id
 * @property int $file_sync_id 关联文件表ID
 * @property string $product 产品名称
 * @property string $sn 序列号
 * @property string $test_datetime 测试日期时间
 * @property string|null $device_name 设备名
 * @property string|null $cpuid CPUID
 * @property string|null $factory_test_version 厂测版本
 * @property string|null $firmware_version 固件版本
 * @property string|null $ddr DDR
 * @property string|null $flash Flash
 * @property string|null $success_projects 成功项目
 * @property string|null $failed_projects 失败项目
 * @property string|null $factory_test_result 厂测结果
 * @property \Carbon\Carbon|null $parsed_at 解析时间
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class FactoryTestResultModel extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'file_factory_test_result';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'file_sync_id', 'product', 'sn', 'test_datetime',
        'device_name', 'cpuid', 'factory_test_version', 'firmware_version',
        'ddr', 'flash', 'success_projects', 'failed_projects',
        'factory_test_result', 'parsed_at'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'file_sync_id' => 'integer',
        'parsed_at' => 'datetime:Y-m-d H:i:s',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s'
    ];

    /**
     * 关联文件同步记录
     */
    public function fileSync()
    {
        return $this->belongsTo(FileSyncModel::class, 'file_sync_id', 'id');
    }
    
}
