<?php

declare(strict_types=1);

namespace App\Model\TchipBiFms;

/**
 * FMS权限主体模型
 *
 * @property int $id 主键ID
 * @property string $subject_type 主体类型：user/dept/role
 * @property int $subject_id 对应用户/部门/角色ID
 */
class FmsSubjectModel extends \App\Model\Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'fms_subjects';

    /**
     * Indicates if the model should be timestamped.
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'subject_type',
        'subject_id',
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'subject_id' => 'integer',
    ];

    /**
     * 主体类型选项
     */
    const SUBJECT_TYPE_USER = 'user';
    const SUBJECT_TYPE_DEPT = 'dept';
    const SUBJECT_TYPE_ROLE = 'role';

    /**
     * ACL规则关联
     */
    public function aclRules()
    {
        return $this->hasMany(FmsAclRuleModel::class, 'subject_key_id');
    }

    /**
     * 创建者关联
     */
    public function creator()
    {
        return $this->belongsTo(\App\Model\TchipBi\UserModel::class, 'created_by');
    }

    /**
     * 更新者关联
     */
    public function updater()
    {
        return $this->belongsTo(\App\Model\TchipBi\UserModel::class, 'updated_by');
    }

    /**
     * 获取主体类型文本
     */
    public function getSubjectTypeTextAttribute(): string
    {
        switch ($this->subject_type) {
            case self::SUBJECT_TYPE_USER:
                return '用户';
            case self::SUBJECT_TYPE_DEPT:
                return '部门';
            case self::SUBJECT_TYPE_ROLE:
                return '角色';
            default:
                return '未知';
        }
    }

    /**
     * 检查是否为用户主体
     */
    public function isUser(): bool
    {
        return $this->subject_type === self::SUBJECT_TYPE_USER;
    }

    /**
     * 检查是否为部门主体
     */
    public function isDepartment(): bool
    {
        return $this->subject_type === self::SUBJECT_TYPE_DEPT;
    }

    /**
     * 检查是否为角色主体
     */
    public function isRole(): bool
    {
        return $this->subject_type === self::SUBJECT_TYPE_ROLE;
    }

    /**
     * 根据用户ID获取相关主体
     */
    public static function getSubjectsForUser(int $userId): array
    {
        $subjects = [];

        // 用户本身
        $userSubject = static::where('subject_type', self::SUBJECT_TYPE_USER)
                            ->where('subject_id', $userId)
                            ->first();
        if ($userSubject) {
            $subjects[] = $userSubject;
        }

        // TODO: 根据实际业务逻辑获取用户所属的部门和角色
        // 这里需要根据项目的用户-部门-角色关系来实现

        return $subjects;
    }

    /**
     * 创建或更新主体
     */
    public static function createOrUpdate(string $type, int $subjectId, string $name, ?string $description = null): FmsSubjectModel
    {
        return static::updateOrCreate(
            [
                'subject_type' => $type,
                'subject_id' => $subjectId,
            ],
            [
                'subject_name' => $name,
                'description' => $description,
            ]
        );
    }
}