<?php

declare(strict_types=1);

namespace App\Model\TchipBiFms;

use Carbon\Carbon;
use Hyperf\Utils\Str;

/**
 * FMS文件分享模型
 *
 * @property int $id 主键ID
 * @property int $file_id 文件ID
 * @property string $share_token 分享令牌（唯一）
 * @property string|null $password 外链访问密码（hash存储）
 * @property int $visits 当前访问次数
 * @property Carbon|null $expires_at 过期时间
 * @property int $created_by 创建者ID
 * @property int|null $updated_by 最后修改者ID
 * @property Carbon|null $created_at 创建时间
 * @property Carbon|null $updated_at 更新时间
 */
class FmsFileShareModel extends \App\Model\Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'fms_file_shares';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'file_id',
        'share_token',
        'password',
        'visits',
        'expires_at',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'file_id' => 'integer',
        'visit_limit' => 'integer',
        'visit_count' => 'integer',
        'download_limit' => 'integer',
        'download_count' => 'integer',
        'is_active' => 'boolean',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'expires_at' => 'datetime:Y-m-d H:i',
        'created_at' => 'datetime:Y-m-d H:i',
        'updated_at' => 'datetime:Y-m-d H:i',
    ];

    /**
     * 关联的文件
     */
    public function file()
    {
        return $this->belongsTo(FmsFileModel::class, 'file_id');
    }

    /**
     * 创建者关联
     */
    public function creator()
    {
        return $this->belongsTo(\App\Model\TchipBi\UserModel::class, 'created_by');
    }

    /**
     * 更新者关联
     */
    public function updater()
    {
        return $this->belongsTo(\App\Model\TchipBi\UserModel::class, 'updated_by');
    }

    /**
     * 检查分享是否有效
     */
    public function isValid(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        // 检查是否过期
        if ($this->expires_at && $this->expires_at->isPast()) {
            return false;
        }

        // 检查访问次数限制
        if ($this->visit_limit && $this->visit_count >= $this->visit_limit) {
            return false;
        }

        return true;
    }

    /**
     * 检查是否可以下载
     */
    public function canDownload(): bool
    {
        if (!$this->isValid()) {
            return false;
        }

        // 检查下载次数限制
        if ($this->download_limit && $this->download_count >= $this->download_limit) {
            return false;
        }

        return true;
    }

    /**
     * 检查密码
     */
    public function checkPassword(?string $password): bool
    {
        if (!$this->share_password) {
            return true; // 无密码保护
        }

        return $this->share_password === $password;
    }

    /**
     * 增加访问次数
     */
    public function incrementVisitCount(): void
    {
        $this->increment('visit_count');
    }

    /**
     * 增加下载次数
     */
    public function incrementDownloadCount(): void
    {
        $this->increment('download_count');
    }

    /**
     * 生成分享令牌
     */
    public static function generateToken(): string
    {
        do {
            $token = Str::random(32);
        } while (static::where('share_token', $token)->exists());

        return $token;
    }

    /**
     * 创建文件分享
     */
    public static function createShare(
        int $fileId,
        int $createdBy,
        ?string $password = null,
        ?Carbon $expiresAt = null,
        ?int $visitLimit = null,
        ?int $downloadLimit = null,
        ?string $description = null
    ): FmsFileShareModel {
        return static::create([
            'file_id' => $fileId,
            'share_token' => static::generateToken(),
            'share_password' => $password,
            'expires_at' => $expiresAt,
            'visit_limit' => $visitLimit,
            'download_limit' => $downloadLimit,
            'visit_count' => 0,
            'download_count' => 0,
            'is_active' => true,
            'description' => $description,
            'created_by' => $createdBy,
        ]);
    }

    /**
     * 通过令牌查找分享
     */
    public static function findByToken(string $token): ?FmsFileShareModel
    {
        return static::where('share_token', $token)->first();
    }

    /**
     * 获取分享链接
     */
    public function getShareUrlAttribute(): string
    {
        // TODO: 根据实际域名配置生成完整URL
        return config('app.url') . '/share/' . $this->share_token;
    }

    /**
     * 获取过期状态文本
     */
    public function getExpiryStatusAttribute(): string
    {
        if (!$this->expires_at) {
            return '永不过期';
        }

        if ($this->expires_at->isPast()) {
            return '已过期';
        }

        $diffInDays = $this->expires_at->diffInDays(Carbon::now());
        if ($diffInDays > 0) {
            return $diffInDays . '天后过期';
        }

        $diffInHours = $this->expires_at->diffInHours(Carbon::now());
        if ($diffInHours > 0) {
            return $diffInHours . '小时后过期';
        }

        return '即将过期';
    }

    /**
     * 获取访问状态文本
     */
    public function getVisitStatusAttribute(): string
    {
        if (!$this->visit_limit) {
            return '无限制';
        }

        $remaining = $this->visit_limit - $this->visit_count;
        return "剩余 {$remaining} 次访问";
    }

    /**
     * 获取下载状态文本
     */
    public function getDownloadStatusAttribute(): string
    {
        if (!$this->download_limit) {
            return '无限制';
        }

        $remaining = $this->download_limit - $this->download_count;
        return "剩余 {$remaining} 次下载";
    }

    /**
     * 禁用分享
     */
    public function disable(): void
    {
        $this->is_active = false;
        $this->save();
    }

    /**
     * 启用分享
     */
    public function enable(): void
    {
        $this->is_active = true;
        $this->save();
    }

    /**
     * 延长过期时间
     */
    public function extendExpiry(int $days): void
    {
        if ($this->expires_at) {
            $this->expires_at = $this->expires_at->addDays($days);
        } else {
            $this->expires_at = Carbon::now()->addDays($days);
        }
        $this->save();
    }

    /**
     * 重置访问统计
     */
    public function resetStats(): void
    {
        $this->visit_count = 0;
        $this->download_count = 0;
        $this->save();
    }

    /**
     * 清理过期分享
     */
    public static function cleanupExpired(): int
    {
        return static::where('expires_at', '<', Carbon::now())
                    ->where('is_active', true)
                    ->update(['is_active' => false]);
    }

    /**
     * 获取用户的分享统计
     */
    public static function getUserShareStats(int $userId): array
    {
        $shares = static::where('created_by', $userId)->get();

        return [
            'total_shares' => $shares->count(),
            'active_shares' => $shares->where('is_active', true)->count(),
            'expired_shares' => $shares->filter(function ($share) {
                return $share->expires_at && $share->expires_at->isPast();
            })->count(),
            'total_visits' => $shares->sum('visit_count'),
            'total_downloads' => $shares->sum('download_count'),
        ];
    }
}