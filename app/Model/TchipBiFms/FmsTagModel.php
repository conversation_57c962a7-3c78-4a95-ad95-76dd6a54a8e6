<?php

declare(strict_types=1);

namespace App\Model\TchipBiFms;

use Carbon\Carbon;

/**
 * FMS标签模型
 *
 * @property int $id 主键ID
 * @property string $name 标签名称
 * @property int $created_by 创建者ID
 * @property int|null $updated_by 最后修改者ID
 * @property Carbon|null $created_at 创建时间
 */
class FmsTagModel extends \App\Model\Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'fms_tags';

    /**
     * Indicates if the model should be timestamped.
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'created_at' => 'datetime:Y-m-d H:i',
        'updated_at' => 'datetime:Y-m-d H:i',
    ];

    /**
     * 默认标签颜色
     */
    const DEFAULT_COLORS = [
        '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
        '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ];

    /**
     * 关联的文件（多对多）
     */
    public function files()
    {
        return $this->belongsToMany(FmsFileModel::class, 'fms_file_tags', 'tag_id', 'file_id')
                    ->withPivot('created_by', 'created_at');
    }

    /**
     * 文件标签关联表
     */
    public function fileTags()
    {
        return $this->hasMany(FmsFileTagModel::class, 'tag_id');
    }

    /**
     * 创建者关联
     */
    public function creator()
    {
        return $this->belongsTo(\App\Model\TchipBi\UserModel::class, 'created_by');
    }

    /**
     * 更新者关联
     */
    public function updater()
    {
        return $this->belongsTo(\App\Model\TchipBi\UserModel::class, 'updated_by');
    }

    /**
     * 增加使用次数
     */
    public function incrementUsage(): void
    {
        $this->increment('usage_count');
    }

    /**
     * 减少使用次数
     */
    public function decrementUsage(): void
    {
        if ($this->usage_count > 0) {
            $this->decrement('usage_count');
        }
    }

    /**
     * 获取随机颜色
     */
    public static function getRandomColor(): string
    {
        return self::DEFAULT_COLORS[array_rand(self::DEFAULT_COLORS)];
    }

    /**
     * 创建或获取标签
     */
    public static function createOrGet(string $name, ?string $color = null, ?string $description = null): FmsTagModel
    {
        $tag = static::where('name', $name)->first();

        if (!$tag) {
            $tag = static::create([
                'name' => $name,
                'color' => $color ?: static::getRandomColor(),
                'description' => $description,
                'usage_count' => 0,
                'is_active' => true,
                'created_by' => 1, // TODO: 获取当前用户ID
            ]);
        }

        return $tag;
    }

    /**
     * 获取热门标签
     */
    public static function getPopularTags(int $limit = 10) 
    {
        return static::where('is_active', true)
                    ->where('usage_count', '>', 0)
                    ->orderBy('usage_count', 'desc')
                    ->limit($limit)
                    ->get();
    }

    /**
     * 搜索标签
     */
    public static function searchTags(string $keyword, int $limit = 20) 
    {
        return static::where('is_active', true)
                    ->where('name', 'like', '%' . $keyword . '%')
                    ->orderBy('usage_count', 'desc')
                    ->orderBy('name')
                    ->limit($limit)
                    ->get();
    }

    /**
     * 清理未使用的标签
     */
    public static function cleanupUnusedTags(): int
    {
        return static::where('usage_count', 0)
                    ->where('created_at', '<', Carbon::now()->subDays(30))
                    ->delete();
    }

    /**
     * 获取标签统计信息
     */
    public function getStatsAttribute(): array
    {
        return [
            'usage_count' => $this->usage_count,
            'file_count' => $this->files()->count(),
            'created_days_ago' => $this->created_at->diffInDays(Carbon::now()),
        ];
    }
}