<?php

declare(strict_types=1);

namespace App\Model\TchipBiFms;

use Carbon\Carbon;

/**
 * FMS文件模型
 *
 * @property int $id 主键ID
 * @property int $directory_id 所属目录ID
 * @property string $name 文件名
 * @property string $path 文件存储路径（Flysystem适配器）
 * @property int $size 文件大小（字节）
 * @property string $mime_type 文件类型（MIME）
 * @property int $version 版本号（覆盖上传时递增）
 * @property string $visibility 可见性范围：public/department/user/private
 * @property int $sort_order 文件排序（拖拽排序用）
 * @property int $is_deleted 是否删除（回收站标记）
 * @property int $created_by 上传者ID
 * @property int|null $updated_by 最后修改者ID
 * @property Carbon|null $created_at 创建时间
 * @property Carbon|null $updated_at 更新时间
 */
class FmsFileModel extends \App\Model\Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'fms_files';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'directory_id',
        'name',
        'path',
        'size',
        'mime_type',
        'version',
        'visibility',
        'sort_order',
        'is_deleted',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'directory_id' => 'integer',
        'size' => 'integer',
        'version' => 'integer',
        'sort_order' => 'integer',
        'is_deleted' => 'integer',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 可见性选项
     */
    const VISIBILITY_PUBLIC = 'public';
    const VISIBILITY_DEPARTMENT = 'department';
    const VISIBILITY_USER = 'user';
    const VISIBILITY_PRIVATE = 'private';

    /**
     * 所属目录关联
     */
    public function directory()
    {
        return $this->belongsTo(FmsDirectoryModel::class, 'directory_id');
    }

    /**
     * 父文件关联（版本管理）
     */
    public function parentFile()
    {
        return $this->belongsTo(FmsFileModel::class, 'parent_file_id');
    }

    /**
     * 子版本文件
     */
    public function versions()
    {
        return $this->hasMany(FmsFileModel::class, 'parent_file_id')->orderBy('version', 'desc');
    }

    /**
     * 文件标签关联
     */
    public function tags()
    {
        return $this->belongsToMany(FmsTagModel::class, 'fms_file_tags', 'file_id', 'tag_id')
                    ->withPivot('created_by', 'created_at');
    }

    /**
     * 文件分享关联
     */
    public function shares()
    {
        return $this->hasMany(FmsFileShareModel::class, 'file_id');
    }

    /**
     * 创建者关联
     */
    public function creator()
    {
        return $this->belongsTo(\App\Model\TchipBi\UserModel::class, 'created_by');
    }

    /**
     * 更新者关联
     */
    public function updater()
    {
        return $this->belongsTo(\App\Model\TchipBi\UserModel::class, 'updated_by');
    }

    /**
     * 删除者关联
     */
    public function deleter()
    {
        return $this->belongsTo(\App\Model\TchipBi\UserModel::class, 'deleted_by');
    }

    /**
     * 获取文件扩展名
     */
    public function getExtensionAttribute(): string
    {
        return pathinfo($this->original_name, PATHINFO_EXTENSION);
    }

    /**
     * 获取格式化的文件大小
     */
    public function getFormattedSizeAttribute(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * 检查是否为图片文件
     */
    public function isImage(): bool
    {
        return strpos($this->mime_type, 'image/') === 0;
    }

    /**
     * 检查是否为视频文件
     */
    public function isVideo(): bool
    {
        return strpos($this->mime_type, 'video/') === 0;
    }

    /**
     * 检查是否为音频文件
     */
    public function isAudio(): bool
    {
        return strpos($this->mime_type, 'audio/') === 0;
    }

    /**
     * 检查是否为文档文件
     */
    public function isDocument(): bool
    {
        $documentTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'text/plain',
        ];

        return in_array($this->mime_type, $documentTypes);
    }

    /**
     * 获取文件完整路径
     */
    public function getFullPathAttribute(): string
    {
        return $this->directory->full_path . '/' . $this->name;
    }

    /**
     * 增加下载次数
     */
    public function incrementDownloadCount(): void
    {
        $this->increment('download_count');
    }

    /**
     * 获取最新版本
     */
    public function getLatestVersion()
    {
        if ($this->parent_file_id === null) {
            // 这是主文件，获取最新版本
            return $this->versions()->first() ?: $this;
        }

        // 这是版本文件，获取主文件的最新版本
        return $this->parentFile->getLatestVersion();
    }

    /**
     * 检查是否为最新版本
     */
    public function isLatestVersion(): bool
    {
        $latest = $this->getLatestVersion();
        return $latest->id === $this->id;
    }

    /**
     * 创建新版本
     */
    public function createNewVersion(array $attributes): FmsFileModel
    {
        $parentId = $this->parent_file_id ?: $this->id;
        $maxVersion = static::where('parent_file_id', $parentId)
                           ->orWhere('id', $parentId)
                           ->max('version');

        $attributes['parent_file_id'] = $parentId;
        $attributes['version'] = $maxVersion + 1;
        $attributes['directory_id'] = $this->directory_id;

        return static::create($attributes);
    }
}