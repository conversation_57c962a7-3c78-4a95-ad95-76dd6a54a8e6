<?php

declare(strict_types=1);

namespace App\Model\TchipBiFms;

use Carbon\Carbon;

/**
 * FMS文件标签关联模型
 *
 * @property int $id 主键ID
 * @property int $file_id 文件ID
 * @property int $tag_id 标签ID
 * @property int $created_by 创建者ID
 * @property int|null $updated_by 最后修改者ID
 * @property Carbon|null $created_at 创建时间
 */
class FmsFileTagModel extends \App\Model\Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'fms_file_tags';

    /**
     * Indicates if the model should be timestamped.
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'file_id',
        'tag_id',
        'created_by',
        'updated_by',
        'created_at',
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'file_id' => 'integer',
        'tag_id' => 'integer',
        'created_by' => 'integer',
        'created_at' => 'datetime:Y-m-d H:i',
    ];

    /**
     * 关联的文件
     */
    public function file()
    {
        return $this->belongsTo(FmsFileModel::class, 'file_id');
    }

    /**
     * 关联的标签
     */
    public function tag()
    {
        return $this->belongsTo(FmsTagModel::class, 'tag_id');
    }

    /**
     * 创建者关联
     */
    public function creator()
    {
        return $this->belongsTo(\App\Model\TchipBi\UserModel::class, 'created_by');
    }

    /**
     * 创建文件标签关联
     */
    public static function createRelation(int $fileId, int $tagId, int $createdBy): FmsFileTagModel
    {
        // 检查关联是否已存在
        $existing = static::where('file_id', $fileId)
                          ->where('tag_id', $tagId)
                          ->first();

        if ($existing) {
            return $existing;
        }

        // 创建新关联
        $relation = static::create([
            'file_id' => $fileId,
            'tag_id' => $tagId,
            'created_by' => $createdBy,
            'created_at' => Carbon::now(),
        ]);

        // 增加标签使用次数
        $tag = FmsTagModel::find($tagId);
        if ($tag) {
            $tag->incrementUsage();
        }

        return $relation;
    }

    /**
     * 删除文件标签关联
     */
    public static function removeRelation(int $fileId, int $tagId): bool
    {
        $relation = static::where('file_id', $fileId)
                          ->where('tag_id', $tagId)
                          ->first();

        if ($relation) {
            // 减少标签使用次数
            $tag = FmsTagModel::find($tagId);
            if ($tag) {
                $tag->decrementUsage();
            }

            return $relation->delete();
        }

        return false;
    }

    /**
     * 批量为文件添加标签
     */
    public static function batchAddTags(int $fileId, array $tagIds, int $createdBy): array
    {
        $relations = [];

        foreach ($tagIds as $tagId) {
            $relations[] = static::createRelation($fileId, $tagId, $createdBy);
        }

        return $relations;
    }

    /**
     * 批量移除文件标签
     */
    public static function batchRemoveTags(int $fileId, array $tagIds): int
    {
        $removed = 0;

        foreach ($tagIds as $tagId) {
            if (static::removeRelation($fileId, $tagId)) {
                $removed++;
            }
        }

        return $removed;
    }

    /**
     * 替换文件的所有标签
     */
    public static function replaceTags(int $fileId, array $tagIds, int $createdBy): array
    {
        // 获取当前标签
        $currentTagIds = static::where('file_id', $fileId)->pluck('tag_id')->toArray();

        // 需要删除的标签
        $toRemove = array_diff($currentTagIds, $tagIds);

        // 需要添加的标签
        $toAdd = array_diff($tagIds, $currentTagIds);

        // 删除不需要的标签
        if (!empty($toRemove)) {
            static::batchRemoveTags($fileId, $toRemove);
        }

        // 添加新标签
        $newRelations = [];
        if (!empty($toAdd)) {
            $newRelations = static::batchAddTags($fileId, $toAdd, $createdBy);
        }

        return $newRelations;
    }

    /**
     * 获取文件的所有标签
     */
    public static function getFileTags(int $fileId) 
    {
        return static::where('file_id', $fileId)
                    ->with('tag')
                    ->get()
                    ->pluck('tag');
    }

    /**
     * 获取标签的所有文件
     */
    public static function getTagFiles(int $tagId) 
    {
        return static::where('tag_id', $tagId)
                    ->with('file')
                    ->get()
                    ->pluck('file');
    }
}