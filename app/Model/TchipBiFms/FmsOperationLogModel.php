<?php

declare(strict_types=1);

namespace App\Model\TchipBiFms;

use Carbon\Carbon;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Utils\ApplicationContext;

/**
 * FMS操作日志模型
 *
 * @property int $id 主键ID
 * @property int $user_id 操作者ID
 * @property string $action 操作类型：upload/download/delete/restore/create_dir/rename
 * @property string $target_type 操作目标类型：directory/file
 * @property int $target_id 操作对象ID
 * @property string|null $detail 操作详情（JSON，可选）
 * @property Carbon $created_at 操作时间
 */
class FmsOperationLogModel extends \App\Model\Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'fms_operation_logs';

    /**
     * Indicates if the model should be timestamped.
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'action',
        'target_type',
        'target_id',
        'target_name',
        'old_data',
        'new_data',
        'client_ip',
        'user_agent',
        'is_success',
        'error_message',
        'created_at',
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'user_id' => 'integer',
        'target_id' => 'integer',
        'old_data' => 'array',
        'new_data' => 'array',
        'is_success' => 'boolean',
        'created_at' => 'datetime:Y-m-d H:i',
    ];

    /**
     * 操作动作选项
     */
    const ACTION_CREATE = 'create';
    const ACTION_UPDATE = 'update';
    const ACTION_DELETE = 'delete';
    const ACTION_MOVE = 'move';
    const ACTION_COPY = 'copy';
    const ACTION_UPLOAD = 'upload';
    const ACTION_DOWNLOAD = 'download';
    const ACTION_SHARE = 'share';
    const ACTION_RESTORE = 'restore';
    const ACTION_PURGE = 'purge';
    const ACTION_TAG = 'tag';
    const ACTION_UNTAG = 'untag';

    /**
     * 目标类型选项
     */
    const TARGET_TYPE_DIRECTORY = 'directory';
    const TARGET_TYPE_FILE = 'file';
    const TARGET_TYPE_TAG = 'tag';
    const TARGET_TYPE_SHARE = 'share';

    /**
     * 操作用户关联
     */
    public function user()
    {
        return $this->belongsTo(\App\Model\TchipBi\UserModel::class, 'user_id');
    }

    /**
     * 获取操作动作文本
     */
    public function getActionTextAttribute(): string
    {
        switch ($this->action) {
            case self::ACTION_CREATE:
                return '创建';
            case self::ACTION_UPDATE:
                return '更新';
            case self::ACTION_DELETE:
                return '删除';
            case self::ACTION_MOVE:
                return '移动';
            case self::ACTION_COPY:
                return '复制';
            case self::ACTION_UPLOAD:
                return '上传';
            case self::ACTION_DOWNLOAD:
                return '下载';
            case self::ACTION_SHARE:
                return '分享';
            case self::ACTION_RESTORE:
                return '恢复';
            case self::ACTION_PURGE:
                return '清除';
            case self::ACTION_TAG:
                return '添加标签';
            case self::ACTION_UNTAG:
                return '移除标签';
            default:
                return '未知操作';
        }
    }

    /**
     * 获取目标类型文本
     */
    public function getTargetTypeTextAttribute(): string
    {
        switch ($this->target_type) {
            case self::TARGET_TYPE_DIRECTORY:
                return '目录';
            case self::TARGET_TYPE_FILE:
                return '文件';
            case self::TARGET_TYPE_TAG:
                return '标签';
            case self::TARGET_TYPE_SHARE:
                return '分享';
            default:
                return '未知';
        }
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute(): string
    {
        return $this->is_success ? '成功' : '失败';
    }

    /**
     * 记录操作日志
     */
    public static function log(
        int $userId,
        string $action,
        string $targetType,
        ?int $targetId = null,
        ?string $targetName = null,
        ?array $oldData = null,
        ?array $newData = null,
        bool $isSuccess = true,
        ?string $errorMessage = null
    ): FmsOperationLogModel {
        return static::create([
            'user_id' => $userId,
            'action' => $action,
            'target_type' => $targetType,
            'target_id' => $targetId,
            'target_name' => $targetName,
            'old_data' => $oldData,
            'new_data' => $newData,
            'client_ip' => ApplicationContext::getContainer()->get(RequestInterface::class)->getServerParams()['REMOTE_ADDR'] ?? null,
            'user_agent' => ApplicationContext::getContainer()->get(RequestInterface::class)->getHeaderLine('User-Agent'),
            'is_success' => $isSuccess,
            'error_message' => $errorMessage,
            'created_at' => Carbon::now(),
        ]);
    }

    /**
     * 记录目录操作
     */
    public static function logDirectory(
        int               $userId,
        string            $action,
        FmsDirectoryModel $directory,
        ?array            $oldData = null,
        bool              $isSuccess = true,
        ?string           $errorMessage = null
    ): FmsOperationLogModel {
        return static::log(
            $userId,
            $action,
            self::TARGET_TYPE_DIRECTORY,
            $directory->id,
            $directory->name,
            $oldData,
            $directory->toArray(),
            $isSuccess,
            $errorMessage
        );
    }

    /**
     * 记录文件操作
     */
    public static function logFile(
        int          $userId,
        string       $action,
        FmsFileModel $file,
        ?array       $oldData = null,
        bool         $isSuccess = true,
        ?string      $errorMessage = null
    ): FmsOperationLogModel {
        return static::log(
            $userId,
            $action,
            self::TARGET_TYPE_FILE,
            $file->id,
            $file->name,
            $oldData,
            $file->toArray(),
            $isSuccess,
            $errorMessage
        );
    }

    /**
     * 获取用户操作统计
     */
    public static function getUserStats(int $userId, int $days = 30): array
    {
        $startDate = Carbon::now()->subDays($days);

        $logs = static::where('user_id', $userId)
                     ->where('created_at', '>=', $startDate)
                     ->get();

        return [
            'total_operations' => $logs->count(),
            'successful_operations' => $logs->where('is_success', true)->count(),
            'failed_operations' => $logs->where('is_success', false)->count(),
            'actions' => $logs->groupBy('action')->map->count(),
            'target_types' => $logs->groupBy('target_type')->map->count(),
        ];
    }

    /**
     * 获取系统操作统计
     */
    public static function getSystemStats(int $days = 30): array
    {
        $startDate = Carbon::now()->subDays($days);

        $logs = static::where('created_at', '>=', $startDate)->get();

        return [
            'total_operations' => $logs->count(),
            'successful_operations' => $logs->where('is_success', true)->count(),
            'failed_operations' => $logs->where('is_success', false)->count(),
            'unique_users' => $logs->pluck('user_id')->unique()->count(),
            'actions' => $logs->groupBy('action')->map->count(),
            'target_types' => $logs->groupBy('target_type')->map->count(),
            'daily_stats' => $logs->groupBy(function ($log) {
                return $log->created_at->format('Y-m-d');
            })->map->count(),
        ];
    }

    /**
     * 清理过期日志
     */
    public static function cleanup(int $daysToKeep = 90): int
    {
        $expiredDate = Carbon::now()->subDays($daysToKeep);

        return static::where('created_at', '<', $expiredDate)->delete();
    }
}