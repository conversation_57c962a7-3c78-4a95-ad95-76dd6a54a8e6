<?php

declare(strict_types=1);

namespace App\Model\TchipBiFms;

use Carbon\Carbon;

/**
 * FMS目录模型
 *
 * @property int $id 主键ID
 * @property int|null $parent_id 父目录ID，NULL=根目录
 * @property int $owner_id 拥有者ID（用户或部门）
 * @property string $owner_type 拥有者类型：user/dept
 * @property string $name 目录名称
 * @property string $path 目录路径 /1/2/3/
 * @property string $visibility 可见性范围：public/department/user/private
 * @property int $sort_order 目录排序（拖拽排序用）
 * @property int $is_deleted 是否删除（回收站标记）
 * @property int|null $updated_by 最后修改者ID
 * @property Carbon|null $created_at 创建时间
 * @property Carbon|null $updated_at 更新时间
 */
class FmsDirectoryModel extends \App\Model\Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'fms_directories';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'parent_id',
        'owner_id',
        'owner_type',
        'name',
        'path',
        'visibility',
        'sort_order',
        'is_deleted',
        'updated_by',
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'parent_id' => 'integer',
        'owner_id' => 'integer',
        'sort_order' => 'integer',
        'is_deleted' => 'integer',
        'updated_by' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 可见性选项
     */
    const VISIBILITY_PUBLIC = 'public';
    const VISIBILITY_DEPARTMENT = 'department';
    const VISIBILITY_USER = 'user';
    const VISIBILITY_PRIVATE = 'private';

    /**
     * 拥有者类型选项
     */
    const OWNER_TYPE_USER = 'user';
    const OWNER_TYPE_DEPT = 'dept';

    /**
     * 父目录关联
     */
    public function parent()
    {
        return $this->belongsTo(FmsDirectoryModel::class, 'parent_id');
    }

    /**
     * 子目录关联
     */
    public function children()
    {
        return $this->hasMany(FmsDirectoryModel::class, 'parent_id');
    }

    /**
     * acl规则
     */
    public function aclRules()
    {
        return $this->hasMany(FmsAclRuleModel::class, 'target_id')->where('target_type', FmsAclRuleModel::TARGET_TYPE_DIRECTORY);
    }


    /**
     * 所有子目录（递归）
     */
    public function allChildren()
    {
        return $this->children()->with('allChildren');
    }

    /**
     * 目录下的文件
     */
    public function files()
    {
        return $this->hasMany(FmsFileModel::class, 'directory_id');
    }

    /**
     * 更新者关联
     */
    public function updater()
    {
        return $this->belongsTo(\App\Model\TchipBi\UserModel::class, 'updated_by');
    }

    /**
     * 获取完整路径
     */
    public function getFullPathAttribute(): string
    {
        if ($this->parent_id === null) {
            return '/' . $this->id . '/';
        }

        $parent = $this->parent;
        if ($parent) {
            // 去掉父路径末尾的斜杠，避免出现 //，然后在末尾加上本节点 id 和一个 /
            $parentPath = rtrim($parent->full_path, '/');
            return $parentPath . '/' . $this->id . '/';
        }

        return '/' . $this->id . '/';
    }

    /**
     * 检查是否为根目录
     */
    public function isRoot(): bool
    {
        return $this->parent_id === null;
    }

    /**
     * 检查是否有子目录
     */
    public function hasChildren(): bool
    {
        return $this->children()->exists();
    }

    /**
     * 获取目录层级
     */
    public function getLevel(): int
    {
        if ($this->isRoot()) {
            return 0;
        }

        $level = 0;
        $parent = $this->parent;
        while ($parent) {
            $level++;
            $parent = $parent->parent;
        }

        return $level;
    }

    /**
     * 获取所有祖先目录
     */
    public function getAncestors()
    {
        $ancestors = collect();
        $parent = $this->parent;

        while ($parent) {
            $ancestors->prepend($parent);
            $parent = $parent->parent;
        }

        return $ancestors;
    }

    /**
     * 检查是否为指定目录的子目录
     */
    public function isChildOf(FmsDirectoryModel $directory): bool
    {
        $parent = $this->parent;
        while ($parent) {
            if ($parent->id === $directory->id) {
                return true;
            }
            $parent = $parent->parent;
        }

        return false;
    }

    /**
     * 更新路径（在移动目录时使用）
     */
    public function updatePath(): void
    {
        $this->path = $this->getFullPathAttribute();
        $this->save();

        // 递归更新所有子目录的路径
        foreach ($this->children as $child) {
            $child->updatePath();
        }
    }

    /**
     * ACL权限过滤scope
     * 根据用户权限主体过滤目录，只返回有权限的目录或公开目录
     * 
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param array $subjects 用户权限主体信息 ['user_id' => int, 'dept_ids' => array, 'role_ids' => array]
     * @param int $permission 权限位（1=查看，2=编辑，4=删除等）
     * @param int|null $userId 用户ID，用于超管权限检查
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithAcl($query, array $subjects, int $permission = 1, int $userId = null)
    {
        // 检查是否为超级管理员，超管绕过ACL检查
        if ($userId) {
            $authService = \Hyperf\Utils\ApplicationContext::getContainer()->get(\App\Core\Services\AuthService::class);
            if ($authService->isSuper($userId, ['Director'])) {
                return $query; // 超管返回原查询，不做权限过滤
            }
        }

        // 构建权限主体ID数组
        $subjectIds = $subjects;

        return $query->where(function ($q) use ($subjectIds, $permission) {
            // 条件1: 公开目录（没有ACL规则的目录）
            $q->whereDoesntHave('aclRules')
              // 条件2: 有权限的目录（ACL规则中包含用户权限主体且有对应权限位）
              ->orWhereHas('aclRules', function ($aclQuery) use ($subjectIds, $permission) {
                  $aclQuery->whereIn('subject_key_id', $subjectIds)
                           ->whereRaw('(permission_set & ?) > 0', [$permission]);
              });
        });
    }
}