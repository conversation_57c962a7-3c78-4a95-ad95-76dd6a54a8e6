<?php

declare(strict_types=1);

namespace App\Model\TchipBiFms;

use Carbon\Carbon;

/**
 * FMS ACL规则模型
 *
 * @property int $id 主键ID
 * @property string $target_type 授权目标类型：directory/file
 * @property int $target_id 目录或文件ID
 * @property int $subject_key_id 引用fms_subjects.id
 * @property int $permission_set 位掩码权限集细粒度权限集合（"view""upload""delete""share"）
 * @property string $effect 允许/拒绝：allow/deny
 * @property int $priority 规则优先级（越大越优先），用于不同种类规则冲突解决
 * @property Carbon|null $created_at 创建时间
 * @property Carbon|null $updated_at 更新时间
 */
class FmsAclRuleModel extends \App\Model\Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'fms_acl_rules';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'target_type',
        'target_id',
        'subject_key_id',
        'permission_set',
        'effect',
        'priority',
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'target_id' => 'integer',
        'subject_key_id' => 'integer',
        'permission_set' => 'integer',
        'priority' => 'integer',
        'created_at' => 'datetime:Y-m-d H:i',
        'updated_at' => 'datetime:Y-m-d H:i',
    ];

    /**
     * 目标类型选项
     */
    const TARGET_TYPE_DIRECTORY = 'directory';
    const TARGET_TYPE_FILE = 'file';

    /**
     * 效果选项
     */
    const EFFECT_ALLOW = 'allow';
    const EFFECT_DENY = 'deny';

    /**
     * 权限位定义
     */
    const PERMISSION_VIEW = 1;   // 查看权限
    const PERMISSION_UPLOAD = 2; // 上传权限
    const PERMISSION_DELETE = 4; // 删除权限
    const PERMISSION_SHARE = 8;  // 分享权限

    /**
     * 主体关联
     */
    public function subject()
    {
        return $this->belongsTo(FmsSubjectModel::class, 'subject_key_id');
    }

    /**
     * 条件关联
     */
    public function conditions()
    {
        return $this->hasMany(FmsAclConditionModel::class, 'rule_id');
    }

    /**
     * 创建者关联
     */
    public function creator()
    {
        return $this->belongsTo(\App\Model\TchipBi\UserModel::class, 'created_by');
    }

    /**
     * 更新者关联
     */
    public function updater()
    {
        return $this->belongsTo(\App\Model\TchipBi\UserModel::class, 'updated_by');
    }

    /**
     * 检查是否允许效果
     */
    public function isAllow(): bool
    {
        return $this->effect === self::EFFECT_ALLOW;
    }

    /**
     * 检查是否拒绝效果
     */
    public function isDeny(): bool
    {
        return $this->effect === self::EFFECT_DENY;
    }

    /**
     * 检查是否有指定权限
     */
    public function hasPermission(int $permission): bool
    {
        return ($this->permission_set & $permission) === $permission;
    }

    /**
     * 添加权限
     */
    public function addPermission(int $permission): void
    {
        $this->permission_set |= $permission;
    }

    /**
     * 移除权限
     */
    public function removePermission(int $permission): void
    {
        $this->permission_set &= ~$permission;
    }

    /**
     * 获取权限列表
     */
    public function getPermissions(): array
    {
        $permissions = [];
        if ($this->hasPermission(self::PERMISSION_VIEW)) {
            $permissions[] = 'view';
        }
        if ($this->hasPermission(self::PERMISSION_UPLOAD)) {
            $permissions[] = 'upload';
        }
        if ($this->hasPermission(self::PERMISSION_DELETE)) {
            $permissions[] = 'delete';
        }
        if ($this->hasPermission(self::PERMISSION_SHARE)) {
            $permissions[] = 'share';
        }
        return $permissions;
    }
}