<?php

declare(strict_types=1);

namespace App\Model\TchipBiFms;

use Carbon\Carbon;

/**
 * FMS ACL条件模型
 *
 * @property int $id 主键ID
 * @property int $rule_id 关联fms_acl_rules.id
 * @property string $condition_type 条件类型：tag/time/ip
 * @property string $condition_value 条件值（如标签=confidential，时间=2025-01-01~2025-12-31，IP=***********/24）
 */
class FmsAclConditionModel extends \App\Model\Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'fms_acl_conditions';

    /**
     * Indicates if the model should be timestamped.
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'rule_id',
        'condition_type',
        'condition_value',
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'rule_id' => 'integer',
    ];

    /**
     * 条件类型选项
     */
    const CONDITION_TYPE_TAG = 'tag';
    const CONDITION_TYPE_TIME = 'time';
    const CONDITION_TYPE_IP = 'ip';
    const CONDITION_TYPE_FILE_SIZE = 'file_size';
    const CONDITION_TYPE_FILE_TYPE = 'file_type';

    /**
     * 操作符选项
     */
    const OPERATOR_EQ = 'eq';           // 等于
    const OPERATOR_NE = 'ne';           // 不等于
    const OPERATOR_GT = 'gt';           // 大于
    const OPERATOR_LT = 'lt';           // 小于
    const OPERATOR_GTE = 'gte';         // 大于等于
    const OPERATOR_LTE = 'lte';         // 小于等于
    const OPERATOR_IN = 'in';           // 包含
    const OPERATOR_NOT_IN = 'not_in';   // 不包含
    const OPERATOR_LIKE = 'like';       // 模糊匹配
    const OPERATOR_BETWEEN = 'between'; // 区间

    /**
     * 关联的ACL规则
     */
    public function rule()
    {
        return $this->belongsTo(FmsAclRuleModel::class, 'rule_id');
    }

    /**
     * 创建者关联
     */
    public function creator()
    {
        return $this->belongsTo(\App\Model\TchipBi\UserModel::class, 'created_by');
    }

    /**
     * 更新者关联
     */
    public function updater()
    {
        return $this->belongsTo(\App\Model\TchipBi\UserModel::class, 'updated_by');
    }

    /**
     * 检查条件是否匹配
     */
    public function matches($context): bool
    {
        if (!$this->is_active) {
            return true; // 非激活条件默认通过
        }

        $value = $this->getContextValue($context);
        $conditionValue = $this->condition_value;

        switch ($this->operator) {
            case self::OPERATOR_EQ:
                return $value == $conditionValue;

            case self::OPERATOR_NE:
                return $value != $conditionValue;

            case self::OPERATOR_GT:
                return $value > $conditionValue;

            case self::OPERATOR_LT:
                return $value < $conditionValue;

            case self::OPERATOR_GTE:
                return $value >= $conditionValue;

            case self::OPERATOR_LTE:
                return $value <= $conditionValue;

            case self::OPERATOR_IN:
                return in_array($value, (array)$conditionValue);

            case self::OPERATOR_NOT_IN:
                return !in_array($value, (array)$conditionValue);

            case self::OPERATOR_LIKE:
                return strpos($value, $conditionValue) !== false;

            case self::OPERATOR_BETWEEN:
                if (is_array($conditionValue) && count($conditionValue) >= 2) {
                    return $value >= $conditionValue[0] && $value <= $conditionValue[1];
                }
                return false;

            default:
                return false;
        }
    }

    /**
     * 从上下文中获取条件值
     */
    private function getContextValue($context)
    {
        switch ($this->condition_type) {
            case self::CONDITION_TYPE_TAG:
                return $context['tags'] ?? [];

            case self::CONDITION_TYPE_TIME:
                return $context['current_time'] ?? time();

            case self::CONDITION_TYPE_IP:
                return $context['client_ip'] ?? '';

            case self::CONDITION_TYPE_FILE_SIZE:
                return $context['file_size'] ?? 0;

            case self::CONDITION_TYPE_FILE_TYPE:
                return $context['file_type'] ?? '';

            default:
                return null;
        }
    }

    /**
     * 获取条件类型文本
     */
    public function getConditionTypeTextAttribute(): string
    {
        switch ($this->condition_type) {
            case self::CONDITION_TYPE_TAG:
                return '标签';
            case self::CONDITION_TYPE_TIME:
                return '时间';
            case self::CONDITION_TYPE_IP:
                return 'IP地址';
            case self::CONDITION_TYPE_FILE_SIZE:
                return '文件大小';
            case self::CONDITION_TYPE_FILE_TYPE:
                return '文件类型';
            default:
                return '未知';
        }
    }

    /**
     * 获取操作符文本
     */
    public function getOperatorTextAttribute(): string
    {
        switch ($this->operator) {
            case self::OPERATOR_EQ:
                return '等于';
            case self::OPERATOR_NE:
                return '不等于';
            case self::OPERATOR_GT:
                return '大于';
            case self::OPERATOR_LT:
                return '小于';
            case self::OPERATOR_GTE:
                return '大于等于';
            case self::OPERATOR_LTE:
                return '小于等于';
            case self::OPERATOR_IN:
                return '包含';
            case self::OPERATOR_NOT_IN:
                return '不包含';
            case self::OPERATOR_LIKE:
                return '模糊匹配';
            case self::OPERATOR_BETWEEN:
                return '区间';
            default:
                return '未知';
        }
    }
}