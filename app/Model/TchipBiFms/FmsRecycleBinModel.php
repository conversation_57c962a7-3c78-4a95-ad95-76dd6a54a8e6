<?php

declare(strict_types=1);

namespace App\Model\TchipBiFms;

use Carbon\Carbon;

/**
 * FMS回收站模型
 *
 * @property int $id 主键ID
 * @property string $target_type 删除对象类型：directory/file
 * @property int $target_id 对应目录/文件ID
 * @property int $deleted_by 删除者ID
 * @property Carbon $deleted_at 删除时间
 */
class FmsRecycleBinModel extends \App\Model\Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'fms_recycle_bin';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'target_type',
        'target_id',
        'target_name',
        'target_path',
        'target_data',
        'deleted_by',
        'deleted_at',
        'restored_by',
        'restored_at',
        'purged_at',
        'is_purged',
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'target_id' => 'integer',
        'target_data' => 'array',
        'deleted_by' => 'integer',
        'restored_by' => 'integer',
        'is_purged' => 'boolean',
        'deleted_at' => 'datetime:Y-m-d H:i',
        'restored_at' => 'datetime:Y-m-d H:i',
        'purged_at' => 'datetime:Y-m-d H:i',
        'created_at' => 'datetime:Y-m-d H:i',
        'updated_at' => 'datetime:Y-m-d H:i',
    ];

    /**
     * 目标类型选项
     */
    const TARGET_TYPE_DIRECTORY = 'directory';
    const TARGET_TYPE_FILE = 'file';

    /**
     * 删除者关联
     */
    public function deleter()
    {
        return $this->belongsTo(\App\Model\TchipBi\UserModel::class, 'deleted_by');
    }

    /**
     * 恢复者关联
     */
    public function restorer()
    {
        return $this->belongsTo(\App\Model\TchipBi\UserModel::class, 'restored_by');
    }

    /**
     * 检查是否为目录
     */
    public function isDirectory(): bool
    {
        return $this->target_type === self::TARGET_TYPE_DIRECTORY;
    }

    /**
     * 检查是否为文件
     */
    public function isFile(): bool
    {
        return $this->target_type === self::TARGET_TYPE_FILE;
    }

    /**
     * 检查是否已恢复
     */
    public function isRestored(): bool
    {
        return $this->restored_at !== null;
    }

    /**
     * 检查是否已清除
     */
    public function isPurged(): bool
    {
        return $this->is_purged;
    }

    /**
     * 检查是否可以恢复
     */
    public function canRestore(): bool
    {
        return !$this->isRestored() && !$this->isPurged();
    }

    /**
     * 恢复项目
     */
    public function restore(int $restoredBy): bool
    {
        if (!$this->canRestore()) {
            return false;
        }

        // 根据类型恢复对应的模型
        if ($this->isDirectory()) {
            $directory = FmsDirectoryModel::withTrashed()->find($this->target_id);
            if ($directory) {
                $directory->restore();
                $directory->deleted_by = null;
                $directory->save();
            }
        } elseif ($this->isFile()) {
            $file = FmsFileModel::withTrashed()->find($this->target_id);
            if ($file) {
                $file->restore();
                $file->deleted_by = null;
                $file->save();
            }
        }

        // 更新回收站记录
        $this->restored_by = $restoredBy;
        $this->restored_at = Carbon::now();
        $this->save();

        return true;
    }

    /**
     * 永久删除项目
     */
    public function purge(): bool
    {
        if ($this->isPurged()) {
            return false;
        }

        // 根据类型永久删除对应的模型
        if ($this->isDirectory()) {
            $directory = FmsDirectoryModel::withTrashed()->find($this->target_id);
            if ($directory) {
                $directory->forceDelete();
            }
        } elseif ($this->isFile()) {
            $file = FmsFileModel::withTrashed()->find($this->target_id);
            if ($file) {
                // TODO: 删除物理文件
                $file->forceDelete();
            }
        }

        // 更新回收站记录
        $this->is_purged = true;
        $this->purged_at = Carbon::now();
        $this->save();

        return true;
    }

    /**
     * 获取在回收站的天数
     */
    public function getDaysInRecycleBinAttribute(): int
    {
        return $this->deleted_at->diffInDays(Carbon::now());
    }

    /**
     * 获取目标类型文本
     */
    public function getTargetTypeTextAttribute(): string
    {
        switch ($this->target_type) {
            case self::TARGET_TYPE_DIRECTORY:
                return '目录';
            case self::TARGET_TYPE_FILE:
                return '文件';
            default:
                return '未知';
        }
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute(): string
    {
        if ($this->isPurged()) {
            return '已清除';
        } elseif ($this->isRestored()) {
            return '已恢复';
        } else {
            return '回收站中';
        }
    }

    /**
     * 批量恢复
     */
    public static function batchRestore(array $ids, int $restoredBy): int
    {
        $restored = 0;

        foreach ($ids as $id) {
            $item = static::find($id);
            if ($item && $item->restore($restoredBy)) {
                $restored++;
            }
        }

        return $restored;
    }

    /**
     * 批量清除
     */
    public static function batchPurge(array $ids): int
    {
        $purged = 0;

        foreach ($ids as $id) {
            $item = static::find($id);
            if ($item && $item->purge()) {
                $purged++;
            }
        }

        return $purged;
    }

    /**
     * 自动清理过期项目
     */
    public static function autoCleanup(int $daysToKeep = 30): int
    {
        $expiredItems = static::where('deleted_at', '<', Carbon::now()->subDays($daysToKeep))
                             ->where('is_purged', false)
                             ->get();

        $cleaned = 0;
        foreach ($expiredItems as $item) {
            if ($item->purge()) {
                $cleaned++;
            }
        }

        return $cleaned;
    }
}