<?php

declare (strict_types=1);

namespace App\Model\TchipSale;

/**
 * @property int $id
 * @property int $client_id
 * @property string $comy
 * @property string $contact
 * @property string $phone
 * @property string $email
 * @property string $qq
 * @property string $address
 * @property string $notes
 * @property string $technician
 * @property string $technician_phone
 * @property string $client_website
 */
class ClientContactTableModel extends \App\Model\TchipSale\SaleBaseModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'client_contact_table';

    //关闭自动更新时间
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'id',
        'client_id',
        'comy',
        'contact',
        'phone',
        'email',
        'qq',
        'address',
        'notes',
        'technician',
        'technician_phone',
        'client_website'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'client_id' => 'integer'
    ];
}
