<?php

declare (strict_types=1);
namespace App\Model\TchipSale;

/**
 * @property int $id
 * @property int $user_id
 * @property int $client_id
 * @property int $type
 * @property int $ptype
 * @property int $status
 * @property string $username
 * @property string $addtime
 * @property string $description
 */
class ClientProgressTableModel extends \App\Model\TchipSale\SaleBaseModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'client_progress_table';
    //关闭自动更新时间
    public $timestamps = false;
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [];
    
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer', 
        'user_id' => 'integer', 
        'client_id' => 'integer',
        'type' => 'integer', 
        'ptype' => 'integer', 
        'status' => 'integer',
        'addtime' => 'datetime'
    ];
} 