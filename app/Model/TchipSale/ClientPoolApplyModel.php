<?php

declare (strict_types=1);
namespace App\Model\TchipSale;

use Hyperf\Database\Model\SoftDeletes;

/**
 * @property int $id
 * @property int $client_id
 * @property int $apply_user_id
 * @property int $original_user_id
 * @property string $follow_content
 * @property string $apply_content
 * @property string $apply_time
 * @property int $status
 * @property int $audit_user_id
 * @property string $audit_time
 * @property string $audit_remark
 * @property string $created_at
 * @property string $updated_at
 * @property string $deleted_at
 */
class ClientPoolApplyModel extends \App\Model\TchipSale\SaleBaseModel
{
    use SoftDeletes;
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'client_pool_apply';

    const CREATED_AT = 'created_at';

    const UPDATED_AT = 'updated_at';

    const DELETED_AT = 'deleted_at';

    //关闭自动更新时间
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [];
    
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'client_id' => 'integer',
        'apply_user_id' => 'integer',
        'original_user_id' => 'integer',
        'status' => 'integer',
        'audit_user_id' => 'integer',
        'apply_time' => 'datetime',
        'audit_time' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];
} 