<?php

declare(strict_types=1);

namespace App\Model\TchipSale;

/**
 * @property int $id
 * @property int $user_id
 * @property int $part_group_id
 * @property int $group_id
 * @property int $part_id
 * @property int $company_id
 */
class UserMainTableModel extends SaleBaseModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'user_main_table';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'part_group_id',
        'group_id',
        'part_id',
        'company_id'
    ];

    /**
     * 禁用时间戳自动管理（该表没有created_at/updated_at字段）
     */
    public $timestamps = false;

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'user_id' => 'integer',
        'part_group_id' => 'integer',
        'group_id' => 'integer',
        'part_id' => 'integer',
        'company_id' => 'integer'
    ];

    /**
     * 销售系统默认配置
     */
    const DEFAULT_COMPANY_ID = 1001;  // 公司ID
    const DEFAULT_GROUP_ID = 4;       // 销售角色ID
    const DEFAULT_PART_ID = 47;       // 部门ID

    /**
     * 创建用户关联记录
     * @param int $userId
     * @return static
     */
    public static function createUserRelation(int $userId): self
    {
        return self::create([
            'user_id' => $userId,
            'part_group_id' => 0, // 不设置，后续手动设置
            'group_id' => self::DEFAULT_GROUP_ID,
            'part_id' => self::DEFAULT_PART_ID,
            'company_id' => self::DEFAULT_COMPANY_ID
        ]);
    }
}
