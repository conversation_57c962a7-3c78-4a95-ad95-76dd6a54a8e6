<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2023/4/18
 * <AUTHOR> @Description
 *
 */

namespace App\Aspect;

use App\Annotation\CrotabLogsAnnotation;
use App\Core\Monolog\Handler\CrontabLoggerHandler;
use App\Core\Utils\Log;
use Hyperf\Context\Context;
use Hyperf\Di\Aop\ProceedingJoinPoint;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Di\Annotation\Aspect;
use Hyperf\Di\Annotation\Inject;

/**
 * 定时任务　日志
 * Class SubscribeAspect
 * @package App\Aspect
 * @Aspect()
 */
class CrotabLogsAspect extends \Hyperf\Di\Aop\AbstractAspect
{
    public $classes = [];

    public $annotations = [
        CrotabLogsAnnotation::class,
    ];

    /**
     * @Inject()
     * @var RequestInterface
     */
    protected $request;

    /**
     * @inheritDoc
     */
    public function process(ProceedingJoinPoint $proceedingJoinPoint)
    {
        // 切面切入后，执行对应的方法会由此来负责
        // $proceedingJoinPoint 为连接点，通过该类的 process() 方法调用原方法并获得结果
        $metadata = $proceedingJoinPoint->getAnnotationMetadata();
        /** @var CrotabLogsAnnotation $annotation */
        $annotation = $metadata->method[CrotabLogsAnnotation::class];
        $request = \Hyperf\Utils\ApplicationContext::getContainer()->get(\Psr\Http\Message\ServerRequestInterface::class);
        $taskType= $annotation->type;
        Context::set(CrontabLoggerHandler::CrontabTaskName, $taskType);

        // 在调用前进行某些处理 以上
        $result = $proceedingJoinPoint->process();
        // 在调用后进行某些处理 以下
        Context::set(CrontabLoggerHandler::CrontabTaskName, '');
        //
        Log::unsetLogs();


        return $result;
    }


}