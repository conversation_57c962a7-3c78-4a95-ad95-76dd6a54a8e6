<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/4/19 下午5:28
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Aspect;

use App\Annotation\WorkWxTokenAnnotation;
use App\Constants\CommonCode;
use App\Core\Services\WorkWx\WorkWxBaseService;
use Hyperf\Di\Annotation\AnnotationCollector;
use Hyperf\Di\Annotation\Aspect;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Di\Aop\ProceedingJoinPoint;
use App\Constants\StatusCode;
use App\Exception\AppException;

/**
 * Class FirmwareVersionAnnotationAspect
 * @package App\Aspect
 * @Aspect()
 */
class WorkWxTokenAnnotationAspect extends \Hyperf\Di\Aop\AbstractAspect
{

    public $classes = [];

    public $annotations = [
        WorkWxTokenAnnotation::class,
    ];

    /**
     * @Inject()
     * @var WorkWxBaseService
     */
    protected $workWxBaseService;

    /**
     * @inheritDoc
     */
    public function process(ProceedingJoinPoint $proceedingJoinPoint)
    {
        $metadata = $proceedingJoinPoint->getAnnotationMetadata();

        $type = $metadata->method[WorkWxTokenAnnotation::class]->type ?? $metadata->class[WorkWxTokenAnnotation::class]->type;

        if(!$typeData = $this->typeList($type)){
            throw new AppException(StatusCode::ERR_EXCEPTION, 'workwx get token type err:'.$type);
        }

        if(!getCache($typeData['redis_key'])){
            $this->workWxBaseService->getToken($typeData['secret'], $typeData['redis_key']);
        }

        // 切面切入后，执行对应的方法会由此来负责
        // $proceedingJoinPoint 为连接点，通过该类的 process() 方法调用原方法并获得结果
        // 在调用前进行某些处理
        $result = $proceedingJoinPoint->process();
        // 在调用后进行某些处理
        return $result;
    }

    public function typeList($type)
    {
        $items = [
            'agent_tchip_bi' => [
                'appid' => env('AGENT_TCHIP_BI_AGENTID'),
                'secret' => env('AGENT_TCHIP_BI_SECRET'),
                'redis_key' => CommonCode::REDISKEY_WORKWX_AGENT_TCHIP_BI_TOKEN
            ],
            'contact' => [
                'appid' => '',
                'secret' => env('CONTACTS_SECRET'),
                'redis_key' => CommonCode::REDISKEY_WORKWX_CONTACT_TOKEN
            ],
            'approval' => [
                'appid' => env('APPROVAL_AGENTID'),
                'secret' => env('APPROVAL_SECRET'),
                'redis_key' => CommonCode::REDISKEY_WORKWX_APPROVAL_TOKEN
            ]
        ];

        return $items[$type] ?? '';
    }
}