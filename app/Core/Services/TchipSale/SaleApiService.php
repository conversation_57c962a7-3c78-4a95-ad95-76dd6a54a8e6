<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/30 下午4:28
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\TchipSale;

use App\Constants\StatusCode;
use App\Core\Services\BaseService;
use App\Exception\AppException;
use App\Model\TchipBi\UserModel;
use GuzzleHttp\Exception\GuzzleException;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Guzzle\ClientFactory;

class SaleApiService extends BaseService
{
    /**
     * @var \Hyperf\Guzzle\ClientFactory
     */
    private $clientFactory;

    protected $uri;

    public function __construct()
    {
        parent::__construct();
        $options = [
            'base_uri' => env('TCHIP_SALE_API_URL','http://**************:8028/'),
            'timeout'  => 30
        ];
        $this->clientFactory = make(ClientFactory::class)->create($options);
    }

    /**
     * 获取销售系统产品库存数据 (ThinkPHP 3.2)
     * @param array $filter 过滤条件
     * @param array $op 操作符
     * @param int $page 页码
     * @param int $rows 每页条数
     * @return array
     */
    public function getSecureStock($filter = [], $op = [], $page = 1, $rows = 10000)
    {
        // 构建查询参数
        $queryParams = [
            's' => '/securestock/index/json/1',  // ThinkPHP 3.2 路由格式
            'page' => $page,
            'rows' => $rows,
            'sort' => 'NAME', 
            'order' => 'ASC',
            '_' => time() * 1000  // 时间戳，防止缓存
        ];
        
        // 添加过滤条件
        foreach ($filter as $key => $value) {
            $queryParams["filter[{$key}]"] = $value;
        }
        
        // 添加操作符
        foreach ($op as $key => $value) {
            $queryParams["op[{$key}]"] = $value;
        }
        
        // 构建完整URL，注意ThinkPHP 3.2需要特定格式
        $url = 'index.php?' . http_build_query($queryParams, '', '&', PHP_QUERY_RFC3986);
        
        // 发送GET请求，不传递额外options，避免干扰ThinkPHP路由
        $result = $this->sendRequest($url, [], 'GET');
        return $result;
    }

    private function sendRequest($uri, $options, $method = 'get', $header = [])
    {
        try {
            if($header){
                $options['headers'] = array_merge($options['headers'], $header);
            }

            $response = $this->clientFactory->request($method, $uri, $options);

            $responseArr = json_decode($response->getBody()->getContents(), true);
            return $responseArr;
        } catch (GuzzleException $e) {
            if($e->getCode() == '403'){
                throw new AppException(StatusCode::ERR_FORBIDDEN);
            }
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }
}