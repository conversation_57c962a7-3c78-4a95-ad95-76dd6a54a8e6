<?php

namespace App\Core\Services\TchipSale;

use App\Core\Services\WorkWx\ContactChange\UserAccountServiceInterface;
use App\Core\Utils\Log;
use App\Core\Utils\Random;
use App\Constants\SystemAccountConfig;
use App\Model\TchipSale\UserTableModel;
use App\Model\TchipSale\UserMainTableModel;
use App\Model\TchipBi\UserThirdModel;

/**
 * 销售系统用户账号服务
 */
class UserAccountService implements UserAccountServiceInterface
{
    /**
     * 创建用户账号
     */
    public function createUserAccount(array $userInfo): ?array
    {
        $config = SystemAccountConfig::getConfig('tchip_sale');
        if (empty($config)) {
            Log::get()->warning("销售系统配置不存在");
            return null;
        }

        // 检查邮箱
        $userEmail = $userInfo['biz_mail'] ?? $userInfo['email'] ?? '';
        if (empty($userEmail)) {
            Log::get()->warning("用户没有邮箱信息，无法创建销售系统账号", [
                'userid' => $userInfo['userid'],
                'name' => $userInfo['name']
            ]);
            return null;
        }

        try {
            $accountInfo = null;
            \Hyperf\DbConnection\Db::connection('tchip_sale')->transaction(function () use (
                $userEmail, $userInfo, $config, &$accountInfo
            ) {
                $userTableModel = make(UserTableModel::class);
                $existingUser = $userTableModel::query()->where('email', $userEmail)->first();

                if (!$existingUser) {
                    // 创建新账号
                    $result = $this->createNewAccount($userInfo, $userEmail);
                    if ($result) {
                        // 记录到UserThird表
                        $this->recordUserThird($userInfo, $result['user_id']);

                        $accountInfo = array_merge($config, [
                            'username' => $userInfo['userid'],
                            'account_username' => $userInfo['userid'],
                            'password' => $result['password'],
                            'account_password' => $result['password'],
                            'role_name' => $config['default_role'] ?? '销售员',
                            'user_id' => $result['user_id'],
                            'created' => true
                        ]);
                    }
                } else {
                    // 账号已存在，确保UserThird记录存在
                    $this->recordUserThird($userInfo, $existingUser->id);

                    Log::get()->info("销售系统账号已存在，跳过创建", [
                        'email' => $userEmail,
                        'username' => $userInfo['userid'],
                        'existing_id' => $existingUser->id
                    ]);

                    $accountInfo = array_merge($config, [
                        'username' => $userInfo['userid'],
                        'account_username' => $userInfo['userid'],
                        'password' => '***已存在***',
                        'account_password' => '***已存在***',
                        'role_name' => $config['default_role'] ?? '销售员',
                        'user_id' => $existingUser->id,
                        'created' => false
                    ]);
                }
            });

            return $accountInfo;
        } catch (\Exception $e) {
            Log::get()->error("创建销售系统账号失败", [
                'username' => $userInfo['userid'] ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * 禁用用户账号
     */
    public function disableUserAccount(string $email, string $userName): bool
    {
        try {
            $userTableModel = make(UserTableModel::class);
            $user = $userTableModel::query()->where('email', $email)->first();

            if ($user) {
                $user->update(['status' => '0']);

                // 更新UserThird表中的备注信息
                UserThirdModel::query()
                    ->where('third_user_id', $user->id)
                    ->where('platform', 'tchip_sale')
                    ->update(['remark' => '销售管理系统账号(已禁用)']);

                Log::get()->info("成功禁用销售系统账号", [
                    'email' => $email,
                    'username' => $userName,
                    'user_id' => $user->id
                ]);
                return true;
            } else {
                Log::get()->info("销售系统中未找到对应账号", [
                    'email' => $email,
                    'username' => $userName
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::get()->error("禁用销售系统账号失败", [
                'email' => $email,
                'username' => $userName,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 获取系统名称
     */
    public function getSystemName(): string
    {
        return 'Tchip Sale';
    }

    /**
     * 检查是否应该为此用户创建账号
     */
    public function shouldCreateAccount(array $userInfo): bool
    {
        $departments = $userInfo['department'] ?? [];
        if (!is_array($departments)) {
            $departments = json_decode($departments, true) ?: [];
        }

        // 销售系统的触发部门：平台销售中心(2)
        $triggerDepartments = [2];
        return !empty(array_intersect($departments, $triggerDepartments));
    }

    /**
     * 创建新账号的具体逻辑
     */
    private function createNewAccount(array $userInfo, string $userEmail): ?array
    {
        // 生成6位随机密码
        $randomPassword = $this->generateRandomString(6);
        $encryptedPassword = md5($randomPassword);
        $currentTime = time();

        // 创建销售系统用户
        $userTableModel = make(UserTableModel::class);
        $user = $userTableModel::create([
            'username' => $userInfo['name'],
            'realname' => $userInfo['name'],
            'password' => $encryptedPassword,
            'email' => $userEmail,
            'status' => '1',
            'access' => 10,
            'login_count' => 0,
            'last_visit' => 0,
            'date_created' => $currentTime,
            'report' => '0',
            'MailPwd' => '',
            'smtp' => '',
            'ssl' => 0,
            'port' => 0,
            'out_stock_notice' => 0,
            'stock_notice' => 0,
            'sort' => 1000
        ]);

        // 创建用户关联记录
        $userMainTableModel = make(UserMainTableModel::class);
        $userMainTableModel::createUserRelation($user->id);

        Log::get()->info("销售系统-成功创建账号", [
            'username' => $userInfo['userid'],
            'password' => $randomPassword,
            'user_id' => $user->id
        ]);

        return [
            'password' => $randomPassword,
            'user_id' => $user->id
        ];
    }

    /**
     * 记录用户第三方关联信息
     */
    private function recordUserThird(array $userInfo, int $thirdUserId): void
    {
        try {
            // 获取BI系统中的用户ID
            $biUserModel = make(\App\Model\TchipBi\UserModel::class);
            $biUser = $biUserModel::query()->where('workwx_userid', $userInfo['userid'])->first();

            if ($biUser) {
                // 生成API Key（参考Redmine方式）
                $apiKey = Random::alnum(40);

                UserThirdModel::query()->updateOrCreate(
                    [
                        'user_id' => $biUser->id,
                        'platform' => 'tchip_sale'
                    ],
                    [
                        'third_user_id' => $thirdUserId,
                        'api_key' => $apiKey,
                        'remark' => '销售管理系统账号'
                    ]
                );

                Log::get()->info("成功记录销售系统用户关联", [
                    'bi_user_id' => $biUser->id,
                    'sale_user_id' => $thirdUserId,
                    'username' => $userInfo['userid'],
                    'api_key' => $apiKey
                ]);
            }
        } catch (\Exception $e) {
            Log::get()->error("记录销售系统用户关联失败: " . $e->getMessage(), [
                'username' => $userInfo['userid'],
                'third_user_id' => $thirdUserId
            ]);
        }
    }

    /**
     * 生成随机字符串
     */
    private function generateRandomString(int $length = 6): string
    {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $result = '';
        for ($i = 0; $i < $length; $i++) {
            $result .= $chars[mt_rand(0, strlen($chars) - 1)];
        }
        return $result;
    }
}
