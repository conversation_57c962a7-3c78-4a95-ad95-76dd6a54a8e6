<?php

namespace App\Core\Services\TchipSale;

use App\Model\TchipSale\StockOrderlistModel;
use App\Model\TchipSale\ClientTableModel;
use App\Model\TchipSale\ClientContactTableModel;
use App\Core\Services\BusinessService;
use Hyperf\Di\Annotation\Inject;

class StockOrderListService extends BusinessService
{
    /**
     * @Inject
     * @var StockOrderlistModel
     */
    protected $model;

    public function getStockOrderClient($orderCode)
    {
        if (!is_array($orderCode)) {
            $orderCode = [$orderCode];
        }

        if (empty($orderCode)) {
            return [];
        }

        // 使用ORM关联查询直接获取所需数据
        $stockOrders = $this->model::query()
            ->select(['sn', 'client_id'])
            ->with([
                'client:id,name',
                'clientContact:client_id,comy,contact'
            ])
            ->whereIn('sn', $orderCode)
            ->get();
        
        if ($stockOrders->isEmpty()) {
            return [];
        }

        // 组装结果
        $result = [];
        foreach ($stockOrders as $order) {
            $client = $order->client;
            $contact = $order->clientContact;
            
            $clientData = [
                'sn' => $order->sn,
                'client_id' => $order->client_id,
                'username' => $client->name ?? '',
                'comy' => $contact->comy ?? '',
                'contact' => $contact->contact ?? '',
                'display_name' => $this->formatClientName([
                    'username' => $client->name ?? '',
                    'comy' => $contact->comy ?? '',
                    'contact' => $contact->contact ?? ''
                ])
            ];
            
            $result[$order->sn] = $clientData;
        }
        
        return $result;
    }

    /**
     * 格式化客户名称显示
     */
    private function formatClientName($client)
    {
        if (!empty($client['comy']) && !empty($client['contact'])) {
            return $client['comy']." / ".$client['contact'];
        } else if (empty($client['comy']) && empty($client['contact'])) {
            return $client['username'];
        } else if (empty($client['comy'])) {
            return $client['contact'];
        } else {
            return $client['comy'];
        }
    }
}