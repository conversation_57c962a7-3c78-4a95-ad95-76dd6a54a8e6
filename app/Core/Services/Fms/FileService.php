<?php

declare(strict_types=1);

namespace App\Core\Services\Fms;

use App\Core\Services\BusinessService;
use App\Core\Services\Fms\AclService;
use App\Model\TchipBiFms\FmsFileModel;
use App\Model\TchipBiFms\FmsDirectoryModel;
use App\Model\TchipBiFms\FmsRecycleBinModel;
use App\Model\TchipBiFms\FmsAclRuleModel;
use App\Exception\AppException;
use App\Constants\StatusCode;
use Carbon\Carbon;
use Hyperf\DbConnection\Db;
use Hyperf\HttpMessage\Upload\UploadedFile;
use Hyperf\Di\Annotation\Inject;

/**
 * FMS文件服务
 */
class FileService extends BusinessService
{
    /**
     * @Inject()
     * @var FmsFileModel
     */
    protected $model;

    /**
     * ACL权限服务
     * @Inject()
     * @var AclService
     */
    private $aclService;


    /**
     * 获取文件列表
     */
    public function getFileList(?int $directoryId = null, string $search = '', int $page = 1, int $pageSize = 20): array
    {
        $query = FmsFileModel::query()
            ->where('is_deleted', 0)
            ->orderBy('created_at', 'desc');

        // 按目录筛选
        if ($directoryId !== null) {
            $query->where('directory_id', $directoryId);
        }

        // 搜索
        if (!empty($search)) {
            $query->where('name', 'like', '%' . $search . '%');
        }

        // 分页
        $total = $query->count();
        $files = $query->offset(($page - 1) * $pageSize)
            ->limit($pageSize)
            ->get()
            ->toArray();

        return [
            'data' => $files,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize,
        ];
    }

    /**
     * 上传文件
     */
    public function uploadFile(UploadedFile $uploadedFile, int $directoryId, array $options = []): FmsFileModel
    {
        // 验证文件
        $this->validateUploadedFile($uploadedFile);

        // 生成文件信息
        $originalName = $uploadedFile->getClientFilename();
        $mimeType = $uploadedFile->getClientMediaType();
        $fileSize = $uploadedFile->getSize();
        $fileHash = $this->calculateFileHash($uploadedFile);


        // 生成存储路径
        $storagePath = $this->generateStoragePath($originalName, $fileHash);

        // 移动文件到存储位置
        $uploadedFile->moveTo($storagePath);

        chmod($storagePath, 0666);

        // 创建文件记录
        $fileData = [
            'directory_id' => $directoryId,
            'name' => $options['name'] ?? pathinfo($originalName, PATHINFO_FILENAME),
            'path' => $storagePath,
            'size' => $fileSize,
            'mime_type' => $mimeType,
            'version' => 1,
            'visibility' => $options['visibility'] ?? 'private',
            'sort_order' => 0,
            'is_deleted' => 0,
            'download_count' => 0,
            'created_by' => $this->getCurrentUserId(),
            'created_at' => Carbon::now(),
        ];

        $file = FmsFileModel::create($fileData);

        // 设置ACL权限（如果提供了权限配置）
        if (!empty($options['permissions'])) {
            $this->setFilePermissions($file->id, $options['permissions']);
        }

        return $file;
    }

    /**
     * 更新文件信息
     */
    public function updateFile(int $id, array $data): FmsFileModel
    {
        $file = FmsFileModel::findOrFail($id);

        $oldData = $file->toArray();

        // 如果修改名称，检查同目录下是否有重名文件
        if (isset($data['name']) && $data['name'] !== $file->name) {
            $exists = FmsFileModel::where('directory_id', $file->directory_id)
                            ->where('name', $data['name'])
                            ->where('id', '!=', $id)
                            ->exists();
            if ($exists) {
                throw new AppException(StatusCode::ERR_SERVER, '文件名称已存在');
            }
        }

        $data['updated_by'] = $this->getCurrentUserId();
        $data['updated_at'] = Carbon::now();

        $file->update($data);



        return $file;
    }

    /**
     * 删除文件（软删除）
     */
    public function deleteFile(int $id): bool
    {
        $file = FmsFileModel::findOrFail($id);

        return Db::transaction(function () use ($file) {
            // 记录到回收站
            FmsRecycleBinModel::create([
                'target_type' => 'file',
                'target_id' => $file->id,
                'target_name' => $file->name,
                'target_path' => $file->directory->full_path . '/' . $file->name,
                'target_data' => $file->toArray(),
                'deleted_by' => $this->getCurrentUserId(),
                'deleted_at' => Carbon::now(),
            ]);

            // 软删除
            $file->deleted_by = $this->getCurrentUserId();
            $file->save();
            $file->delete();



            return true;
        });
    }

    /**
     * 移动文件
     */
    public function moveFile(int $id, int $newDirectoryId): FmsFileModel
    {
        $file = FmsFileModel::findOrFail($id);
        $newDirectory = FmsDirectoryModel::findOrFail($newDirectoryId);



        // 检查新位置是否有同名文件
        $exists = FmsFileModel::where('directory_id', $newDirectoryId)
                        ->where('name', $file->name)
                        ->where('id', '!=', $id)
                        ->exists();
        if ($exists) {
            throw new AppException(StatusCode::ERR_SERVER, '目标位置已存在同名文件');
        }

        $oldData = $file->toArray();

        $file->directory_id = $newDirectoryId;
        $file->updated_by = $this->getCurrentUserId();
        $file->save();



        return $file;
    }

    /**
     * 复制文件
     */
    public function copyFile(int $id, int $targetDirectoryId, ?string $newName = null): FmsFileModel
    {
        $sourceFile = FmsFileModel::findOrFail($id);
        $targetDirectory = FmsDirectoryModel::findOrFail($targetDirectoryId);



        $fileName = $newName ?: $sourceFile->name;

        // 检查目标位置是否有同名文件
        $exists = FmsFileModel::where('directory_id', $targetDirectoryId)
                        ->where('name', $fileName)
                        ->exists();
        if ($exists) {
            throw new AppException(StatusCode::ERR_SERVER, '目标位置已存在同名文件');
        }

        // 复制物理文件
        $newStoragePath = $this->copyPhysicalFile($sourceFile->path);

        // 创建新文件记录
        $newFileData = $sourceFile->toArray();
        unset($newFileData['id'], $newFileData['created_at'], $newFileData['updated_at']);

        $newFileData['directory_id'] = $targetDirectoryId;
        $newFileData['name'] = $fileName;
        $newFileData['path'] = $newStoragePath;
        $newFileData['created_by'] = $this->getCurrentUserId();
        $newFileData['created_at'] = Carbon::now();

        $newFile = FmsFileModel::create($newFileData);



        return $newFile;
    }



    /**
     * 下载文件
     */
    public function downloadFile(int $id): array
    {
        $file = FmsFileModel::findOrFail($id);



        // 检查文件是否存在
        if (!file_exists($file->path)) {
            throw new AppException(StatusCode::ERR_SERVER, '文件不存在');
        }

        // 记录操作日志
        // $this->logOperation('download', 'file', $file->id, $file->name);

        return [
            'file_path' => $file->path,
            'name' => $file->name,
            'mime_type' => $file->mime_type,
            'size' => $file->size,
        ];
    }

    /**
     * 验证上传文件
     */
    private function validateUploadedFile(UploadedFile $file): void
    {
        if (!$file->isValid()) {
            throw new AppException(StatusCode::ERR_SERVER, '文件上传失败');
        }

        // 检查文件大小
        $maxSize = config('upload.max_size', 100 * 1024 * 1024); // 默认100MB
        if ($file->getSize() > $maxSize) {
            throw new AppException(StatusCode::ERR_SERVER, '文件大小超出限制');
        }

        // 检查文件类型
        $allowedTypes = config('upload.allowed_types', []);
        if (!empty($allowedTypes)) {
            $extension = pathinfo($file->getClientFilename(), PATHINFO_EXTENSION);
            if (!in_array(strtolower($extension), $allowedTypes)) {
                throw new AppException(StatusCode::ERR_SERVER, '不支持的文件类型');
            }
        }
    }

    /**
     * 计算文件哈希值（前 16 个十六进制字符，32 位）
     * 理论碰撞概率：16 位 → 1/2^64 ≈ 1.8×10^-20，文件量级 < 亿级可放心用
     */
    private function calculateFileHash(UploadedFile $file): string
    {
        return substr(hash_file('sha256', $file->getRealPath()), 0, 16);
    }

    /**
     * 生成存储路径
     * @param string $originalName 原文件名
     * @param string $hash 文件的哈希值
     * @return string 完整的存储路径
     */
    private function generateStoragePath(string $originalName, string $hash): string
    {
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $directory = date('Y-m');
        $filename = date('Ymd') . '-' . $hash . ($extension ? '.' . $extension : '');

        $storagePath = storage_path('app/fms/' . $directory);
        if (!is_dir($storagePath)) {
            mkdir($storagePath, 0777, true);
        }

        return $storagePath . '/' . $filename;
    }


    /**
     * 复制物理文件
     */
    private function copyPhysicalFile(string $sourcePath): string
    {
        $hash = hash_file('sha256', $sourcePath);
        $extension = pathinfo($sourcePath, PATHINFO_EXTENSION);
        $newPath = $this->generateStoragePath(basename($sourcePath), $hash);

        if (!copy($sourcePath, $newPath)) {
            throw new AppException(StatusCode::ERR_SERVER, '文件复制失败');
        }

        return $newPath;
    }

    /**
     * 提取文件元数据
     */
    private function extractMetadata(UploadedFile $file, string $mimeType): array
    {
        $metadata = [];

        // 图片文件元数据
        if (strpos($mimeType, 'image/') === 0) {
            $imageInfo = getimagesize($file->getRealPath());
            if ($imageInfo) {
                $metadata['width'] = $imageInfo[0];
                $metadata['height'] = $imageInfo[1];
                $metadata['type'] = $imageInfo['mime'];
            }
        }

        return $metadata;
    }



    /**
     * 设置文件权限
     *
     * @param int $fileId 文件ID
     * @param array $permissions 权限配置数组
     * @return array 创建的规则数组
     */
    public function setFilePermissions(int $fileId, array $permissions): array
    {
        return $this->aclService->setPermissions(
            FmsAclRuleModel::TARGET_TYPE_FILE,
            $fileId,
            $permissions
        );
    }

    /**
     * 检查文件权限
     *
     * @param int $fileId 文件ID
     * @param int $userId 用户ID
     * @param int $permission 权限类型
     * @param array $context 上下文信息
     * @return bool
     */
    public function checkFilePermission(int $fileId, int $userId, int $permission, array $context = []): bool
    {
        return $this->aclService->checkPermission(
            FmsAclRuleModel::TARGET_TYPE_FILE,
            $fileId,
            $userId,
            $permission,
            $context
        );
    }

    /**
     * 获取文件权限规则
     *
     * @param int $fileId 文件ID
     */
    public function getFilePermissions(int $fileId)
    {
        return $this->aclService->getTargetRules(
            FmsAclRuleModel::TARGET_TYPE_FILE,
            $fileId
        );
    }

    /**
     * 清除文件权限
     *
     * @param int $fileId 文件ID
     * @return bool
     */
    public function clearFilePermissions(int $fileId): bool
    {
        return $this->aclService->clearTargetPermissions(
            FmsAclRuleModel::TARGET_TYPE_FILE,
            $fileId
        );
    }

    /**
     * 复制文件权限
     *
     * @param int $sourceFileId 源文件ID
     * @param int $targetFileId 目标文件ID
     * @return array 复制的规则数组
     */
    public function copyFilePermissions(int $sourceFileId, int $targetFileId): array
    {
        return $this->aclService->copyPermissions(
            FmsAclRuleModel::TARGET_TYPE_FILE,
            $sourceFileId,
            FmsAclRuleModel::TARGET_TYPE_FILE,
            $targetFileId
        );
    }

    /**
     * 获取用户对文件的有效权限
     *
     * @param int $fileId 文件ID
     * @param int $userId 用户ID
     * @param array $context 上下文信息
     * @return array
     */
    public function getUserFilePermissions(int $fileId, int $userId, array $context = []): array
    {
        return $this->aclService->getEffectivePermissions(
            FmsAclRuleModel::TARGET_TYPE_FILE,
            $fileId,
            $userId,
            $context
        );
    }

    /**
     * 获取当前用户ID（临时实现）
     */
    private function getCurrentUserId(): int
    {
        // TODO: 从认证上下文获取当前用户ID
        return 1;
    }
}