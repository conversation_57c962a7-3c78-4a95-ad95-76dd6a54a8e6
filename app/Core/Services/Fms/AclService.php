<?php

declare(strict_types=1);

namespace App\Core\Services\Fms;

use App\Core\Services\BusinessService;
use App\Model\TchipBiFms\FmsSubjectModel;
use App\Model\TchipBiFms\FmsAclRuleModel;
use App\Model\TchipBiFms\FmsAclConditionModel;
use App\Model\TchipBi\UserModel;
use App\Model\TchipBi\UserDepartmentModel;
use App\Exception\AppException;
use App\Constants\StatusCode;
use Carbon\Carbon;
use Hyperf\Database\Model\Builder;
use Hyperf\Database\Model\Model;
use Hyperf\DbConnection\Db;
use Qbhy\HyperfAuth\AuthManager;

/**
 * FMS ACL权限服务
 * 
 * 负责处理文件管理系统的访问控制列表(ACL)权限管理
 * 包括主体管理、权限规则管理、权限条件管理和权限验证
 */
class AclService extends BusinessService
{
    protected $model = FmsAclRuleModel::class;

    /**
     * 创建或获取权限主体
     * 
     * @param string $type 主体类型：user/dept/role
     * @param int $subjectId 主体ID
     * @param string|null $name 主体名称
     * @return Model
     */
    public function createOrGetSubject(string $type, int $subjectId, ?string $name = null)
    {
        // 验证主体类型
        if (!in_array($type, [FmsSubjectModel::SUBJECT_TYPE_USER, FmsSubjectModel::SUBJECT_TYPE_DEPT, FmsSubjectModel::SUBJECT_TYPE_ROLE])) {
            throw new AppException(StatusCode::ERR_SERVER, '无效的主体类型');
        }

        // 查找现有主体
        $subject = FmsSubjectModel::where('subject_type', $type)
            ->where('subject_id', $subjectId)
            ->first();

        if ($subject) {
            return $subject;
        }

        // 创建新主体
        return FmsSubjectModel::create([
            'subject_type' => $type,
            'subject_id' => $subjectId,
            // 'subject_name' => $name,
            'created_by' => $this->getCurrentUserId(),
            'created_at' => Carbon::now(),
        ]);
    }

    /**
     * 批量创建权限主体
     * 
     * @param array $userIds 用户ID数组
     * @param array $deptIds 部门ID数组
     * @param array $roleIds 角色ID数组
     * @return array 创建的主体数组
     */
    public function createSubjectsBatch(array $userIds = [], array $deptIds = [], array $roleIds = []): array
    {
        $subjects = [];

        // 创建用户主体
        foreach ($userIds as $userId) {
            $subjects[] = $this->createOrGetSubject(FmsSubjectModel::SUBJECT_TYPE_USER, $userId);
        }

        // 创建部门主体
        foreach ($deptIds as $deptId) {
            $subjects[] = $this->createOrGetSubject(FmsSubjectModel::SUBJECT_TYPE_DEPT, $deptId);
        }

        // 创建角色主体
        foreach ($roleIds as $roleId) {
            $subjects[] = $this->createOrGetSubject(FmsSubjectModel::SUBJECT_TYPE_ROLE, $roleId);
        }

        return $subjects;
    }

    /**
     * 创建ACL权限规则
     * 
     * @param string $targetType 目标类型：directory/file
     * @param int $targetId 目标ID
     * @param int $subjectKeyId 主体ID
     * @param int $permissionSet 权限集合（位掩码）
     * @param string $effect 效果：allow/deny
     * @param int $priority 优先级
     * @param array $conditions 条件数组
     * @return FmsAclRuleModel
     */
    public function createAclRule(
        string $targetType,
        int $targetId,
        int $subjectKeyId,
        int $permissionSet = FmsAclRuleModel::PERMISSION_VIEW,
        string $effect = FmsAclRuleModel::EFFECT_ALLOW,
        int $priority = 100,
        array $conditions = []
    ): FmsAclRuleModel {
        // 验证参数
        if (!in_array($targetType, [FmsAclRuleModel::TARGET_TYPE_DIRECTORY, FmsAclRuleModel::TARGET_TYPE_FILE])) {
            throw new AppException(StatusCode::ERR_SERVER, '无效的目标类型');
        }

        if (!in_array($effect, [FmsAclRuleModel::EFFECT_ALLOW, FmsAclRuleModel::EFFECT_DENY])) {
            throw new AppException(StatusCode::ERR_SERVER, '无效的效果类型');
        }

        // 验证主体是否存在
        $subject = FmsSubjectModel::find($subjectKeyId);
        if (!$subject) {
            throw new AppException(StatusCode::ERR_SERVER, '权限主体不存在');
        }

        return Db::transaction(function () use ($targetType, $targetId, $subjectKeyId, $permissionSet, $effect, $priority, $conditions) {
            // 创建ACL规则
            $rule = FmsAclRuleModel::create([
                'target_type' => $targetType,
                'target_id' => $targetId,
                'subject_key_id' => $subjectKeyId,
                'permission_set' => $permissionSet,
                'effect' => $effect,
                'priority' => $priority,
                'created_by' => $this->getCurrentUserId(),
                'created_at' => Carbon::now(),
            ]);

            // 创建条件
            foreach ($conditions as $condition) {
                $this->createAclCondition($rule->id, $condition);
            }

            return $rule;
        });
    }

    /**
     * 创建ACL条件
     * 
     * @param int $ruleId 规则ID
     * @param array $conditionData 条件数据
     * @return FmsAclConditionModel
     */
    public function createAclCondition(int $ruleId, array $conditionData): FmsAclConditionModel
    {
        $requiredFields = ['condition_type', 'condition_value'];
        foreach ($requiredFields as $field) {
            if (!isset($conditionData[$field])) {
                throw new AppException(StatusCode::ERR_SERVER, "缺少必需字段: {$field}");
            }
        }

        return FmsAclConditionModel::create([
            'rule_id' => $ruleId,
            'condition_type' => $conditionData['condition_type'],
            'condition_value' => $conditionData['condition_value'],
            'operator' => $conditionData['operator'] ?? FmsAclConditionModel::OPERATOR_EQ,
            'created_by' => $this->getCurrentUserId(),
            'created_at' => Carbon::now(),
        ]);
    }

    /**
     * 为目录或文件设置权限
     * 
     * @param string $targetType 目标类型：directory/file
     * @param int $targetId 目标ID
     * @param array $permissions 权限配置数组
     * @return array 创建的规则数组
     */
    public function setPermissions(string $targetType, int $targetId, array $permissions): array
    {
        $rules = [];

        foreach ($permissions as $permission) {
            // 确保主体存在
            $subject = $this->createOrGetSubject(
                $permission['subject_type'],
                $permission['subject_id'],
                $permission['subject_name'] ?? null
            );

            // 创建权限规则
            $rule = $this->createAclRule(
                $targetType,
                $targetId,
                $subject->id,
                $permission['permission_set'],
                $permission['effect'] ?? FmsAclRuleModel::EFFECT_ALLOW,
                $permission['priority'] ?? 100,
                $permission['conditions'] ?? []
            );

            $rules[] = $rule;
        }

        return $rules;
    }

    /**
     * 获取主体名称
     * 
     * @param string $type 主体类型
     * @param int $subjectId 主体ID
     * @return string
     */
    private function getSubjectName(string $type, int $subjectId): string
    {
        switch ($type) {
            case FmsSubjectModel::SUBJECT_TYPE_USER:
                $user = UserModel::find($subjectId);
                return $user ? $user->name : "用户_{$subjectId}";

            case FmsSubjectModel::SUBJECT_TYPE_DEPT:
                $dept = UserDepartmentModel::find($subjectId);
                return $dept ? $dept->name : "部门_{$subjectId}";

            case FmsSubjectModel::SUBJECT_TYPE_ROLE:
                // 这里需要根据实际的角色模型来获取名称
                return "角色_{$subjectId}";

            default:
                return "未知_{$subjectId}";
        }
    }

    /**
     * 检查权限
     *
     * @param string $targetType 目标类型：directory/file
     * @param int $targetId 目标ID
     * @param int $userId 用户ID
     * @param int $permission 要检查的权限
     * @param array $context 上下文信息（IP、时间、标签等）
     * @return bool
     */
    public function checkPermission(string $targetType, int $targetId, int $userId, int $permission, array $context = []): bool
    {
        // 获取用户相关的所有主体
        $subjects = $this->getUserSubjects($userId);

        if (empty($subjects)) {
            return false;
        }

        // 获取适用的规则
        $rules = $this->getApplicableRules($targetType, $targetId, $subjects, $context);

        // 按优先级排序（优先级高的先处理）
        $rules = $rules->sortByDesc('priority');

        // 默认拒绝
        $hasPermission = false;

        foreach ($rules as $rule) {
            // 检查规则是否有指定权限
            if (!$rule->hasPermission($permission)) {
                continue;
            }

            // 如果是拒绝规则，直接返回false
            if ($rule->isDeny()) {
                return false;
            }

            // 如果是允许规则，标记为有权限
            if ($rule->isAllow()) {
                $hasPermission = true;
            }
        }

        return $hasPermission;
    }

    /**
     * 获取用户相关的所有主体
     *
     * @param int $userId 用户ID
     * @return array
     */
    public function getUserSubjects(int $userId): array
    {
        $subjects = [];

        // 用户本身
        $userSubject = FmsSubjectModel::where('subject_type', FmsSubjectModel::SUBJECT_TYPE_USER)
            ->where('subject_id', $userId)
            ->first();
        if ($userSubject) {
            $subjects[] = $userSubject->id;
        }

        // 获取用户所属部门
        $user = UserModel::find($userId);
        if ($user && $user->department) {
            $departments = is_array($user->department) ? $user->department : [$user->department];
            foreach ($departments as $deptId) {
                $deptSubject = FmsSubjectModel::where('subject_type', FmsSubjectModel::SUBJECT_TYPE_DEPT)
                    ->where('subject_id', $deptId)
                    ->first();
                if ($deptSubject) {
                    $subjects[] = $deptSubject->id;
                }
            }
        }

        // TODO: 获取用户角色（需要根据实际的角色系统实现）

        return $subjects;
    }

    /**
     * 获取适用的规则
     *
     * @param string $targetType 目标类型
     * @param int $targetId 目标ID
     * @param array $subjectIds 主体ID数组
     * @param array $context 上下文
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getApplicableRules(string $targetType, int $targetId, array $subjectIds, array $context)
    {
        $query = FmsAclRuleModel::with(['conditions'])
            ->where('target_type', $targetType)
            ->where('target_id', $targetId)
            ->whereIn('subject_id', $subjectIds);

        $rules = $query->get();

        // 过滤满足条件的规则
        return $rules->filter(function ($rule) use ($context) {
            return $this->checkRuleConditions($rule, $context);
        });
    }

    /**
     * 检查规则条件
     *
     * @param FmsAclRuleModel $rule 规则
     * @param array $context 上下文
     * @return bool
     */
    private function checkRuleConditions(FmsAclRuleModel $rule, array $context): bool
    {
        // 如果没有条件，规则适用
        if ($rule->conditions->isEmpty()) {
            return true;
        }

        // 所有条件都必须满足
        foreach ($rule->conditions as $condition) {
            if (!$condition->matches($context)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取目标的所有权限规则
     *
     * @param string $targetType 目标类型
     * @param int $targetId 目标ID
     *
     */
    public function getTargetRules(string $targetType, int $targetId)
    {
        return FmsAclRuleModel::with(['subject', 'conditions'])
            ->where('target_type', $targetType)
            ->where('target_id', $targetId)
            ->orderBy('priority', 'desc')
            ->get();
    }

    /**
     * 获取多个目标的权限规则
     *
     * @param string $targetType 目标类型
     * @param array<int> $targetIds 目标ID数组
     */
    public function getTargetsRules(string $targetType, array $targetIds)
    {
        return FmsAclRuleModel::with(['subject', 'conditions'])
            ->where('target_type', $targetType)
            ->whereIn('target_id', $targetIds)
            ->orderBy('priority', 'desc')
            ->get()
            ->groupBy('target_id') // 按 target_id 分组，方便后续处理
            ->toArray() ?? [];
    }


    /**
     * 删除权限规则
     *
     * @param int $ruleId 规则ID
     * @return bool
     */
    public function deleteRule(int $ruleId): bool
    {
        return Db::transaction(function () use ($ruleId) {
            $rule = FmsAclRuleModel::findOrFail($ruleId);

            // 删除相关条件
            FmsAclConditionModel::where('rule_id', $ruleId)->delete();

            // 删除规则
            return $rule->delete();
        });
    }

    /**
     * 更新权限规则
     *
     * @param int $ruleId 规则ID
     * @param array $data 更新数据
     * @return FmsAclRuleModel
     */
    public function updateRule(int $ruleId, array $data): FmsAclRuleModel
    {
        $rule = FmsAclRuleModel::findOrFail($ruleId);

        $data['updated_by'] = $this->getCurrentUserId();
        $data['updated_at'] = Carbon::now();

        $rule->update($data);

        return $rule;
    }

    /**
     * 清除目标的所有权限
     *
     * @param string $targetType 目标类型
     * @param int $targetId 目标ID
     * @return bool
     */
    public function clearTargetPermissions(string $targetType, int $targetId): bool
    {
        return Db::transaction(function () use ($targetType, $targetId) {
            $rules = FmsAclRuleModel::where('target_type', $targetType)
                ->where('target_id', $targetId)
                ->get();
            var_dump('---');
            var_dump($rules);

            foreach ($rules as $rule) {
                // 删除条件
                FmsAclConditionModel::where('rule_id', $rule->id)->delete();
                // 删除规则
                $rule->delete();
            }

            return true;
        });
    }

    /**
     * 获取主体列表
     *
     * @param array $filter 过滤条件
     * @param array $op 操作符
     * @param string $sort 排序字段
     * @param string $order 排序方向
     * @param int $limit 分页大小
     * @return mixed
     */
    public function getSubjectList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        $query = FmsSubjectModel::query();

        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit, $query);

        return $query->orderBy($sort, $order)->paginate($limit);
    }

    /**
     * 获取主体详情
     *
     * @param int $subjectId 主体ID
     * @return FmsSubjectModel|null
     */
    public function getSubjectDetail(int $subjectId): ?FmsSubjectModel
    {
        return FmsSubjectModel::with(['aclRules.conditions'])->find($subjectId);
    }

    /**
     * 更新主体信息
     *
     * @param int $subjectId 主体ID
     * @param array $data 更新数据
     * @return FmsSubjectModel
     */
    public function updateSubject(int $subjectId, array $data): FmsSubjectModel
    {
        $subject = FmsSubjectModel::findOrFail($subjectId);

        $data['updated_by'] = $this->getCurrentUserId();
        $data['updated_at'] = Carbon::now();

        $subject->update($data);

        return $subject;
    }

    /**
     * 删除主体（同时删除相关规则）
     *
     * @param int $subjectId 主体ID
     * @return bool
     */
    public function deleteSubject(int $subjectId): bool
    {
        return Db::transaction(function () use ($subjectId) {
            $subject = FmsSubjectModel::findOrFail($subjectId);

            // 获取相关的规则
            $rules = FmsAclRuleModel::where('subject_key_id', $subjectId)->get();

            // 删除规则的条件
            foreach ($rules as $rule) {
                FmsAclConditionModel::where('rule_id', $rule->id)->delete();
            }

            // 删除规则
            FmsAclRuleModel::where('subject_key_id', $subjectId)->delete();

            // 删除主体
            return $subject->delete();
        });
    }

    /**
     * 根据类型和ID查找主体
     *
     * @param string $type 主体类型
     * @param int $subjectId 主体ID
     * @return FmsSubjectModel|null
     */
    public function findSubject(string $type, int $subjectId): ?FmsSubjectModel
    {
        return FmsSubjectModel::where('subject_type', $type)
            ->where('subject_id', $subjectId)
            ->first();
    }

    /**
     * 获取用户的权限摘要
     *
     * @param int $userId 用户ID
     * @param string|null $targetType 目标类型过滤
     * @return array
     */
    public function getUserPermissionSummary(int $userId, ?string $targetType = null): array
    {
        $subjects = $this->getUserSubjects($userId);

        if (empty($subjects)) {
            return [];
        }

        $query = FmsAclRuleModel::with(['subject', 'conditions'])
            ->whereIn('subject_id', $subjects);

        if ($targetType) {
            $query->where('target_type', $targetType);
        }

        $rules = $query->orderBy('priority', 'desc')->get();

        $summary = [];
        foreach ($rules as $rule) {
            $key = $rule->target_type . '_' . $rule->target_id;
            if (!isset($summary[$key])) {
                $summary[$key] = [
                    'target_type' => $rule->target_type,
                    'target_id' => $rule->target_id,
                    'permissions' => [],
                    'rules' => []
                ];
            }

            $summary[$key]['rules'][] = [
                'id' => $rule->id,
                'subject' => $rule->subject->subject_name,
                'subject_type' => $rule->subject->subject_type,
                'permissions' => $rule->getPermissions(),
                'effect' => $rule->effect,
                'priority' => $rule->priority,
                'conditions_count' => $rule->conditions->count()
            ];

            // 合并权限
            if ($rule->isAllow()) {
                $summary[$key]['permissions'] = array_unique(array_merge(
                    $summary[$key]['permissions'],
                    $rule->getPermissions()
                ));
            }
        }

        return array_values($summary);
    }

    /**
     * 复制权限规则
     *
     * @param string $sourceTargetType 源目标类型
     * @param int $sourceTargetId 源目标ID
     * @param string $destTargetType 目标类型
     * @param int $destTargetId 目标ID
     * @return array 复制的规则数组
     */
    public function copyPermissions(string $sourceTargetType, int $sourceTargetId, string $destTargetType, int $destTargetId): array
    {
        $sourceRules = FmsAclRuleModel::with(['conditions'])
            ->where('target_type', $sourceTargetType)
            ->where('target_id', $sourceTargetId)
            ->get();

        $copiedRules = [];

        foreach ($sourceRules as $sourceRule) {
            $newRule = $this->createAclRule(
                $destTargetType,
                $destTargetId,
                $sourceRule->subject_id,
                $sourceRule->permission_set,
                $sourceRule->effect,
                $sourceRule->priority,
                $sourceRule->conditions->map(function ($condition) {
                    return [
                        'condition_type' => $condition->condition_type,
                        'condition_value' => $condition->condition_value,
                        'operator' => $condition->operator,
                    ];
                })->toArray()
            );

            $copiedRules[] = $newRule;
        }

        return $copiedRules;
    }

    /**
     * 获取权限规则列表
     *
     * @param array $filter 过滤条件
     * @param array $op 操作符
     * @param string $sort 排序字段
     * @param string $order 排序方向
     * @param int $limit 分页大小
     * @return mixed
     */
    public function getRuleList(array $filter = [], array $op = [], string $sort = 'priority', string $order = 'DESC', int $limit = 10)
    {
        $query = FmsAclRuleModel::with(['subject', 'conditions']);

        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit, $query);

        return $query->orderBy($sort, $order)->paginate($limit);
    }

    /**
     * 获取权限规则详情
     *
     * @param int $ruleId 规则ID
     * @return FmsAclRuleModel|null
     */
    public function getRuleDetail(int $ruleId): ?FmsAclRuleModel
    {
        return FmsAclRuleModel::with(['subject', 'conditions'])->find($ruleId);
    }

    /**
     * 批量创建权限规则
     *
     * @param array $rulesData 规则数据数组
     * @return array 创建的规则数组
     */
    public function createRulesBatch(array $rulesData): array
    {
        $rules = [];

        foreach ($rulesData as $ruleData) {
            $rule = $this->createAclRule(
                $ruleData['target_type'],
                $ruleData['target_id'],
                $ruleData['subject_id'],
                $ruleData['permission_set'],
                $ruleData['effect'] ?? FmsAclRuleModel::EFFECT_ALLOW,
                $ruleData['priority'] ?? 100,
                $ruleData['conditions'] ?? []
            );

            $rules[] = $rule;
        }

        return $rules;
    }

    /**
     * 批量删除权限规则
     *
     * @param array $ruleIds 规则ID数组
     * @return bool
     */
    public function deleteRulesBatch(array $ruleIds): bool
    {
        return Db::transaction(function () use ($ruleIds) {
            // 删除条件
            FmsAclConditionModel::whereIn('rule_id', $ruleIds)->delete();

            // 删除规则
            return FmsAclRuleModel::whereIn('id', $ruleIds)->delete() > 0;
        });
    }

    /**
     * 更新规则优先级
     *
     * @param array $priorities 规则ID和优先级的映射数组 [rule_id => priority]
     * @return bool
     */
    public function updateRulePriorities(array $priorities): bool
    {
        return Db::transaction(function () use ($priorities) {
            foreach ($priorities as $ruleId => $priority) {
                FmsAclRuleModel::where('id', $ruleId)->update([
                    'priority' => $priority,
                    'updated_by' => $this->getCurrentUserId(),
                    'updated_at' => Carbon::now()
                ]);
            }
            return true;
        });
    }

    /**
     * 启用/禁用权限规则
     *
     * @param int $ruleId 规则ID
     * @param bool $isActive 是否启用
     * @return FmsAclRuleModel
     */
    public function toggleRuleStatus(int $ruleId, bool $isActive): FmsAclRuleModel
    {
        $rule = FmsAclRuleModel::findOrFail($ruleId);

        $rule->update([
            'updated_by' => $this->getCurrentUserId(),
            'updated_at' => Carbon::now()
        ]);

        return $rule;
    }

    /**
     * 获取权限位的文本描述
     *
     * @param int $permissionSet 权限集合
     * @return array
     */
    public function getPermissionTexts(int $permissionSet): array
    {
        $permissions = [];

        if ($permissionSet & FmsAclRuleModel::PERMISSION_VIEW) {
            $permissions[] = '查看';
        }
        if ($permissionSet & FmsAclRuleModel::PERMISSION_UPLOAD) {
            $permissions[] = '上传';
        }
        if ($permissionSet & FmsAclRuleModel::PERMISSION_DELETE) {
            $permissions[] = '删除';
        }
        if ($permissionSet & FmsAclRuleModel::PERMISSION_SHARE) {
            $permissions[] = '分享';
        }

        return $permissions;
    }

    /**
     * 将权限文本转换为权限位
     *
     * @param array $permissions 权限文本数组
     * @return int
     */
    public function parsePermissions(array $permissions): int
    {
        $permissionSet = 0;

        foreach ($permissions as $permission) {
            switch (strtolower($permission)) {
                case 'view':
                case '查看':
                    $permissionSet |= FmsAclRuleModel::PERMISSION_VIEW;
                    break;
                case 'upload':
                case '上传':
                    $permissionSet |= FmsAclRuleModel::PERMISSION_UPLOAD;
                    break;
                case 'delete':
                case '删除':
                    $permissionSet |= FmsAclRuleModel::PERMISSION_DELETE;
                    break;
                case 'share':
                case '分享':
                    $permissionSet |= FmsAclRuleModel::PERMISSION_SHARE;
                    break;
            }
        }

        return $permissionSet;
    }

    /**
     * 获取条件列表
     *
     * @param int|null $ruleId 规则ID过滤
     * @param array $filter 过滤条件
     * @param array $op 操作符
     * @param string $sort 排序字段
     * @param string $order 排序方向
     * @param int $limit 分页大小
     * @return mixed
     */
    public function getConditionList(?int $ruleId = null, array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        $query = FmsAclConditionModel::with(['rule']);

        if ($ruleId) {
            $query->where('rule_id', $ruleId);
        }

        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit, $query);

        return $query->orderBy($sort, $order)->paginate($limit);
    }

    /**
     * 获取条件详情
     *
     * @param int $conditionId 条件ID
     * @return FmsAclConditionModel|null
     */
    public function getConditionDetail(int $conditionId): ?FmsAclConditionModel
    {
        return FmsAclConditionModel::with(['rule'])->find($conditionId);
    }

    /**
     * 更新条件
     *
     * @param int $conditionId 条件ID
     * @param array $data 更新数据
     * @return FmsAclConditionModel
     */
    public function updateCondition(int $conditionId, array $data): FmsAclConditionModel
    {
        $condition = FmsAclConditionModel::findOrFail($conditionId);

        $data['updated_by'] = $this->getCurrentUserId();
        $data['updated_at'] = Carbon::now();

        $condition->update($data);

        return $condition;
    }

    /**
     * 删除条件
     *
     * @param int $conditionId 条件ID
     * @return bool
     */
    public function deleteCondition(int $conditionId): bool
    {
        $condition = FmsAclConditionModel::findOrFail($conditionId);
        return $condition->delete();
    }

    /**
     * 批量创建条件
     *
     * @param int $ruleId 规则ID
     * @param array $conditionsData 条件数据数组
     * @return array
     */
    public function createConditionsBatch(int $ruleId, array $conditionsData): array
    {
        $conditions = [];

        foreach ($conditionsData as $conditionData) {
            $conditions[] = $this->createAclCondition($ruleId, $conditionData);
        }

        return $conditions;
    }

    /**
     * 创建时间条件
     *
     * @param int $ruleId 规则ID
     * @param string $startTime 开始时间
     * @param string $endTime 结束时间
     * @param string $operator 操作符
     * @return FmsAclConditionModel
     */
    public function createTimeCondition(int $ruleId, string $startTime, string $endTime, string $operator = FmsAclConditionModel::OPERATOR_BETWEEN): FmsAclConditionModel
    {
        return $this->createAclCondition($ruleId, [
            'condition_type' => FmsAclConditionModel::CONDITION_TYPE_TIME,
            'condition_value' => [$startTime, $endTime],
            'operator' => $operator
        ]);
    }

    /**
     * 创建IP条件
     *
     * @param int $ruleId 规则ID
     * @param string $ipRange IP范围（如：***********/24）
     * @param string $operator 操作符
     * @return FmsAclConditionModel
     */
    public function createIpCondition(int $ruleId, string $ipRange, string $operator = FmsAclConditionModel::OPERATOR_LIKE): FmsAclConditionModel
    {
        return $this->createAclCondition($ruleId, [
            'condition_type' => FmsAclConditionModel::CONDITION_TYPE_IP,
            'condition_value' => $ipRange,
            'operator' => $operator
        ]);
    }

    /**
     * 创建标签条件
     *
     * @param int $ruleId 规则ID
     * @param array $tags 标签数组
     * @param string $operator 操作符
     * @return FmsAclConditionModel
     */
    public function createTagCondition(int $ruleId, array $tags, string $operator = FmsAclConditionModel::OPERATOR_IN): FmsAclConditionModel
    {
        return $this->createAclCondition($ruleId, [
            'condition_type' => FmsAclConditionModel::CONDITION_TYPE_TAG,
            'condition_value' => $tags,
            'operator' => $operator
        ]);
    }

    /**
     * 创建文件大小条件
     *
     * @param int $ruleId 规则ID
     * @param int $minSize 最小大小（字节）
     * @param int $maxSize 最大大小（字节）
     * @param string $operator 操作符
     * @return FmsAclConditionModel
     */
    public function createFileSizeCondition(int $ruleId, int $minSize, int $maxSize, string $operator = FmsAclConditionModel::OPERATOR_BETWEEN): FmsAclConditionModel
    {
        return $this->createAclCondition($ruleId, [
            'condition_type' => FmsAclConditionModel::CONDITION_TYPE_FILE_SIZE,
            'condition_value' => [$minSize, $maxSize],
            'operator' => $operator
        ]);
    }

    /**
     * 创建文件类型条件
     *
     * @param int $ruleId 规则ID
     * @param array $fileTypes 文件类型数组
     * @param string $operator 操作符
     * @return FmsAclConditionModel
     */
    public function createFileTypeCondition(int $ruleId, array $fileTypes, string $operator = FmsAclConditionModel::OPERATOR_IN): FmsAclConditionModel
    {
        return $this->createAclCondition($ruleId, [
            'condition_type' => FmsAclConditionModel::CONDITION_TYPE_FILE_TYPE,
            'condition_value' => $fileTypes,
            'operator' => $operator
        ]);
    }

    /**
     * 验证条件值格式
     *
     * @param string $conditionType 条件类型
     * @param mixed $conditionValue 条件值
     * @param string $operator 操作符
     * @return bool
     */
    public function validateConditionValue(string $conditionType, $conditionValue, string $operator): bool
    {
        switch ($conditionType) {
            case FmsAclConditionModel::CONDITION_TYPE_TIME:
                if ($operator === FmsAclConditionModel::OPERATOR_BETWEEN) {
                    return is_array($conditionValue) && count($conditionValue) === 2;
                }
                return is_string($conditionValue) || is_numeric($conditionValue);

            case FmsAclConditionModel::CONDITION_TYPE_IP:
                return is_string($conditionValue) && !empty($conditionValue);

            case FmsAclConditionModel::CONDITION_TYPE_TAG:
                return is_array($conditionValue) || is_string($conditionValue);

            case FmsAclConditionModel::CONDITION_TYPE_FILE_SIZE:
                if ($operator === FmsAclConditionModel::OPERATOR_BETWEEN) {
                    return is_array($conditionValue) && count($conditionValue) === 2;
                }
                return is_numeric($conditionValue);

            case FmsAclConditionModel::CONDITION_TYPE_FILE_TYPE:
                return is_array($conditionValue) || is_string($conditionValue);

            default:
                return false;
        }
    }

    /**
     * 批量检查权限
     *
     * @param array $targets 目标数组 [['type' => 'directory', 'id' => 1], ...]
     * @param int $userId 用户ID
     * @param int $permission 要检查的权限
     * @param array $context 上下文信息
     * @return array 权限检查结果
     */
    public function checkPermissionsBatch(array $targets, int $userId, int $permission, array $context = []): array
    {
        $results = [];

        foreach ($targets as $target) {
            $key = $target['type'] . '_' . $target['id'];
            $results[$key] = $this->checkPermission($target['type'], $target['id'], $userId, $permission, $context);
        }

        return $results;
    }

    /**
     * 检查用户是否有任意权限
     *
     * @param string $targetType 目标类型
     * @param int $targetId 目标ID
     * @param int $userId 用户ID
     * @param array $permissions 权限数组
     * @param array $context 上下文信息
     * @return bool
     */
    public function hasAnyPermission(string $targetType, int $targetId, int $userId, array $permissions, array $context = []): bool
    {
        foreach ($permissions as $permission) {
            if ($this->checkPermission($targetType, $targetId, $userId, $permission, $context)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查用户是否有所有权限
     *
     * @param string $targetType 目标类型
     * @param int $targetId 目标ID
     * @param int $userId 用户ID
     * @param array $permissions 权限数组
     * @param array $context 上下文信息
     * @return bool
     */
    public function hasAllPermissions(string $targetType, int $targetId, int $userId, array $permissions, array $context = []): bool
    {
        foreach ($permissions as $permission) {
            if (!$this->checkPermission($targetType, $targetId, $userId, $permission, $context)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取用户对目标的有效权限
     *
     * @param string $targetType 目标类型
     * @param int $targetId 目标ID
     * @param int $userId 用户ID
     * @param array $context 上下文信息
     * @return array
     */
    public function getEffectivePermissions(string $targetType, int $targetId, int $userId, array $context = []): array
    {
        $permissions = [];

        $allPermissions = [
            FmsAclRuleModel::PERMISSION_VIEW,
            FmsAclRuleModel::PERMISSION_UPLOAD,
            FmsAclRuleModel::PERMISSION_DELETE,
            FmsAclRuleModel::PERMISSION_SHARE
        ];

        foreach ($allPermissions as $permission) {
            if ($this->checkPermission($targetType, $targetId, $userId, $permission, $context)) {
                $permissions[] = $permission;
            }
        }

        return $permissions;
    }

    /**
     * 检查IP是否匹配条件
     *
     * @param string $clientIp 客户端IP
     * @param string $conditionValue 条件值
     * @param string $operator 操作符
     * @return bool
     */
    public function checkIpCondition(string $clientIp, string $conditionValue, string $operator): bool
    {
        switch ($operator) {
            case FmsAclConditionModel::OPERATOR_EQ:
                return $clientIp === $conditionValue;

            case FmsAclConditionModel::OPERATOR_LIKE:
                // 支持CIDR格式
                if (strpos($conditionValue, '/') !== false) {
                    return $this->ipInRange($clientIp, $conditionValue);
                }
                return strpos($clientIp, $conditionValue) !== false;

            case FmsAclConditionModel::OPERATOR_IN:
                $allowedIps = is_array($conditionValue) ? $conditionValue : explode(',', $conditionValue);
                return in_array($clientIp, $allowedIps);

            default:
                return false;
        }
    }

    /**
     * 检查IP是否在指定范围内
     *
     * @param string $ip IP地址
     * @param string $range CIDR格式的IP范围
     * @return bool
     */
    private function ipInRange(string $ip, string $range): bool
    {
        list($subnet, $mask) = explode('/', $range);

        $ipLong = ip2long($ip);
        $subnetLong = ip2long($subnet);
        $maskLong = -1 << (32 - (int)$mask);

        return ($ipLong & $maskLong) === ($subnetLong & $maskLong);
    }

    /**
     * 检查时间条件
     *
     * @param int $currentTime 当前时间戳
     * @param mixed $conditionValue 条件值
     * @param string $operator 操作符
     * @return bool
     */
    public function checkTimeCondition(int $currentTime, $conditionValue, string $operator): bool
    {
        switch ($operator) {
            case FmsAclConditionModel::OPERATOR_BETWEEN:
                if (is_array($conditionValue) && count($conditionValue) >= 2) {
                    $startTime = is_numeric($conditionValue[0]) ? $conditionValue[0] : strtotime($conditionValue[0]);
                    $endTime = is_numeric($conditionValue[1]) ? $conditionValue[1] : strtotime($conditionValue[1]);
                    return $currentTime >= $startTime && $currentTime <= $endTime;
                }
                return false;

            case FmsAclConditionModel::OPERATOR_GT:
                $compareTime = is_numeric($conditionValue) ? $conditionValue : strtotime($conditionValue);
                return $currentTime > $compareTime;

            case FmsAclConditionModel::OPERATOR_LT:
                $compareTime = is_numeric($conditionValue) ? $conditionValue : strtotime($conditionValue);
                return $currentTime < $compareTime;

            default:
                return false;
        }
    }

    /**
     * 构建权限检查上下文
     *
     * @param array $additionalContext 额外的上下文信息
     * @return array
     */
    public function buildPermissionContext(array $additionalContext = []): array
    {
        $context = [
            'current_time' => time(),
            'client_ip' => $this->getClientIp(),
        ];

        return array_merge($context, $additionalContext);
    }

    /**
     * 获取客户端IP
     *
     * @return string
     */
    private function getClientIp(): string
    {
        // 这里应该从请求中获取真实的客户端IP
        // 考虑代理、负载均衡等情况
        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }

    /**
     * 获取当前用户ID
     *
     * @return int
     */
    private function getCurrentUserId(): int
    {
        return make(AuthManager::class)->user()->getId(); // 临时返回1
    }
}
