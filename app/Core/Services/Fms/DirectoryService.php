<?php

declare(strict_types=1);

namespace App\Core\Services\Fms;

use App\Core\Services\BusinessService;
use App\Core\Services\Fms\AclService;
use App\Core\Services\UserService;
use App\Model\TchipBiFms\FmsDirectoryModel;
use App\Model\TchipBiFms\FmsFileModel;
use App\Model\TchipBiFms\FmsRecycleBinModel;
use App\Model\TchipBiFms\FmsAclRuleModel;
use App\Exception\AppException;
use App\Constants\StatusCode;
use Carbon\Carbon;
use Hyperf\DbConnection\Db;
use Qbhy\HyperfAuth\AuthManager;
use Hyperf\Di\Annotation\Inject;

/**
 * FMS目录服务
 */
class DirectoryService extends BusinessService
{
    /**
     * @Inject()
     * @var AuthManager
     */
    protected $auth;

    /**
     * @Inject()
     * @var FmsDirectoryModel
     */
    protected $model; 

    /**
     * ACL权限服务
     * @Inject()
     * @var AclService
     */
    protected $aclService;


    /**
     * 获取目录列表
     */
    public function getDirectoryList(?int $parentId = null, string $search = '', int $page = 1, int $pageSize = 20, int $union = 0)
    {
        // 当 union = 1 时，启用联合查询功能
        if ($union === 1) {
            return $this->getUnionDirectoryFileList($parentId, $search, $page, $pageSize);
        }
        $userId = $this->getCurrentUserId();
        $deptIds = $this->getCurrentUserDepartmentIds();

        // 获取权限主体
        $subjects = make(AclService::class)->getUserSubjects($userId);

        $permissions = FmsAclRuleModel::PERMISSION_VIEW;

        $query = FmsDirectoryModel::query()
            ->with([ 'children', 'aclRules'])
            ->withAcl($subjects, $permissions, $userId)
            ->where('is_deleted', 0)
            ->orderBy('sort_order', 'asc')
            ->orderBy('created_at', 'desc');

        // 按父目录筛选
        if ($parentId !== null) {
            $query->where('parent_id', $parentId);
        } else {
            $query->whereNull('parent_id');
        }

        // 搜索
        if (!empty($search)) {
            $query->where('name', 'like', '%' . $search . '%');
        }

        // 分页
        $total = $query->count();
        $directories = $query->offset(($page - 1) * $pageSize)
            ->limit($pageSize)
            ->get()
            ->toArray();

        return [
            'data' => $directories,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize,
        ];
    }

    /**
     * 创建目录
     */
    public function createDirectory(array $data)
    {
        // 验证父目录是否存在
        if (!empty($data['parent_id'])) {
            $parent = FmsDirectoryModel::find($data['parent_id']);
            if (!$parent) {
                throw new AppException(StatusCode::ERR_SERVER, '父目录不存在');
            }
        }

        $data['owner_id']   = $data['owner_id']   ?? make(\Qbhy\HyperfAuth\AuthManager::class)->user()->getId();
        $data['owner_type'] = $data['owner_type'] ?? 'user';           // 默认类型
        $data['path']       = '';  // 先给空，保存后再调用 updatePath() 来生成

        $directory = FmsDirectoryModel::create($data);
        // 更新路径
        $directory->updatePath();

        // 设置ACL权限（如果提供了权限配置）
        if (!empty($data['permissions'])) {
            $this->aclService->setPermissions(
                FmsAclRuleModel::TARGET_TYPE_DIRECTORY,
                $directory->id,
                $data['permissions']
            );
        }

        // 记录操作日志
        $this->logOperation('create', 'directory', $directory->id, $directory->name);

        return $this->getOverView($directory->id);
    }

    /**
     * 更新目录
     */
    public function updateDirectory(int $id, array $data): array
    {
        $directory = FmsDirectoryModel::findOrFail($id);

        $data['updated_by'] = $this->getCurrentUserId();
        $data['updated_at'] = Carbon::now();

        $directory->update($data);

        // 如果修改了父目录，更新路径
        if (isset($data['parent_id'])) {
            $directory->updatePath();
        }

        // 设置ACL权限（如果提供了权限配置）
        if (!empty($data['permissions'])) {
            // 目前为单选, 需要清除旧权限 // 日志记录需要分辨是否有变化
            $this->aclService->clearTargetPermissions(
                FmsAclRuleModel::TARGET_TYPE_DIRECTORY,
                $directory->id
            );

            $this->aclService->setPermissions(
                FmsAclRuleModel::TARGET_TYPE_DIRECTORY,
                $directory->id,
                $data['permissions']
            );
        }

        // 记录操作日志
        $this->logOperation('update', 'directory', $directory->id, $directory->name);

        return $this->getOverView($directory->id);
    }

    /**
     * 删除目录（软删除）
     */
    public function deleteDirectory(int $id): bool
    {
        $directory = FmsDirectoryModel::findOrFail($id);


        // 检查是否有子目录或文件
        if ($directory->hasChildren() || $directory->files()->exists()) {
            throw new AppException(StatusCode::ERR_SERVER, '目录不为空，无法删除');
        }

        return Db::transaction(function () use ($directory) {
            // 记录到回收站
            FmsRecycleBinModel::create([
                'target_type' => 'directory',
                'target_id' => $directory->id,
                'target_name' => $directory->name,
                'target_path' => $directory->full_path,
                'target_data' => $directory->toArray(),
                'deleted_by' => $this->getCurrentUserId(),
                'deleted_at' => Carbon::now(),
            ]);

            // 软删除
            $directory->deleted_by = $this->getCurrentUserId();
            $directory->save();
            $directory->delete();

            // 记录操作日志
            $this->logOperation('delete', 'directory', $directory->id, $directory->name);

            return true;
        });
    }

    /**
     * 移动目录
     */
    public function moveDirectory(int $id, ?int $newParentId): FmsDirectoryModel
    {
        $directory = FmsDirectoryModel::findOrFail($id);

        // 验证新父目录
        if ($newParentId) {
            $newParent = FmsDirectoryModel::findOrFail($newParentId);

            // 检查是否移动到自己的子目录
            if ($directory->isChildOf($newParent) || $directory->id === $newParent->id) {
                throw new AppException(StatusCode::ERR_SERVER, '不能移动到自己的子目录');
            }
        }

        // 检查新位置是否有同名目录
        $exists = FmsDirectoryModel::where('parent_id', $newParentId)
                              ->where('name', $directory->name)
                              ->where('id', '!=', $id)
                              ->exists();
        if ($exists) {
            throw new AppException(StatusCode::ERR_SERVER, '目标位置已存在同名目录');
        }

        $directory->parent_id = $newParentId;
        $directory->updated_by = $this->getCurrentUserId();
        $directory->save();

        // 更新路径
        $directory->updatePath();

        // 记录操作日志
        $this->logOperation('move', 'directory', $directory->id, $directory->name);

        return $directory;
    }

    /**
     * 获取目录树 - 原始方法（保留向后兼容性）
     * @deprecated 建议使用 getDirectoryTreeOptimized() 以获得更好的性能
     */
    public function getDirectoryTree(?int $parentId = null, int $maxDepth = 5)
    {
        return $this->getDirectoryTreeOptimized($parentId, $maxDepth);
    }

    /**
     * 获取目录树 - 优化版本
     * 使用批量查询和内存处理，避免 N+1 查询问题
     *
     * 性能优化点：
     * 1. 使用 path 字段进行批量查询，避免递归 with('allChildren')
     * 2. 一次性获取所有权限数据，避免多次数据库往返
     * 3. 在内存中构建树结构，减少数据库查询次数
     *
     * @param int|null $parentId 父目录ID，null表示根目录
     * @param int $maxDepth 最大深度，默认5层
     * @return array 树形结构数组
     */
    public function getDirectoryTreeOptimized(?int $parentId = null, int $maxDepth = 5)
    {
        $userId = $this->getCurrentUserId();
        $subjects = make(AclService::class)->getUserSubjects($userId);
        $permissions = FmsAclRuleModel::PERMISSION_VIEW;

        // 第一步：获取根目录（应用权限过滤）
        $rootQuery = FmsDirectoryModel::withAcl($subjects, $permissions, $userId)
            ->where('is_deleted', 0);

        if ($parentId) {
            $rootQuery->where('parent_id', $parentId);
        } else {
            $rootQuery->whereNull('parent_id');
        }

        $rootDirectories = $rootQuery->orderBy('sort_order')->orderBy('name')->get();

        if ($rootDirectories->isEmpty()) {
            return [];
        }

        // 第二步：批量获取所有子目录（如果需要）
        $allDirectories = collect($rootDirectories);

        if ($maxDepth > 1) {
            // 构建所有根目录的路径模式
            $pathPatterns = [];
            foreach ($rootDirectories as $rootDir) {
                // path 字段格式为 /1/2/3/，我们需要查找以此路径开头的所有子目录
                $basePath = $rootDir->path;
                $pathPatterns[] = $basePath . '%';
            }

            if (!empty($pathPatterns)) {
                // 批量查询所有子目录
                $childQuery = FmsDirectoryModel::where('is_deleted', 0);

                // 添加路径条件
                $childQuery->where(function ($q) use ($pathPatterns) {
                    foreach ($pathPatterns as $pattern) {
                        $q->orWhere(function ($sub) use ($pattern) {
                            $sub->where('path', 'like', $pattern)
                                ->where('path', '!=', rtrim($pattern, '%')); // 排除自身
                        });
                    }
                });

                // 限制深度：计算路径中的斜杠数量来控制深度
                if ($maxDepth > 1) {
                    $maxSlashCount = $this->calculateMaxSlashCount($rootDirectories, $maxDepth);
                    $childQuery->whereRaw('(LENGTH(path) - LENGTH(REPLACE(path, "/", ""))) <= ?', [$maxSlashCount]);
                }

                $childDirectories = $childQuery->orderBy('path')->orderBy('sort_order')->orderBy('name')->get();

                // 过滤有权限的子目录
                $filteredChildren = $childDirectories->filter(function ($directory) use ($subjects, $permissions, $userId) {
                    return $this->checkDirectoryPermissionInternal($directory, $subjects, $permissions, $userId);
                });

                $allDirectories = $allDirectories->merge($filteredChildren);
            }
        }

        // 第三步：批量获取所有目录的权限信息
        $allIds = $allDirectories->pluck('id')->toArray();
        $permissionsData = [];

        if (!empty($allIds)) {
            $permissionsData = $this->aclService->getTargetsRules(
                FmsAclRuleModel::TARGET_TYPE_DIRECTORY,
                $allIds
            );
        }


        // 第四步：在内存中构建树结构并附加权限信息
        return $this->buildTreeOptimized($allDirectories, $permissionsData, $parentId, $maxDepth);
    }


    /**
     * 计算最大斜杠数量，用于控制查询深度
     */
    private function calculateMaxSlashCount($rootDirectories, int $maxDepth): int
    {
        $maxSlashCount = 0;
        foreach ($rootDirectories as $rootDir) {
            $currentSlashCount = substr_count($rootDir->path, '/');
            $maxSlashCount = max($maxSlashCount, $currentSlashCount + $maxDepth);
        }
        return $maxSlashCount;
    }

    /**
     * 检查目录权限（优化版本，内部使用）
     */
    private function checkDirectoryPermissionInternal($directory, array $subjects, int $permissions, int $userId): bool
    {
        // 检查是否为超级管理员
        if ($userId) {
            $authService = \Hyperf\Utils\ApplicationContext::getContainer()->get(\App\Core\Services\AuthService::class);
            if ($authService->isSuper($userId, ['Director'])) {
                return true;
            }
        }

        // 如果目录没有ACL规则，则认为是公开的
        $hasAclRules = FmsAclRuleModel::where('target_type', FmsAclRuleModel::TARGET_TYPE_DIRECTORY)
            ->where('target_id', $directory->id)
            ->exists();

        if (!$hasAclRules) {
            return true;
        }

        // 检查用户是否有访问权限
        $hasPermission = FmsAclRuleModel::where('target_type', FmsAclRuleModel::TARGET_TYPE_DIRECTORY)
            ->where('target_id', $directory->id)
            ->whereIn('subject_key_id', $subjects)
            ->whereRaw('(permission_set & ?) > 0', [$permissions])
            ->exists();

        return $hasPermission;
    }

    /**
     * 优化版本：构建目录树结构并附加权限信息
     * 在内存中进行数据处理，避免多次数据库查询
     */
    private function buildTreeOptimized($directories, $permissionsData, ?int $parentId = null, int $maxDepth = 5): array
    {
        // 将目录按 parent_id 分组，便于快速查找
        $directoriesByParent = $directories->groupBy('parent_id');

        // 预处理权限数据
        $processedPermissions = [];
        foreach ($permissionsData as $directoryId => $directoryPermissions) {
            $processedPermissions[$directoryId] = $this->groupPermissionsByEffectAndType($directoryPermissions);
        }

        // 递归构建树结构
        return $this->buildTreeRecursiveOptimized(
            $directoriesByParent,
            $processedPermissions,
            $parentId,
            1,
            $maxDepth
        );
    }

    /**
     * 递归构建树结构的优化版本
     */
    private function buildTreeRecursiveOptimized($directoriesByParent, $processedPermissions, ?int $parentId, int $currentDepth, int $maxDepth): array
    {
        $tree = [];

        if (!isset($directoriesByParent[$parentId])) {
            return $tree;
        }

        foreach ($directoriesByParent[$parentId] as $directory) {
            $node = [
                'id' => $directory->id,
                'name' => $directory->name,
                'path' => $directory->full_path ?? $directory->path,
                'parent_id' => $directory->parent_id,
                'visibility' => $directory->visibility,
                'level' => $this->calculateLevelFromPath($directory->path),
                'has_children' => isset($directoriesByParent[$directory->id]) && $directoriesByParent[$directory->id]->isNotEmpty(),
                'children' => []
            ];

            // 添加权限信息
            if (isset($processedPermissions[$directory->id])) {
                $node['allow'] = $processedPermissions[$directory->id]['allow'] ?? [];
                $node['deny'] = $processedPermissions[$directory->id]['deny'] ?? [];
            } else {
                $node['allow'] = [];
                $node['deny'] = [];
            }

            // 设置 isLeaf 属性（antd a-tree 需要）
            $node['isLeaf'] = !$node['has_children'];

            // 递归构建子节点（如果未达到最大深度）
            if ($currentDepth < $maxDepth && $node['has_children']) {
                $node['children'] = $this->buildTreeRecursiveOptimized(
                    $directoriesByParent,
                    $processedPermissions,
                    $directory->id,
                    $currentDepth + 1,
                    $maxDepth
                );
            }

            $tree[] = $node;
        }

        return $tree;
    }

    /**
     * 从路径计算目录层级
     */
    private function calculateLevelFromPath(string $path): int
    {
        // path 格式为 /1/2/3/，层级 = 斜杠数量 - 1
        return max(0, substr_count(trim($path, '/'), '/'));
    }



     /**
     * 获取目录详情
     */
    public function getOverView($directoryId)
    {
        $result = $this->model::query()->where('id', $directoryId)->get()->toArray();
        if (!$result) {
            throw new AppException(StatusCode::ERR_SERVER, '目录不存在');
        }
        // 获取acl
        $permissions = $this->aclService->getTargetsRules(FmsAclRuleModel::TARGET_TYPE_DIRECTORY, [$directoryId]);
        //  "deny": []  "allow": [] 获取
        $permissions = $permissions ? $this->groupPermissionsByEffectAndType($permissions[$directoryId]) : [];
        $result = array_merge($result[0], $permissions);
        return $result;
    }


    /**
     * 按 effect(allow/deny) 和 subject_type(user/dept/role) 分组
     * 提取 subject_id 集合
     *
     * @param array $permissions
     * @return array
     */
    function groupPermissionsByEffectAndType(array $permissions): array
    {
        $result = [
            'allow' => [],
            'deny' => [],
        ];

        foreach ($permissions as $permission) {
            // 如果是 Eloquent Collection -> toArray 后可能是对象或数组
            $effect = $permission['effect'] ?? $permission->effect ?? null;
            $subject = $permission['subject'] ?? $permission->subject ?? null;

            if (!$effect || !$subject) {
                continue;
            }

            $type = $subject['subject_type'] ?? $subject->subject_type ?? null;
            $id = $subject['subject_id'] ?? $subject->subject_id ?? null;

            if (!$type || !$id) {
                continue;
            }

            // 初始化
            if (!isset($result[$effect][$type])) {
                $result[$effect][$type] = [];
            }

            // 去重
            if (!in_array($id, $result[$effect][$type])) {
                $result[$effect][$type][] = $id;
            }
        }

        return $result;
    }


    /**
     * 记录操作日志（临时实现，后续会被日志服务替代）
     */
    private function logOperation(string $action, string $targetType, int $targetId, string $targetName): void
    {
        // TODO: 实现操作日志记录
        // 这里暂时为空，等日志服务实现后再替换
    }



    /**
     * 检查目录权限
     *
     * @param int $directoryId 目录ID
     * @param int $userId 用户ID
     * @param int $permission 权限类型
     * @param array $context 上下文信息
     * @return bool
     */
    public function checkDirectoryPermission(int $directoryId, int $userId, int $permission, array $context = []): bool
    {
        return $this->aclService->checkPermission(
            FmsAclRuleModel::TARGET_TYPE_DIRECTORY,
            $directoryId,
            $userId,
            $permission,
            $context
        );
    }

    /**
     * 获取目录权限规则
     *
     * @param int $directoryId 目录ID
     */
    public function getDirectoryPermissions(int $directoryId)
    {
        return $this->aclService->getTargetRules(
            FmsAclRuleModel::TARGET_TYPE_DIRECTORY,
            $directoryId
        );
    }

    /**
     * 清除目录权限
     *
     * @param int $directoryId 目录ID
     * @return bool
     */
    public function clearDirectoryPermissions(int $directoryId): bool
    {
        return $this->aclService->clearTargetPermissions(
            FmsAclRuleModel::TARGET_TYPE_DIRECTORY,
            $directoryId
        );
    }

    /**
     * 复制目录权限
     *
     * @param int $sourceDirectoryId 源目录ID
     * @param int $targetDirectoryId 目标目录ID
     * @return array 复制的规则数组
     */
    public function copyDirectoryPermissions(int $sourceDirectoryId, int $targetDirectoryId): array
    {
        return $this->aclService->copyPermissions(
            FmsAclRuleModel::TARGET_TYPE_DIRECTORY,
            $sourceDirectoryId,
            FmsAclRuleModel::TARGET_TYPE_DIRECTORY,
            $targetDirectoryId
        );
    }

    /**
     * 获取用户对目录的有效权限
     *
     * @param int $directoryId 目录ID
     * @param int $userId 用户ID
     * @param array $context 上下文信息
     * @return array
     */
    public function getUserDirectoryPermissions(int $directoryId, int $userId, array $context = []): array
    {
        return $this->aclService->getEffectivePermissions(
            FmsAclRuleModel::TARGET_TYPE_DIRECTORY,
            $directoryId,
            $userId,
            $context
        );
    }

    /**
     * 获取当前用户ID
     */
    private function getCurrentUserId(): int
    {
        return $this->auth->user()->getId();
    }

    /**
     * 获取当前用户所在部门IDS
     */
    private function getCurrentUserDepartmentIds(): array
    {
        return make(UserService::class)->getUserDepartmentIds($this->getCurrentUserId());
    }

    /**
     * 联合查询目录和文件列表
     */
    private function getUnionDirectoryFileList(?int $parentId = null, string $search = '', int $page = 1, int $pageSize = 20): array
    {
        // 构建目录查询
        $directoryQuery = FmsDirectoryModel::query()
            ->select([
                'id',
                'name',
                'created_at',
                'updated_at',
                'sort_order',
                Db::raw("'directory' as item_type"),
                Db::raw("0 as item_type_order"), // 目录排序优先级为0
                'parent_id',
                Db::raw("NULL as directory_id"),
                Db::raw("NULL as size"),
                Db::raw("NULL as mime_type"),
                Db::raw("NULL as version"),
                'visibility',
                Db::raw("NULL as created_by"),
                Db::raw("NULL as updated_by")
            ])
            ->where('is_deleted', 0)
            ->getQuery();

        // 构建文件查询
        $fileQuery = FmsFileModel::query()
            ->select([
                'id',
                'name',
                'created_at',
                'updated_at',
                'sort_order',
                Db::raw("'file' as item_type"),
                Db::raw("1 as item_type_order"), // 文件排序优先级为1
                Db::raw("NULL as parent_id"),
                'directory_id',
                'size',
                'mime_type',
                'version',
                'visibility',
                'created_by',
                'updated_by'
            ])
            ->where('is_deleted', 0)
            ->getQuery();

        // 应用父目录筛选条件
        if ($parentId !== null) {
            $directoryQuery->where('parent_id', $parentId);
            $fileQuery->where('directory_id', $parentId);
        } else {
            $directoryQuery->whereNull('parent_id');
            // 对于文件，当 parentId 为 null 时，查询根目录下的文件（directory_id 为 null 或 0）
            $fileQuery->where(function ($query) {
                $query->whereNull('directory_id')->orWhere('directory_id', 0);
            });
        }

        // 应用搜索条件
        if (!empty($search)) {
            $directoryQuery->where('name', 'like', '%' . $search . '%');
            $fileQuery->where('name', 'like', '%' . $search . '%');
        }

        // 在分页前先计算各自的总数
        $directoryCount = (clone $directoryQuery)->count();
        $fileCount = (clone $fileQuery)->count();

        // 执行联合查询并使用 paginate 方法
        $unionQuery = $directoryQuery->unionAll($fileQuery);
        
        // 应用排序：先按类型排序（目录优先），再按 sort_order，最后按创建时间
        $paginate = $unionQuery
            ->orderBy('item_type_order', 'asc')
            ->orderBy('sort_order', 'asc')
            ->orderBy('created_at', 'desc')
            ->paginate($pageSize, ['*'], 'page', $page);

        // 转换为数组格式
        $paginateArray = $paginate->toArray();
        
        return [
            'data' => $paginateArray['data'],
            'total' => $paginateArray['total'],
            'directory_count' => $directoryCount,
            'file_count' => $fileCount,
            'page' => $paginateArray['current_page'],
            'page_size' => $paginateArray['per_page'],
        ];
    }
}