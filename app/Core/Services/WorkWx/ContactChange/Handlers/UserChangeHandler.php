<?php

namespace App\Core\Services\WorkWx\ContactChange\Handlers;

use App\Annotation\WorkWxTokenAnnotation;
use App\Core\Services\WorkWx\ContactChange\ContactChangeHandlerInterface;
use App\Core\Services\WorkWx\ContactChange\SystemAccountCoordinator;
use App\Core\Services\WorkWx\WorkWxBaseService;
use App\Core\Utils\Log;
use App\Core\Utils\Random;
use App\Constants\CommonCode;
use App\Model\TchipBi\AuthGroupAccessModel;
use App\Model\TchipBi\UserDepartmentBindModel;
use App\Model\TchipBi\UserModel;
use Hyperf\Di\Annotation\Inject;

/**
 * 用户变更处理器 - 按照syncUserList逻辑处理
 */
class UserChangeHandler extends WorkWxBaseService implements ContactChangeHandlerInterface
{
    /**
     * @Inject()
     * @var UserModel
     */
    protected $userModel;

    /**
     * @Inject()
     * @var AuthGroupAccessModel
     */
    protected $groupAccessModel;

    /**
     * @Inject()
     * @var UserDepartmentBindModel
     */
    protected $userDepartmentBindModel;

    /**
     * @Inject()
     * @var SystemAccountCoordinator
     */
    protected $systemAccountCoordinator;

    /**
     * 处理用户变更事件
     * @WorkWxTokenAnnotation(type="contact")
     * @param string $changeType
     * @param array $data
     * @return bool
     */
    public function handle(string $changeType, array $data): bool
    {
        Log::get()->info("处理用户变更事件: {$changeType}", $data);

        $userId = $data['UserID'] ?? '';
        if (empty($userId)) {
            Log::get()->error('用户变更事件缺少UserID');
            return false;
        }

        switch ($changeType) {
            case 'create_user':
            case 'update_user':
                return $this->handleCreateOrUpdateUser($userId, $changeType);
            case 'delete_user':
                return $this->handleDeleteUser($userId);
            default:
                Log::get()->error("不支持的用户变更类型: {$changeType}");
                return false;
        }
    }

    /**
     * 处理新增或更新用户 - 按照syncUserList逻辑
     */
    private function handleCreateOrUpdateUser(string $userId, string $changeType): bool
    {
        try {
            Log::get()->info("开始处理用户: {$userId} - {$changeType}");

            // 从企业微信获取完整用户信息
            $userInfo = $this->getUserInfoFromWorkWx($userId);
            if (!$userInfo) {
                Log::get()->error("无法从企业微信获取用户信息: {$userId}");
                return false;
            }

            // 检查本地用户是否存在
            $syncUser = $this->userModel::query()->where('workwx_userid', $userId)->first();

            // 如果用户存在且不需要同步企业微信信息，跳过
            if ($syncUser && $syncUser->sync_work_wechat != 1) {
                Log::get()->info("用户 {$userInfo['name']} 为不需要同步BI系统企业微信信息");
                return true;
            }

            // 检查用户状态
            if (isset($userInfo['status']) && $userInfo['status'] != 1) {
                Log::get()->info("用户 {$userInfo['name']} 状态为: {$userInfo['status']}");
            }

            // 创建或更新用户 - 按照syncUserList逻辑
            // 过滤适合数据库的字段
            $userDataForDb = [
                'name' => $userInfo['name'] ?? '',
                'mobile' => $userInfo['mobile'] ?? '',
                'email' => $userInfo['biz_mail'] ?? $userInfo['email'] ?? '',
                'biz_mail' => $userInfo['biz_mail'] ?? $userInfo['email'] ?? '',
                'avatar' => $userInfo['avatar'] ?? '',
                'thumb_avatar' => $userInfo['thumb_avatar'] ?? '',
                'status' => $userInfo['status'] ?? 1,
                'department' => $userInfo['department'] ?? [],
                'order' => $userInfo['order'] ?? [0],
                'position' => $userInfo['position'] ?? '',
                'gender' => $userInfo['gender'] ?? 0,
                'telephone' => $userInfo['telephone'] ?? '',
                'alias' => $userInfo['alias'] ?? '',
                'address' => $userInfo['address'] ?? '',
                'open_userid' => $userInfo['open_userid'] ?? '',
                'main_department' => $userInfo['main_department'] ?? 0,
                'is_leader_in_dept' => $userInfo['is_leader_in_dept'] ?? [0],
                'direct_leader' => $userInfo['direct_leader'] ?? [],
                'extattr' => $userInfo['extattr'] ?? ['attrs' => []],
                'qr_code' => $userInfo['qr_code'] ?? '',
                'external_profile' => $userInfo['external_profile'] ?? null,
            ];

            $user = $this->userModel::query()->updateOrCreate(['workwx_userid' => $userId], $userDataForDb);
            if (!$user) {
                Log::get()->error("创建或查找用户失败: {$userInfo['name']}");
                return false;
            }

            // 设置BI系统用户信息
            $currentSystemAccount = $this->setupUserBySyncLogic($user, $userInfo);

            // 只有创建用户时才发送包含所有系统账号的邮件通知
            if ($changeType === 'create_user') {
                // 收集所有创建的账号信息
                $allCreatedAccounts = [];

                // 如果当前系统创建了账号，添加到列表中（放在第一位）
                if ($currentSystemAccount) {
                    $allCreatedAccounts[] = $currentSystemAccount;
                }

                // 创建其他系统账号
                $otherSystemAccounts = $this->systemAccountCoordinator->createOtherSystemAccounts($userInfo);
                $allCreatedAccounts = array_merge($allCreatedAccounts, $otherSystemAccounts);

                // 统一发送包含所有系统账号的邮件
                if (!empty($allCreatedAccounts)) {
                    $this->sendMultiSystemAccountNotice($userInfo, $allCreatedAccounts);
                }
            }

            Log::get()->info("成功处理用户: {$userInfo['name']} ({$userId}) - {$changeType}");
            return true;
        } catch (\Exception $e) {
            Log::get()->error("处理用户失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理删除用户
     */
    private function handleDeleteUser(string $userId): bool
    {
        try {
            if (empty($userId)) {
                Log::get()->error('删除用户事件缺少UserID');
                return false;
            }

            // 查找本地用户
            $user = $this->userModel::query()->where('workwx_userid', $userId)->first();
            if (!$user) {
                Log::get()->info("本地不存在用户，无需删除: {$userId}");
                return true;
            }

            // 设置用户状态为退出企业
            $user->update(['status' => 5]);

            // 同时处理其他系统的账号状态
            $this->systemAccountCoordinator->disableUserInOtherSystems($user->email, $user->name);

            Log::get()->info("成功设置用户为退出状态: {$user->name} ({$userId})");
            return true;
        } catch (\Exception $e) {
            Log::get()->error("处理删除用户失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 从企业微信获取用户信息
     */
    private function getUserInfoFromWorkWx(string $userId): ?array
    {
        try {
            // 注解的存在，这里的token应该已经可用
            $accessToken = getCache(CommonCode::REDISKEY_WORKWX_CONTACT_TOKEN);

            if (empty($accessToken)) {
                Log::get()->error("Contact token仍然为空，注解可能未生效");
                return null;
            }

            // 调用企业微信API获取用户详情
            $response = $this->sendRequest('user/get', [
                'query' => [
                    'access_token' => $accessToken,
                    'userid' => $userId
                ]
            ]);

            Log::get()->info('企微用户详情', is_array($response) ? $response : json_decode($response, true));

            return $response;
        } catch (\Exception $e) {
            Log::get()->error("从企业微信获取用户信息失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 按照syncUserList逻辑设置用户信息
     * @return array|null 返回当前系统的账号信息，用于邮件通知
     */
    private function setupUserBySyncLogic($user, array $userInfo): ?array
    {
        // 是否需要更新邮箱 - 按照syncUserList逻辑
        $email = $userInfo['biz_mail'] ?? '';
        if ($email && empty($user->email)) {
            $user->email = $email;
            $user->save();
        }

        // 密码处于不正常状态，初始化密码 - 按照syncUserList逻辑
        $currentSystemPassword = null;
        $passwordCreated = false;
        if (empty($user->password) || empty($user->salt) && $user->must_change_password == 1) {
            $user->salt = Random::alnum(10);
            // 所有用户为随机密码
            $defaultPassword = Random::alnum(10);
            $user->password = makePassword($defaultPassword, $user->salt);
            $user->save();
            $currentSystemPassword = $defaultPassword;
            $passwordCreated = true;
            Log::get()->info("初始化用户密码: {$userInfo['name']} ({$defaultPassword})");
        }

        // 3. 更新角色 - 按照syncUserList逻辑
        $groupAccess = [CommonCode::BASE_GROUP_ACCESS];
        if (empty($user->group_access_ids)) {
            $user->group_access_ids = json_encode($groupAccess);
            $user->save();
        } else {
            $groupAccess = !is_array($user->group_access_ids) ? explode(',', $user->group_access_ids) : $user->group_access_ids;
        }
        foreach ($groupAccess as $access) {
            $accessResult = $this->groupAccessModel::query()->updateOrCreate(['user_id' => $user->id, 'group_id' => $access]);
            if (!$accessResult) {
                // 记录错误，继续操作下面的同步
                Log::get()->error("创建或查找用户组群失败ID:{$access} {$userInfo['name']}");
            }
        }

        // 4. 同步部门 - 按照syncUserList逻辑
        if ($userInfo['department']) {
            $department = is_array($userInfo['department']) ? $userInfo['department'] : json_decode($userInfo['department'], true);
            // 先把不在的部门删除掉
            $this->userDepartmentBindModel::query()->whereNotIn('department_id', $department)->where('user_id', $user->id)->delete();
            // 重新加入
            foreach ($department as $dep) {
                $this->userDepartmentBindModel::query()->updateOrCreate(['user_id' => $user->id, 'department_id' => $dep]);
            }
        }

        // 5. 同步创建Redmine系统
        if (!empty($user->email)) {
            try {
                $accountService = make(\App\Core\Services\Redmine\AccountService::class);
                $accountService->initRedmine($user->id, $user->name, $user->email);
                Log::get()->info("成功创建Redmine账号: {$userInfo['name']}", [
                    'user_id' => $user->id,
                    'email' => $user->email
                ]);
            } catch (\Exception $e) {
                Log::get()->error("创建Redmine账号失败: " . $e->getMessage(), [
                    'user_id' => $user->id,
                    'username' => $userInfo['name'],
                    'email' => $user->email,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // 如果创建了新密码，返回当前系统账号信息
        if ($passwordCreated && $currentSystemPassword) {
            $currentSystemConfig = \App\Constants\SystemAccountConfig::getConfig('tchip_bi');
            return array_merge($currentSystemConfig, [
                'username' => $user->username,
                'account_username' => $user->email,
                'password' => $currentSystemPassword,
                'account_password' => $currentSystemPassword,
                'role_name' => $currentSystemConfig['default_role'] ?? '基础用户',
                'user_id' => $user->id,
                'created' => true
            ]);
        }

        return null;
    }

    /**
     * 发送多系统账号创建通知
     * @param array $userInfo 用户信息
     * @param array $createdAccounts 创建的账号信息数组
     */
    private function sendMultiSystemAccountNotice(array $userInfo, array $createdAccounts): void
    {
        try {
            // 使用多系统账号通知处理器
            \App\Core\Services\Notice\Driver\DynamicNoticeFactory::call(
                'MultiSystemAccountNotice',
                $userInfo,
                $createdAccounts
            );
        } catch (\Exception $e) {
            Log::get()->error("发送多系统账号创建通知失败", [
                'username' => $userInfo['userid'] ?? 'unknown',
                'systems_count' => count($createdAccounts),
                'error' => $e->getMessage()
            ]);
        }
    }
    
}
