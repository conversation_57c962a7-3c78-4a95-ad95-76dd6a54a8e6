<?php

namespace App\Core\Services\WorkWx\ContactChange;

use App\Core\Utils\Log;

/**
 * 系统账号协调器
 * 负责协调各系统的用户账号管理，不包含具体的业务逻辑
 */
class SystemAccountCoordinator
{
    /**
     * 各系统的用户账号服务
     * @var UserAccountServiceInterface[]
     */
    private array $accountServices = [];

    public function __construct()
    {
        // 注册各系统的用户账号服务
        $this->registerAccountServices();
    }

    /**
     * 创建其他系统账号
     * @param array $userInfo 用户信息
     * @return array 返回创建的账号信息数组
     */
    public function createOtherSystemAccounts(array $userInfo): array
    {
        $createdAccounts = [];

        foreach ($this->accountServices as $service) {
            try {
                // 检查是否应该为此用户创建账号
                if (!$service->shouldCreateAccount($userInfo)) {
                    Log::get()->info("用户不需要创建{$service->getSystemName()}账号", [
                        'userid' => $userInfo['userid'],
                        'system' => $service->getSystemName()
                    ]);
                    continue;
                }

                // 创建账号
                $accountInfo = $service->createUserAccount($userInfo);
                if ($accountInfo) {
                    // 只有新创建的账号才加入邮件通知列表
                    if ($accountInfo['created']) {
                        $createdAccounts[] = $accountInfo;
                        Log::get()->info("成功创建{$service->getSystemName()}账号", [
                            'userid' => $userInfo['userid'],
                            'system' => $service->getSystemName(),
                            'created' => true
                        ]);
                    } else {
                        Log::get()->info("{$service->getSystemName()}账号已存在，跳过邮件通知", [
                            'userid' => $userInfo['userid'],
                            'system' => $service->getSystemName(),
                            'created' => false
                        ]);
                    }
                }
            } catch (\Exception $e) {
                Log::get()->error("创建{$service->getSystemName()}账号时发生异常", [
                    'userid' => $userInfo['userid'],
                    'system' => $service->getSystemName(),
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $createdAccounts;
    }

    /**
     * 禁用其他系统中的用户账号
     * @param string $email 用户邮箱
     * @param string $userName 用户名称
     */
    public function disableUserInOtherSystems(string $email, string $userName): void
    {
        if (empty($email)) {
            Log::get()->warning("用户没有邮箱信息，无法处理其他系统账号状态", [
                'username' => $userName
            ]);
            return;
        }

        foreach ($this->accountServices as $service) {
            try {
                $result = $service->disableUserAccount($email, $userName);
                Log::get()->info("处理{$service->getSystemName()}账号禁用", [
                    'email' => $email,
                    'username' => $userName,
                    'system' => $service->getSystemName(),
                    'success' => $result
                ]);
            } catch (\Exception $e) {
                Log::get()->error("禁用{$service->getSystemName()}账号时发生异常", [
                    'email' => $email,
                    'username' => $userName,
                    'system' => $service->getSystemName(),
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * 注册各系统的用户账号服务
     */
    private function registerAccountServices(): void
    {
        // 注册销售系统服务
        $this->accountServices[] = make(\App\Core\Services\TchipSale\UserAccountService::class);
        
        // 注册售后系统服务
        $this->accountServices[] = make(\App\Core\Services\FireflyService\UserAccountService::class);
        
        // 未来可以继续注册其他系统服务
        // $this->accountServices[] = make(\App\Core\Services\OtherSystem\UserAccountService::class);
    }

    /**
     * 获取所有注册的系统服务
     * @return UserAccountServiceInterface[]
     */
    public function getAccountServices(): array
    {
        return $this->accountServices;
    }

    /**
     * 根据系统名称获取服务
     * @param string $systemName
     * @return UserAccountServiceInterface|null
     */
    public function getAccountServiceByName(string $systemName): ?UserAccountServiceInterface
    {
        foreach ($this->accountServices as $service) {
            if ($service->getSystemName() === $systemName) {
                return $service;
            }
        }
        return null;
    }
}
