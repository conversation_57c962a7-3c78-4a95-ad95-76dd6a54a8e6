<?php

namespace App\Core\Services\WorkWx\ContactChange;

/**
 * 用户账号服务接口
 * 各系统需要实现此接口来管理自己的用户账号
 */
interface UserAccountServiceInterface
{
    /**
     * 创建用户账号
     * @param array $userInfo 企业微信用户信息
     * @return array|null 返回创建的账号信息，失败返回null
     * 返回格式：
     * [
     *     'system_name' => '系统名称',
     *     'system_description' => '系统描述', 
     *     'login_url' => '登录地址',
     *     'username' => '用户名',
     *     'account_username' => '账号用户名',
     *     'password' => '密码',
     *     'account_password' => '账号密码',
     *     'role_name' => '角色名称',
     *     'user_id' => '用户ID',
     *     'created' => true/false // 是否新创建
     * ]
     */
    public function createUserAccount(array $userInfo): ?array;

    /**
     * 禁用用户账号
     * @param string $email 用户邮箱
     * @param string $userName 用户名称
     * @return bool 是否成功
     */
    public function disableUserAccount(string $email, string $userName): bool;

    /**
     * 获取系统名称
     * @return string
     */
    public function getSystemName(): string;

    /**
     * 检查是否应该为此用户创建账号
     * @param array $userInfo 企业微信用户信息
     * @return bool
     */
    public function shouldCreateAccount(array $userInfo): bool;
}
