<?php

namespace App\Core\Services\WorkWx\ContactChange;

use App\Core\Services\WorkWx\ContactChange\Handlers\UserChangeHandler;
use App\Exception\AppException;
use App\Constants\StatusCode;

/**
 * 通讯录变更处理器工厂
 */
class ContactChangeHandlerFactory
{
    /**
     * 创建对应的处理器
     *
     * @param string $changeType 变更类型
     * @return ContactChangeHandlerInterface
     * @throws AppException
     */
    public static function create(string $changeType): ContactChangeHandlerInterface
    {
        switch ($changeType) {
            case 'create_user':
            case 'update_user':
            case 'delete_user':
                return make(UserChangeHandler::class);

            default:
                throw new AppException(StatusCode::ERR_SERVER, "不支持的变更类型: {$changeType}");
        }
    }
}
