<?php

namespace App\Core\Services\TchipBi;

use Hyperf\Di\Annotation\Inject;
use App\Model\TchipBi\UserSubscribeUserModel;

class UserSubscribeUserService extends \App\Core\Services\BusinessService
{

    /**
     * @Inject()
     * @var UserSubscribeUserModel
     */
    protected $model;

    /**
     * 获取列表
     * @param array $filter
     * @param array $op
     * @param string $sort
     * @param string $order
     * @param int $limit
     * @return mixed
     */
    public function getSubscribeList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
        $userTable = 'user';
        $table = 'user_subscribe_user';
        $paginate = $query->select(["{$userTable}.id", "{$userTable}.name", "{$userTable}.biz_mail", "{$userTable}.thumb_avatar", "{$table}.type", "{$table}.module"])
            ->join($userTable, "{$userTable}.id", '=', "{$table}.subscribed_user_id")
            ->orderBy($sort, $order)->paginate($limit);
        $paginate = $paginate ? $paginate->toArray() : [];
        return $paginate;
    }

    /**
     * 获取用户关注的用户列表
     * @param $uid
     * @param $module
     * @param string $type
     * @return void
     */
    public function mySubscribeUsers($uid, $module, string $type = 'subscribe') : array
    {
        $userTable = 'user';
        $table = 'user_subscribe_user';
        $users = $this->model->select(["{$userTable}.*"])->join($userTable, "{$userTable}.id", '=', "{$table}.subscribed_user_id")
            ->where('user_id', $uid)->where('module', $module);
        if ($type) {
            $users = $users->where('type', $type);
        }
        $users = $users->get();
        return $users ? $users->toArray() : [];
    }

    /**
     * 获取用户关注的用户IDS
     * @param $uid
     * @param $module
     * @param string $type
     * @return array
     */
    public function mySubscribeUsersIds($uid, $module, string $type = 'subscribe') : array
    {
        $users = $this->mySubscribeUsers($uid, $module, $type);
        return $users ? array_column($users, 'id') : [];
    }

    /**
     * 操作订阅用户
     * @param $uid
     * @param $subscribedId
     * @param $module
     * @param $action
     * @param $type
     * @return void
     */
    public function doSubscribe($uid, $subscribedId, $module, int $action, string $type = 'subscribe')
    {
        $data = [
            'user_id'            => $uid,
            'subscribed_user_id' => $subscribedId,
            'module'             => $module,
            'type'               => $type,
        ];
        if ($action == 1) {
            $result = $this->model::updateOrCreate($data, $data);
            $result = $result ? 1 : 0;
        } else {
            $result = $this->model::query()->where($data)->delete();
        }
        return $result;
    }
}