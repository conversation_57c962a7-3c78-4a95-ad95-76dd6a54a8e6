<?php

namespace App\Core\Services\Notice\Driver;

use App\Core\Services\Notice\NoticeService;
use Hyperf\Di\Annotation\Inject;
use App\Core\Utils\Log;

class MultiSystemAccountNotice implements NoticeHandlerInterface
{
    /**
     * @Inject()
     * @var NoticeService
     */
    protected $noticeService;

    /**
     * 处理多系统账号创建通知
     * @param mixed ...$arguments [userInfo, createdAccounts]
     * @return bool
     */
    public function handle(...$arguments)
    {
        [$userInfo, $createdAccounts] = $arguments;

        if (empty($userInfo['biz_mail']) && empty($userInfo['email'])) {
            Log::get()->warning('用户没有邮箱地址，无法发送系统账号创建通知', [
                'userid' => $userInfo['userid'] ?? 'unknown',
                'name' => $userInfo['name'] ?? 'unknown'
            ]);
            return false;
        }

        if (empty($createdAccounts)) {
            Log::get()->warning('没有创建的账号信息', [
                'userid' => $userInfo['userid'] ?? 'unknown'
            ]);
            return false;
        }

        // 获取用户在本系统中的ID
        $userModel = make(\App\Model\TchipBi\UserModel::class);
        $localUser = $userModel::query()->where('workwx_userid', $userInfo['userid'])->first();
        
        if (!$localUser) {
            Log::get()->error('本地用户不存在，无法发送通知', [
                'workwx_userid' => $userInfo['userid']
            ]);
            return false;
        }

        // 准备邮件参数
        $params = [
            'username' => $userInfo['name'] ?? $userInfo['userid'],
            'total_systems' => count($createdAccounts),
            'systems_list' => $this->formatSystemsList($createdAccounts),
            'systems_table' => $this->formatSystemsTable($createdAccounts),
            'created_time' => date('Y-m-d H:i:s'),
            'email_title' => "【系统账号创建通知】为您创建了" . count($createdAccounts) . "个系统账号"
        ];

        // 发送通知
        $this->noticeService->handleNotice('multiSystemAccountCreation', $localUser->id, $params);

        Log::get()->info('账号创建通知已发送', [
            'user_id' => $localUser->id,
            'workwx_userid' => $userInfo['userid'],
            'systems_count' => count($createdAccounts),
            'systems' => array_column($createdAccounts, 'system_name'),
            'email' => $userInfo['biz_mail'] ?? $userInfo['email']
            . "\n" . $params['systems_list']
        ]);

        return true;
    }

    /**
     * 格式化系统列表（用于企微和网站通知）
     */
    private function formatSystemsList(array $accounts): string
    {
        $list = [];
        foreach ($accounts as $account) {
            $status = $account['created'] ? '新建' : '已存在';
            $list[] = "• {$account['system_name']}（{$account['system_description']}）- {$status}";
        }
        return implode("\n", $list);
    }

    /**
     * 格式化系统表格（用于邮件通知）
     */
    private function formatSystemsTable(array $accounts): string
    {
        $table = '<table style="width: 100%; border-collapse: collapse; margin: 20px 0;">';
        $table .= '<thead><tr style="background: #f8f9fa;">';
        $table .= '<th style="border: 1px solid #ddd; padding: 12px; text-align: left;">系统名称</th>';
        $table .= '<th style="border: 1px solid #ddd; padding: 12px; text-align: left;">登录地址</th>';
        $table .= '<th style="border: 1px solid #ddd; padding: 12px; text-align: left;">用户名</th>';
        $table .= '<th style="border: 1px solid #ddd; padding: 12px; text-align: left;">密码</th>';
        // $table .= '<th style="border: 1px solid #ddd; padding: 12px; text-align: left;">角色</th>';
        // $table .= '<th style="border: 1px solid #ddd; padding: 12px; text-align: left;">状态</th>';
        $table .= '</tr></thead><tbody>';

        foreach ($accounts as $account) {
            $table .= '<tr>';
            $table .= '<td style="border: 1px solid #ddd; padding: 12px;"><strong>' . htmlspecialchars($account['system_name']) . '</strong><br><small>' . htmlspecialchars($account['system_description']) . '</small></td>';
            $table .= '<td style="border: 1px solid #ddd; padding: 12px;"><a href="' . htmlspecialchars($account['login_url']) . '" target="_blank">' . htmlspecialchars($account['login_url']) . '</a></td>';
            $table .= '<td style="border: 1px solid #ddd; padding: 12px;"><strong>' . htmlspecialchars($account['account_username']) . '</strong></td>';
            $table .= '<td style="border: 1px solid #ddd; padding: 12px;"><code style="background: #f8f9fa; padding: 2px 4px;">' . htmlspecialchars($account['account_password']) . '</code></td>';
            // $table .= '<td style="border: 1px solid #ddd; padding: 12px;">' . htmlspecialchars($account['role_name']) . '</td>';
            // $table .= '<td style="border: 1px solid #ddd; padding: 12px;"><span style="color: #28a745;">新建</span></td>';
            $table .= '</tr>';
        }

        $table .= '</tbody></table>';
        return $table;
    }
}
