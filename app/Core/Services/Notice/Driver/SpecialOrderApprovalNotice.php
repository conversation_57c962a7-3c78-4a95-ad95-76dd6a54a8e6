<?php 
namespace App\Core\Services\Notice\Driver;

use App\Core\Services\Notice\NoticeService;
use App\Core\Services\UserService;
use Hyperf\Di\Annotation\Inject;
use App\Core\Services\Notice\Driver\NoticeHandlerInterface;
use App\Model\TchipBi\UserModel;

class SpecialOrderApprovalNotice implements NoticeHandlerInterface
{
    /**
     * @Inject()
     * @var UserService
     */
    protected $userService;

    /**
     * @Inject()
     * @var NoticeService
     */
    protected $noticeService;

    /**
     * 处理特采申请单审批通知
     *
     * @param mixed ...$arguments
     * @return bool
     */
    public function handle(...$arguments)
    {
        [$actionType, $orderInfo, $targetUserId, $extraParams] = $arguments;

        // 获取目标用户信息
        $targetUser = UserModel::query()->where('id', $targetUserId)->first();
        if (!$targetUser) {
            return false;
        }

        // 构建通知参数
        $params = $this->buildNoticeParams($actionType, $orderInfo, $targetUser, $extraParams);

        // 统一使用 specialOrderNotice 作为通知类型
        $result = $this->noticeService->handleNotice('specialOrderNotice', $targetUserId, $params);

        return $result;
    }

    /**
     * 构建通知参数
     *
     * @param string $actionType 动作类型
     * @param array $orderInfo 订单信息
     * @param UserModel $targetUser 目标用户
     * @param array $extraParams 额外参数
     * @return array
     */
    private function buildNoticeParams($actionType, $orderInfo, $targetUser, $extraParams = [])
    {
        // 基础参数
        $params = [
            'order_id' => $orderInfo['id'],
            'order_no' => $orderInfo['order_no'] ?? '',
            'applicant_name' => $orderInfo['creator_name'] ?? '',
            'target_user_name' => $targetUser->name,
            'created_at' => $orderInfo['created_at'] ?? date('Y-m-d H:i:s'),
            'host' => env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101'),
        ];

        // QC订单相关信息
        if (isset($orderInfo['qc_order'])) {
            $qcOrder = $orderInfo['qc_order'];
            $params['prod_name'] = $qcOrder['prod_name'] ?? '';
            $params['prod_code'] = $qcOrder['prod_code'] ?? '';
            $params['prod_spec'] = $qcOrder['prod_spec'] ?? '';
            $params['defective_rate'] = $orderInfo['defective_rate'] ?? '0%';
            $params['examine_num'] = $qcOrder['examine_num'] ?? 0;
            $params['defective_num'] = $qcOrder['defective_num'] ?? 0;
            $params['special_reason'] = $orderInfo['special_reason'] ?? '';
        }
        
        // 根据动作类型和目标用户身份添加特定参数
        switch ($actionType) {
            case 'submitted':
            case 'approved':
                // 流转场景：通知下一个审批人
                $params['action_text'] = '您有一个特采订单等待您审批';
                $params['next_action'] = '';
                $params['approval_comment'] = $extraParams['approval_comment'] ?? '';
                break;

            case 'rejected':
                // 驳回场景：根据驳回目标显示不同消息
                $approverName = $extraParams['approver_name'] ?? '';
                $rejectToStep = $extraParams['reject_to_step'] ?? null;

                if ($rejectToStep === 0 || $targetUser->id == $orderInfo['creator_id']) {
                    // 驳回给申请人
                    $params['action_text'] = "您的特采订单被{$approverName}驳回";
                    $params['next_action'] = '请您重新申请';
                } else {
                    // 驳回给其他审批人
                    $params['action_text'] = "您有一张特采订单被{$approverName}驳回";
                    $params['next_action'] = '请您重新操作审批';
                }
                $params['approver_name'] = $approverName;
                $params['reject_reason'] = $extraParams['reject_reason'] ?? '';
                // 驳回时不显示审批意见，只显示驳回原因
                $params['approval_comment'] = '';
                break;

            case 'completed':
                // 完成场景：通知申请人
                $params['action_text'] = '您的特采订单已通过审批';
                $params['next_action'] = '';
                break;

            case 'cancelled':
                // 取消场景：通知相关审批人
                $params['action_text'] = '特采订单已被申请人取消';
                $params['cancel_reason'] = $extraParams['cancel_reason'] ?? '';
                $params['next_action'] = '';
                break;
        }

        // 构建详情链接 - 使用order_no并添加自动打开审批弹窗参数
        $params['detail_url'] = $params['host'] . "/#/production/specialBuyOrderIndex?orderNo=" . $params['order_no'] . "&autoApproval=1";

        // 处理条件显示的内容（企业微信不支持if语句）
        $params['reject_info'] = '';
        $params['cancel_info'] = '';
        $params['action_info'] = '';
        $params['comment_info'] = '';
        $params['reason_info'] = '';

        if (!empty($params['reject_reason'])) {
            $params['reject_info'] = "驳回原因：" . $params['reject_reason'];
        }

        if (!empty($params['cancel_reason'])) {
            $params['cancel_info'] = "取消原因：" . $params['cancel_reason'];
        }

        if (!empty($params['next_action'])) {
            $params['action_info'] = $params['next_action'];
        }

        if (!empty($params['approval_comment'])) {
            $params['comment_info'] = "审批意见：" . $params['approval_comment'];
        }

        if (!empty($params['special_reason'])) {
            $params['reason_info'] = "申请理由：" . $params['special_reason'];
        }

        // 合并额外参数
        return array_merge($params, $extraParams);
    }
}
