<?php

namespace App\Core\Services\FireflyService;

use App\Core\Services\WorkWx\ContactChange\UserAccountServiceInterface;
use App\Core\Utils\Log;
use App\Core\Utils\Random;
use App\Constants\SystemAccountConfig;
use App\Model\FireflyService\AdminModel;
use App\Model\FireflyService\AuthGroupAccessModel;
use App\Model\TchipBi\UserThirdModel;

/**
 * 售后系统用户账号服务
 */
class UserAccountService implements UserAccountServiceInterface
{
    /**
     * 创建用户账号
     */
    public function createUserAccount(array $userInfo): ?array
    {
        $config = SystemAccountConfig::getConfig('firefly_service');
        if (empty($config)) {
            Log::get()->warning("售后系统配置不存在");
            return null;
        }

        // 检查邮箱
        $userEmail = $userInfo['biz_mail'] ?? $userInfo['email'] ?? '';
        if (empty($userEmail)) {
            Log::get()->warning("用户没有邮箱信息，无法创建售后系统账号", [
                'userid' => $userInfo['userid'],
                'name' => $userInfo['name']
            ]);
            return null;
        }

        try {
            $accountInfo = null;
            \Hyperf\DbConnection\Db::connection('firefly_service')->transaction(function () use (
                $userEmail, $userInfo, $config, &$accountInfo
            ) {
                $adminModel = make(AdminModel::class);
                $existingUser = $adminModel::query()->where('email', $userEmail)->first();

                if (!$existingUser) {
                    // 创建新账号
                    $result = $this->createNewAccount($userInfo, $userEmail, $config);
                    if ($result) {
                        // 记录到UserThird表
                        $this->recordUserThird($userInfo, $result['user_id']);

                        $accountInfo = array_merge($config, [
                            'username' => $userInfo['userid'],
                            'account_username' => $userInfo['userid'],
                            'password' => $result['password'],
                            'account_password' => $result['password'],
                            'role_name' => $config['default_role'] ?? '售后组',
                            'user_id' => $result['user_id'],
                            'created' => true
                        ]);
                    }
                } else {
                    // 账号已存在，确保UserThird记录存在
                    $this->recordUserThird($userInfo, $existingUser->id);

                    Log::get()->info("售后系统账号已存在，跳过创建", [
                        'email' => $userEmail,
                        'username' => $userInfo['userid'],
                        'existing_id' => $existingUser->id
                    ]);

                    $accountInfo = array_merge($config, [
                        'username' => $userInfo['userid'],
                        'account_username' => $userInfo['userid'],
                        'password' => '***已存在***',
                        'account_password' => '***已存在***',
                        'role_name' => $config['default_role'] ?? '售后组',
                        'user_id' => $existingUser->id,
                        'created' => false
                    ]);
                }
            });

            return $accountInfo;
        } catch (\Exception $e) {
            Log::get()->error("创建售后系统账号失败", [
                'username' => $userInfo['userid'] ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * 禁用用户账号
     */
    public function disableUserAccount(string $email, string $userName): bool
    {
        try {
            $adminModel = make(AdminModel::class);
            $user = $adminModel::query()->where('email', $email)->first();

            if ($user) {
                $user->update(['status' => 'hidden']);

                // 更新UserThird表中的备注信息
                UserThirdModel::query()
                    ->where('third_user_id', $user->id)
                    ->where('platform', 'firefly_service')
                    ->update(['remark' => '售后管理系统账号(已禁用)']);

                Log::get()->info("成功禁用售后系统账号", [
                    'email' => $email,
                    'username' => $userName,
                    'user_id' => $user->id
                ]);
                return true;
            } else {
                Log::get()->info("售后系统中未找到对应账号", [
                    'email' => $email,
                    'username' => $userName
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::get()->error("禁用售后系统账号失败", [
                'email' => $email,
                'username' => $userName,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 获取系统名称
     */
    public function getSystemName(): string
    {
        return 'Firefly Service';
    }

    /**
     * 检查是否应该为此用户创建账号
     */
    public function shouldCreateAccount(array $userInfo): bool
    {
        $departments = $userInfo['department'] ?? [];
        if (!is_array($departments)) {
            $departments = json_decode($departments, true) ?: [];
        }

        // 售后系统的触发部门：平台销售中心(2)、工程组(13)
        $triggerDepartments = [2, 13];
        return !empty(array_intersect($departments, $triggerDepartments));
    }

    /**
     * 创建新账号的具体逻辑
     */
    private function createNewAccount(array $userInfo, string $userEmail, array $config): ?array
    {
        // 生成密码和盐值
        $salt = $this->generateRandomString(6);
        $randomPassword = $this->generateRandomString(8);
        $encryptedPassword = md5(md5($randomPassword) . $salt);
        $currentTime = time();

        // 创建管理员账号
        $adminModel = make(AdminModel::class);
        $admin = $adminModel::create([
            'username' => $userInfo['userid'],
            'nickname' => $userInfo['name'] ?? $userInfo['userid'],
            'password' => $encryptedPassword,
            'salt' => $salt,
            'email' => $userEmail,
            'avatar' => $userInfo['avatar'] ?? '',
            'loginfailure' => 0,
            'logintime' => null,
            'createtime' => $currentTime,
            'updatetime' => $currentTime,
            'token' => '',
            'status' => 'normal'
        ]);

        // 分配角色
        $authGroupAccessModel = make(AuthGroupAccessModel::class);
        $roleId = $config['role_id'] ?? 6;
        $authGroupAccessModel::create([
            'uid' => $admin->id,
            'group_id' => $roleId
        ]);

        Log::get()->info("售后系统-成功创建账号并分配角色", [
            'username' => $userInfo['userid'],
            'password' => $randomPassword,
            'admin_id' => $admin->id,
            'role_id' => $roleId
        ]);

        return [
            'password' => $randomPassword,
            'user_id' => $admin->id
        ];
    }

    /**
     * 记录用户第三方关联信息
     */
    private function recordUserThird(array $userInfo, int $thirdUserId): void
    {
        try {
            // 获取BI系统中的用户ID
            $biUserModel = make(\App\Model\TchipBi\UserModel::class);
            $biUser = $biUserModel::query()->where('workwx_userid', $userInfo['userid'])->first();

            if ($biUser) {
                // 生成API Key（参考Redmine方式）
                $apiKey = Random::alnum(40);

                UserThirdModel::query()->updateOrCreate(
                    [
                        'user_id' => $biUser->id,
                        'platform' => 'firefly_service'
                    ],
                    [
                        'third_user_id' => $thirdUserId,
                        'api_key' => $apiKey,
                        'remark' => '售后管理系统账号'
                    ]
                );

                Log::get()->info("成功记录售后系统用户关联", [
                    'bi_user_id' => $biUser->id,
                    'firefly_user_id' => $thirdUserId,
                    'username' => $userInfo['userid'],
                    'api_key' => $apiKey
                ]);
            }
        } catch (\Exception $e) {
            Log::get()->error("记录售后系统用户关联失败: " . $e->getMessage(), [
                'username' => $userInfo['userid'],
                'third_user_id' => $thirdUserId
            ]);
        }
    }

    /**
     * 生成随机字符串
     */
    private function generateRandomString(int $length = 6): string
    {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $result = '';
        for ($i = 0; $i < $length; $i++) {
            $result .= $chars[mt_rand(0, strlen($chars) - 1)];
        }
        return $result;
    }
}
