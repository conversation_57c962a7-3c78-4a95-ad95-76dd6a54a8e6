<?php

declare(strict_types=1);

namespace App\Core\Services\TchipOa;

use App\Core\Services\BusinessService;
use App\Model\TchipBi\OaQcModel;
use App\Model\TchipBi\OaQcSpecialOrderModel;
use App\Model\TchipBi\OaQcSpecialOrderApprovalModel;
use App\Model\TchipBi\UserModel;
use App\Exception\AppException;
use App\Constants\StatusCode;
use App\Constants\OaQcSpecialOrderCode;
use App\Core\Services\Notice\Driver\DynamicNoticeFactory;
use Hyperf\Di\Annotation\Inject;
use Hyperf\DbConnection\Db;

use Qbhy\HyperfAuth\AuthManager;
use Carbon\Carbon;

class OaQcSpecialOrderService extends BusinessService
{
    /**
     * @Inject
     * @var OaQcSpecialOrderModel
     */
    protected $model;

    /**
     * @Inject()
     * @var AuthManager
     */
    protected $auth;

    /**
     * 从QC记录创建特采订单
     */
    public function createFromQc(int $qcOrderId, array $applicationData = []): ?OaQcSpecialOrderModel
    {
        // 获取当前用户信息
        $userId = $this->auth->user()->getId();
        $userName = $this->auth->user()->name ?? '未知用户';

        // 检查QC记录是否存在
        $qcRecord = OaQcModel::find($qcOrderId);
        if (!$qcRecord) {
            throw new AppException(StatusCode::ERR_EXCEPTION, 'QC记录不存在');
        }

        // 检查是否已经存在非cancelled状态的特采订单
        $activeOrder = $this->getActiveByQcOrderId($qcOrderId);
        if ($activeOrder) {
            throw new AppException(StatusCode::ERR_EXCEPTION, '该QC记录已经存在进行中的特采订单');
        }

        // 检查QC记录是否有不合格数量
        if ($qcRecord->defective_num <= 0) {
            throw new AppException(StatusCode::ERR_EXCEPTION, '该QC记录没有不合格数量，无需创建特采订单');
        }

        // 生成订单号
        $orderNo = OaQcSpecialOrderModel::generateOrderNo();

        // 自动进入第一步审批流程
        $firstStep = OaQcSpecialOrderCode::STEP_PURCHASE;
        $firstStatus = OaQcSpecialOrderCode::getStatusByStep($firstStep);
        $firstApproverId = OaQcSpecialOrderCode::getNextApproverId($firstStep);

        // 从用户表获取审批人姓名
        $firstApprover = UserModel::find($firstApproverId);
        $firstApproverName = $firstApprover ? $firstApprover->name : '未知用户';

        // 创建特采订单（只保留必要字段，冗余数据通过QC关联获取）
        $orderData = [
            'order_no' => $orderNo,
            'qc_order_id' => $qcOrderId, // 关联QC记录，通过此字段获取产品信息
            'special_reason' => $applicationData['special_reason'] ?? '根据QC检验结果，发现不合格产品需要特采处理',
            'special_scope' => $applicationData['special_scope'] ?? '',
            'status' => $firstStatus,                    // 直接进入第一步审批
            'current_step' => $firstStep,                // 采购审批
            'current_approver_id' => $firstApproverId,   // 当前审批人ID
            'current_approver_name' => $firstApproverName, // 当前审批人姓名
            'creator_id' => $userId,
            'creator_name' => $userName,
        ];

        return Db::transaction(function () use ($orderData, $userId, $userName) {
            // 创建特采订单
            $specialOrder = $this->model->create($orderData);

            // 创建申请人的提交记录
            OaQcSpecialOrderApprovalModel::create([
                'order_id' => $specialOrder->id,
                'order_no' => $specialOrder->order_no,
                'step' => 0, // 申请人提交步骤
                'step_name' => '申请人提交',
                'round' => 1,
                'approver_id' => $userId,
                'approver_name' => $userName,
                'action' => 'submitted',
                'comment' => '提交特采申请',
            ]);

            // 创建第一个审批人的待审批记录
            OaQcSpecialOrderApprovalModel::create([
                'order_id' => $specialOrder->id,
                'order_no' => $specialOrder->order_no,
                'step' => OaQcSpecialOrderCode::STEP_PURCHASE,
                'step_name' => OaQcSpecialOrderCode::getStepText(OaQcSpecialOrderCode::STEP_PURCHASE),
                'round' => 1,
                'approver_id' => $orderData['current_approver_id'],
                'approver_name' => $orderData['current_approver_name'],
                'action' => 'pending',
                'comment' => '等待审批',
            ]);

            // 发送通知给第一个审批人
            $this->sendSubmitNotification($specialOrder, $orderData['current_approver_id']);

            return $specialOrder;
        });
    }

    /**
     * 获取特采订单
     */
    public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        // 提取特殊筛选参数，避免被当作数据库字段处理
        $specialFilters = [];
        $specialKeys = ['myApproval', 'start_date', 'end_date', 'keywords'];

        foreach ($specialKeys as $key) {
            if (isset($filter[$key])) {
                $specialFilters[$key] = $filter[$key];
                unset($filter[$key]);
            }
        }

        [$query, $limit, $sort, $order] = $this->buildparams($filter, $op, $sort, $order, $limit);



        // 处理特殊筛选条件
        $this->handleSpecialFilters($query, $specialFilters);

        return $query->with(['approvals.approver', 'qcOrder', 'currentApprover'])
            ->orderBy($sort, $order)
            ->paginate($limit);
    }

    /**
     * 处理特殊筛选条件
     */
    private function handleSpecialFilters($query, array $filter)
    {
        // 我的审批（包括待审批和已完成的）
        if (isset($filter['myApproval']) && $filter['myApproval']) {
            $userId = $this->auth->user()->getId();
            $query->where('current_approver_id', $userId);
        }

        // 日期范围筛选
        if (isset($filter['start_date']) && isset($filter['end_date'])) {
            $query->whereBetween('created_at', [
                $filter['start_date'] . ' 00:00:00',
                $filter['end_date'] . ' 23:59:59'
            ]);
        }

        // 关键词搜索（产品名称、订单号、QC订单号、入库单号）
        if (isset($filter['keywords']) && $filter['keywords'] !== '') {
            $keywords = $filter['keywords'];
            $query->where(function ($q) use ($keywords) {
                $q->where('order_no', 'like', "%{$keywords}%")
                    ->orWhereHas('qcOrder', function ($qcQuery) use ($keywords) {
                        $qcQuery->where('order_no', 'like', "%{$keywords}%")
                                ->orWhere('prod_name', 'like', "%{$keywords}%")
                                ->orWhere('prod_code', 'like', "%{$keywords}%")
                                ->orWhere('enter_no', 'like', "%{$keywords}%");
                    });
            });
        }
    }

    /**
     * 创建特采申请单（优化版本 - 只保留特采特有字段）
     */
    // public function create(array $data): OaQcSpecialOrderModel
    // {
    //     $userId = $this->auth->user()->getId();
    //     $userName = $this->auth->user()->name ?? '未知用户';

    //     // 生成订单号
    //     $orderNo = OaQcSpecialOrderModel::generateOrderNo();

    //     // 只保留特采特有字段，产品信息通过qc_order_id关联获取
    //     $orderData = [
    //         'order_no' => $orderNo,
    //         'qc_order_id' => $data['qc_order_id'] ?? null, // 关联QC记录
    //         'special_reason' => $data['special_reason'] ?? null,
    //         'improvement_measures' => $data['improvement_measures'] ?? null,
    //         'expected_completion_date' => $data['expected_completion_date'] ?? null,
    //         'special_details' => $data['special_details'] ?? null,
    //         'status' => OaQcSpecialOrderCode::STATUS_PENDING,
    //         'current_step' => OaQcSpecialOrderCode::STEP_PENDING,
    //         'creator_id' => $userId,
    //         'creator_name' => $userName,
    //     ];

    //     return Db::transaction(function () use ($orderData) {
    //         return $this->model->create($orderData);
    //     });
    // }

    /**
     * 审批特采订单
     */
    public function approve(int $id, string $action, string $comment = '', ?int $rejectToStep = null): bool
    {
        $order = $this->model->find($id);
        if (!$order) {
            throw new AppException(StatusCode::ERR_EXCEPTION, '订单不存在');
        }

        $userId = $this->auth->user()->getId();
        $userName = $this->auth->user()->name ?? '未知用户';

        // 检查审批权限
        if (!$order->canApprove($userId)) {
            throw new AppException(StatusCode::ERR_EXCEPTION, '无权限审批此订单');
        }

        return Db::transaction(function () use ($order, $action, $comment, $rejectToStep, $userId, $userName) {
            $now = Carbon::now();

            // 根据驳回类型设置不同的action
            $approvalAction = $action;
            if ($action === OaQcSpecialOrderCode::ACTION_REJECT && $rejectToStep === 0) {
                $approvalAction = OaQcSpecialOrderCode::ACTION_RETURNED;  // 驳回到申请人使用特殊的action
            }

            // 更新或创建审批记录
            // 查找当前步骤的pending记录
            $existingApproval = OaQcSpecialOrderApprovalModel::where('order_id', $order->id)
                ->where('step', $order->current_step)
                ->where('action', 'pending')
                ->first();

            if ($existingApproval) {
                // 更新现有的pending记录
                $updateData = [
                    'action' => $approvalAction,
                    'comment' => $comment,
                    'approved_at' => $now,
                    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                ];

                if ($action === OaQcSpecialOrderCode::ACTION_REJECT && $rejectToStep !== null) {
                    $updateData['reject_to_step'] = $rejectToStep;
                    $updateData['reject_reason'] = $comment;
                }

                $existingApproval->update($updateData);
            } else {
                // 如果没有找到pending记录，创建新记录
                $currentRound = OaQcSpecialOrderApprovalModel::where('order_id', $order->id)
                    ->max('round') ?? 1;

                $approvalData = [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'step' => $order->current_step,
                    'step_name' => OaQcSpecialOrderCode::getStepText($order->current_step),
                    'round' => $currentRound,
                    'approver_id' => $userId,
                    'approver_name' => $userName,
                    'action' => $approvalAction,
                    'comment' => $comment,
                    'approved_at' => $now,
                    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                ];

                if ($action === OaQcSpecialOrderCode::ACTION_REJECT && $rejectToStep !== null) {
                    $approvalData['reject_to_step'] = $rejectToStep;
                    $approvalData['reject_reason'] = $comment;
                }

                OaQcSpecialOrderApprovalModel::create($approvalData);
            }

            // 更新订单状态
            if ($action === OaQcSpecialOrderCode::ACTION_APPROVE) {
                return $this->handleApprove($order, $userId, $comment);
            } else {
                return $this->handleReject($order, $rejectToStep, $comment);
            }
        });
    }

    /**
     * 处理同意审批
     */
    private function handleApprove(OaQcSpecialOrderModel $order, int $userId, string $comment = ''): bool
    {
        $nextStep = $order->getNextStep();

        if ($nextStep > OaQcSpecialOrderCode::STEP_MANAGER) {
            // 审批完成
            $result = $order->update([
                'status' => OaQcSpecialOrderCode::STATUS_APPROVED,
                'current_step' => OaQcSpecialOrderCode::STEP_COMPLETED,
            ]);

            // 发送审批完成通知给申请人
            if ($result) {
                $this->sendApprovalCompletedNotification($order);
            }

            return $result;
        } else {
            // 进入下一步审批
            $nextStatus = OaQcSpecialOrderCode::getStatusByStep($nextStep);
            $nextApproverId = OaQcSpecialOrderCode::getNextApproverId($nextStep);

            // 从用户表获取审批人姓名
            $nextApprover = UserModel::find($nextApproverId);
            $nextApproverName = $nextApprover ? $nextApprover->name : '未知用户';

            $result = $order->update([
                'status' => $nextStatus,
                'current_step' => $nextStep,
                'current_approver_id' => $nextApproverId,
                'current_approver_name' => $nextApproverName,
            ]);

            if ($result) {
                // 获取当前轮次
                $currentRound = OaQcSpecialOrderApprovalModel::where('order_id', $order->id)
                    ->max('round') ?? 1;

                // 创建下一个审批人的待审批记录
                OaQcSpecialOrderApprovalModel::create([
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'step' => $nextStep,
                    'step_name' => OaQcSpecialOrderCode::getStepText($nextStep),
                    'round' => $currentRound,
                    'approver_id' => $nextApproverId,
                    'approver_name' => $nextApproverName,
                    'action' => 'pending',
                    'comment' => '等待审批',
                ]);

                // 发送审批通过通知给下一个审批人
                $this->sendApprovalNotification($order, $nextApproverId, $userId, $comment);
            }

            return $result;
        }
    }

    /**
     * 处理驳回审批
     */
    private function handleReject(OaQcSpecialOrderModel $order, ?int $rejectToStep, string $reason): bool
    {

        if ($rejectToStep !== null) {
            // 检查是否驳回到申请人
            if ($rejectToStep === 0) {
                // 驳回到申请人 - 订单状态变为已退回
                $result = $order->update([
                    'status' => OaQcSpecialOrderCode::STATUS_RETURNED,
                    'current_step' => OaQcSpecialOrderCode::STEP_RETURNED,
                    'current_approver_id' => $order->creator_id,  // 退回给申请人
                    'current_approver_name' => $order->creator_name,
                    'final_remark' => $reason,
                ]);

                // 发送驳回通知给申请人
                if ($result) {
                    $this->sendRejectNotification($order, $order->creator_id, $reason, $rejectToStep);
                }

                return $result;
            } else {
                // 驳回到指定审批步骤
                $rejectStatus = OaQcSpecialOrderCode::getStatusByStep($rejectToStep);
                $rejectApproverId = OaQcSpecialOrderCode::getNextApproverId($rejectToStep);

                // 从用户表获取审批人姓名
                $rejectApprover = UserModel::find($rejectApproverId);
                $rejectApproverName = $rejectApprover ? $rejectApprover->name : '未知用户';

                $result = $order->update([
                    'status' => $rejectStatus,
                    'current_step' => $rejectToStep,
                    'current_approver_id' => $rejectApproverId,
                    'current_approver_name' => $rejectApproverName,
                    'final_remark' => $reason,
                ]);

                // 发送驳回通知给目标审批人
                if ($result) {
                    $this->sendRejectNotification($order, $rejectApproverId, $reason, $rejectToStep);
                }

                return $result;
            }
        } else {
            // 直接驳回 - 订单彻底失败，保留驳回人信息
            $result = $order->update([
                'status' => OaQcSpecialOrderCode::STATUS_REJECTED,
                'current_step' => OaQcSpecialOrderCode::STEP_COMPLETED,
                // 不清除 current_approver_id，保持为驳回人
                'final_remark' => $reason,
            ]);

            // 发送直接驳回通知给申请人
            if ($result) {
                $this->sendRejectNotification($order, $order->creator_id, $reason, null);
            }

            return $result;
        }
    }

    /**
     * 重新提交被退回的订单
     */
    public function resubmit(int $id, array $updateData = []): ?OaQcSpecialOrderModel
    {
        $order = $this->model->find($id);
        if (!$order) {
            throw new AppException(StatusCode::ERR_EXCEPTION, '订单不存在');
        }

        // 检查订单状态是否为已退回
        if ($order->status !== OaQcSpecialOrderCode::STATUS_RETURNED) {
            throw new AppException(StatusCode::ERR_EXCEPTION, '只有已退回的订单才能重新提交');
        }

        // 检查是否是申请人本人
        $userId = $this->auth->user()->getId();
        if ($order->creator_id !== $userId) {
            throw new AppException(StatusCode::ERR_EXCEPTION, '只有申请人本人才能重新提交');
        }

        $userName = $this->auth->user()->name ?? '未知用户';

        return Db::transaction(function () use ($order, $updateData, $userId, $userName) {
            // 更新订单信息（如果有修改）
            if (!empty($updateData)) {
                $allowedFields = ['special_reason'];
                $filteredData = array_intersect_key($updateData, array_flip($allowedFields));
                if (!empty($filteredData)) {
                    $order->update($filteredData);
                }
            }

            // 重新进入第一步审批流程
            $firstStep = OaQcSpecialOrderCode::STEP_PURCHASE;
            $firstStatus = OaQcSpecialOrderCode::getStatusByStep($firstStep);
            $firstApproverId = OaQcSpecialOrderCode::getNextApproverId($firstStep);

            // 从用户表获取审批人姓名
            $firstApprover = UserModel::find($firstApproverId);
            $firstApproverName = $firstApprover ? $firstApprover->name : '未知用户';

            // 更新订单状态
            $order->update([
                'status' => $firstStatus,
                'current_step' => $firstStep,
                'current_approver_id' => $firstApproverId,
                'current_approver_name' => $firstApproverName,
                'final_remark' => null,  // 清除之前的驳回原因
            ]);

            // 计算新的轮次（当前最大轮次 + 1）
            $currentMaxRound = OaQcSpecialOrderApprovalModel::where('order_id', $order->id)
                ->max('round') ?? 0;
            $newRound = $currentMaxRound + 1;

            // 创建申请人的重新提交记录
            OaQcSpecialOrderApprovalModel::create([
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'step' => 0, // 申请人提交步骤
                'step_name' => '申请人提交',
                'round' => $newRound,
                'approver_id' => $userId,
                'approver_name' => $userName,
                'action' => 'resubmitted',
                'comment' => '重新提交特采申请',
            ]);

            // 创建第一个审批人的待审批记录
            OaQcSpecialOrderApprovalModel::create([
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'step' => $firstStep,
                'step_name' => OaQcSpecialOrderCode::getStepTextMap()[$firstStep],
                'round' => $newRound,
                'approver_id' => $firstApproverId,
                'approver_name' => $firstApproverName,
                'action' => 'pending',
                'comment' => '等待审批',
            ]);

            // 发送通知给第一个审批人
            $this->sendSubmitNotification($order, $firstApproverId);

            return $order;
        });
    }

    /**
     * 更新特采订单信息
     */
    public function updateOrderInfo(int $id, array $requestData): bool
    {
        $order = $this->model->find($id);
        if (!$order) {
            throw new AppException(StatusCode::ERR_EXCEPTION, '订单不存在');
        }

        $userId = $this->auth->user()->getId();

        // 定义允许更新的字段及其权限要求
        $fieldPermissions = [
            'special_details' => 'quality_manager_only', // 只有品质经理可以修改
            'special_reason' => 'any', // 其他权限规则，暂时允许任何人修改
        ];

        // 检查字段权限并提取数据
        $updateData = [];
        foreach ($fieldPermissions as $field => $permission) {
            if (array_key_exists($field, $requestData)) {
                // 检查字段权限
                if (!$this->checkFieldPermission($field, $permission, $userId)) {
                    throw new AppException(StatusCode::ERR_EXCEPTION, "无权限修改字段: {$field}");
                }

                $updateData[$field] = $requestData[$field];
            }
        }

        if (empty($updateData)) {
            throw new AppException(StatusCode::ERR_EXCEPTION, '没有可更新的字段');
        }

        return $order->update($updateData);
    }

    /**
     * 检查字段权限
     */
    private function checkFieldPermission(string $field, string $permission, int $userId): bool
    {
        switch ($permission) {
            case 'quality_manager_only':
                // 只有品质经理可以修改
                $qualityManagerId = OaQcSpecialOrderCode::getNextApproverId(OaQcSpecialOrderCode::STEP_QUALITY);
                return $userId === $qualityManagerId;

            case 'creator_only':
                // 只有创建人可以修改（示例，可根据需要实现）
                return true; // 这里需要传入订单信息来判断

            case 'any':
                // 任何人都可以修改
                return true;

            default:
                // 未知权限类型，默认拒绝
                return false;
        }
    }

    /**
     * 获取订单详情
     */
    public function getDetail(int $id): ?OaQcSpecialOrderModel
    {
        $order = $this->model->with(['approvals.approver', 'qcOrder', 'currentApprover'])->find($id);

        if ($order) {
            // 获取所有审批步骤的审批人信息
            $allApprovers = $this->getAllApproversInfo();
            $order->all_approvers = $allApprovers;
        }

        return $order;
    }

    /**
     * 获取所有审批人信息
     */
    private function getAllApproversInfo(): array
    {
        $approvers = [];

        // 获取所有审批步骤的审批人ID
        $steps = [
            OaQcSpecialOrderCode::STEP_PURCHASE,
            OaQcSpecialOrderCode::STEP_QUALITY,
            OaQcSpecialOrderCode::STEP_PMC,
            OaQcSpecialOrderCode::STEP_MANAGER,
        ];

        foreach ($steps as $step) {
            $approverId = OaQcSpecialOrderCode::getNextApproverId($step);
            if ($approverId) {
                $user = UserModel::find($approverId);
                if ($user) {
                    $approvers[$step] = [
                        'id' => $user->id,
                        'name' => $user->name,
                        'department' => $user->department_text ?? '',
                        'position' => $user->position ?? '',
                        'avatar' => $user->avatar ?? '',
                    ];
                }
            }
        }

        return $approvers;
    }

    /**
     * 取消订单
     */
    public function cancel(int $id, string $reason = ''): bool
    {
        $order = $this->model->find($id);
        if (!$order) {
            throw new AppException(StatusCode::ERR_EXCEPTION, '订单不存在');
        }

        $userId = $this->auth->user()->getId();
        $userName = $this->auth->user()->name ?? '未知用户';

        if ($order->creator_id !== $userId) {
            throw new AppException(StatusCode::ERR_EXCEPTION, '只能取消自己创建的订单');
        }

        if ($order->isCompleted()) {
            throw new AppException(StatusCode::ERR_EXCEPTION, '订单已完成，无法取消');
        }

        return Db::transaction(function () use ($order, $reason, $userId, $userName) {
            // 获取当前轮次
            $currentRound = OaQcSpecialOrderApprovalModel::where('order_id', $order->id)
                ->max('round') ?? 1;

            // 创建取消记录到审批历史
            OaQcSpecialOrderApprovalModel::create([
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'step' => $order->current_step, // 当前步骤
                'step_name' => '申请人取消',
                'round' => $currentRound,
                'approver_id' => $userId,
                'approver_name' => $userName,
                'action' => 'cancelled',
                'comment' => $reason ?: '申请人主动取消',
                'approved_at' => Carbon::now(),
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            ]);

            // 更新订单状态，保留取消人信息
            $result = $order->update([
                'status' => OaQcSpecialOrderCode::STATUS_CANCELLED,
                'current_step' => OaQcSpecialOrderCode::STEP_COMPLETED,
                'current_approver_id' => $userId,  // 设置为取消人
                'current_approver_name' => $userName,  // 设置为取消人姓名
                'final_remark' => $reason,
            ]);

            // 发送取消通知给相关审批人
            if ($result) {
                $this->sendCancelNotification($order, $reason);
            }

            return $result;
        });
    }

    /**
     * 根据QC订单ID获取特采订单
     */
    public function getByQcOrderId(int $qcOrderId): ?OaQcSpecialOrderModel
    {
        return $this->model->where('qc_order_id', $qcOrderId)->first();
    }

    /**
     * 根据QC订单ID获取非cancelled状态的特采订单
     */
    public function getActiveByQcOrderId(int $qcOrderId): ?OaQcSpecialOrderModel
    {
        return $this->model->where('qc_order_id', $qcOrderId)
                          ->where('status', '!=', 'cancelled')
                          ->first();
    }

    /**
     * 检查QC记录是否已有特采订单（非cancelled状态）
     */
    public function checkSpecialOrderExists(int $qcOrderId): array
    {
        if (!$qcOrderId) {
            throw new AppException(StatusCode::ERR_EXCEPTION, 'QC订单ID不能为空');
        }

        // 只检查非cancelled状态的订单
        $activeOrder = $this->getActiveByQcOrderId($qcOrderId);

        if ($activeOrder) {
            return [
                'exists' => true,
                'order' => [
                    'id' => $activeOrder->id,
                    'order_no' => $activeOrder->order_no,
                    'status' => $activeOrder->status,
                ]
            ];
        }

        return ['exists' => false];
    }

    /**
     * 发送审批通知给下一个审批人
     */
    private function sendApprovalNotification($order, $targetUserId, $approverId, $comment = '')
    {
        try {
            // 获取审批人信息
            $approver = UserModel::find($approverId);
            $approverName = $approver ? $approver->name : '未知用户';

            // 获取订单详细信息（包含QC订单）
            $orderInfo = $this->getOrderWithQcInfo($order);

            $extraParams = [
                'approver_name' => $approverName,
                'next_approver' => true,
                'approval_comment' => $comment,
            ];

            DynamicNoticeFactory::call(
                'SpecialOrderApprovalNotice',
                'approved',
                $orderInfo,
                $targetUserId,
                $extraParams
            );
        } catch (\Exception $e) {
        }
    }

    /**
     * 发送审批完成通知给申请人、QC所有人和参与所有人
     */
    private function sendApprovalCompletedNotification($order)
    {
        try {
            // 获取订单详细信息（包含QC订单）
            $orderInfo = $this->getOrderWithQcInfo($order);

            $extraParams = [
                'final_result' => '通过',
            ];

            // 获取需要通知的用户ID列表
            $notifyUserIds = $this->getNotifyUserIds($order);

            // 批量发送通知
            $this->sendBatchNotification(
                'SpecialOrderApprovalNotice',
                'completed',
                $orderInfo,
                $notifyUserIds,
                $extraParams
            );
        } catch (\Exception $e) {
        }
    }

    /**
     * 发送驳回通知
     */
    private function sendRejectNotification($order, $targetUserId, $reason, $rejectToStep)
    {
        try {
            // 获取当前审批人信息
            $currentUserId = $this->auth->user()->getId();
            $approver = UserModel::find($currentUserId);
            $approverName = $approver ? $approver->name : '未知用户';

            // 获取订单详细信息（包含QC订单）
            $orderInfo = $this->getOrderWithQcInfo($order);

            $extraParams = [
                'approver_name' => $approverName,
                'reject_reason' => $reason,
                'reject_to_step' => $rejectToStep,
                // 驳回时不传递approval_comment，避免与reject_reason重复
            ];

            DynamicNoticeFactory::call(
                'SpecialOrderApprovalNotice',
                'rejected',
                $orderInfo,
                $targetUserId,
                $extraParams
            );
        } catch (\Exception $e) {
        }
    }

    /**
     * 发送取消通知给QC所有人和参与所有人
     */
    private function sendCancelNotification($order, $reason)
    {
        try {
            // 获取订单详细信息（包含QC订单）
            $orderInfo = $this->getOrderWithQcInfo($order);

            $extraParams = [
                'cancel_reason' => $reason ?: '申请人主动取消',
            ];

            // 获取需要通知的用户ID列表
            $notifyUserIds = $this->getNotifyUserIds($order);

            // 批量发送通知
            $this->sendBatchNotification(
                'SpecialOrderApprovalNotice',
                'cancelled',
                $orderInfo,
                $notifyUserIds,
                $extraParams
            );
        } catch (\Exception $e) {
        }
    }

    /**
     * 发送提交申请通知给第一个审批人
     */
    private function sendSubmitNotification($order, $targetUserId)
    {
        try {
            // 获取订单详细信息（包含QC订单）
            $orderInfo = $this->getOrderWithQcInfo($order);

            $extraParams = [];

            DynamicNoticeFactory::call(
                'SpecialOrderApprovalNotice',
                'submitted',
                $orderInfo,
                $targetUserId,
                $extraParams
            );
        } catch (\Exception $e) {
        }
    }

    /**
     * 获取需要通知的用户ID列表（QC所有人 + 参与所有人）
     */
    private function getNotifyUserIds($order): array
    {
        $userIds = [];

        // 添加申请人
        if ($order->creator_id) {
            $userIds[] = $order->creator_id;
        }

        // 添加所有参与审批的人员（从审批记录中获取）
        if ($order->approvals) {
            foreach ($order->approvals as $approval) {
                if ($approval->approver_id) {
                    $userIds[] = $approval->approver_id;
                }
            }
        }

        // 添加QC部门所有人员
        $qcUserIds = $this->getQcDepartmentUserIds();
        $userIds = array_merge($userIds, $qcUserIds);

        // 添加固定的关键人员（可配置）
        $keyUserIds = $this->getKeyNotifyUserIds();
        $userIds = array_merge($userIds, $keyUserIds);

        // 去重并过滤空值
        return array_values(array_unique(array_filter($userIds)));
    }

    /**
     * 获取关键通知人员ID（可配置的固定人员列表）
     */
    private function getKeyNotifyUserIds(): array
    {
        // 这里可以配置需要固定通知的关键人员
        // 比如：QC主管、生产经理、品质经理等
        return [];
    }

    /**
     * 获取QC部门所有人员ID
     */
    private function getQcDepartmentUserIds(): array
    {
        try {
            // 使用UserService获取QC角色的用户
            $userService = make(\App\Core\Services\UserService::class);
            $qcUsers = $userService->getUsersByRole(OaQcSpecialOrderCode::QcRole);

            // 提取用户ID
            return array_column($qcUsers, 'id');
        } catch (\Exception $e) {
            // 如果查询失败，返回固定的QC用户ID
            return []; // 至少包含品质经理
        }
    }

    /**
     * 批量发送通知
     */
    private function sendBatchNotification(string $noticeType, string $actionType, array $orderInfo, array $userIds, array $extraParams = []): void
    {
        foreach ($userIds as $userId) {
            try {
                DynamicNoticeFactory::call(
                    $noticeType,
                    $actionType,
                    $orderInfo,
                    $userId,
                    $extraParams
                );
            } catch (\Exception $e) {
                // 单个用户发送失败不影响其他用户
                continue;
            }
        }
    }

    /**
     * 获取包含QC订单信息的订单数据
     */
    private function getOrderWithQcInfo($order)
    {
        // 重新加载订单以获取最新数据和关联信息
        return $this->model->with(['qcOrder', 'approvals'])->find($order->id)->toArray();
    }
}
