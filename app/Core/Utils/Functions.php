<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date      2022/2/17 上午11:43
 * <AUTHOR>
 * @Description
 *
 */

declare(strict_types=1);

use App\Constants\CrontabCode;
use App\Constants\StatusCode;
use App\Core\Services\UserService;
use App\Core\Utils\Random;
use App\Exception\AppException;
use App\Model\TchipBi\AttachmentModel;
    use App\Model\TchipBi\UserThirdModel;
    use Hyperf\Context\Context;
use Hyperf\Contract\ContainerInterface;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Utils\ApplicationContext;
use Hyperf\Validation\Contract\ValidatorFactoryInterface;
use Jenssegers\Agent\Agent;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Psr\Http\Message\ServerRequestInterface;
use Qbhy\HyperfAuth\AuthManager;
use Qbhy\HyperfAuth\Exception\AuthException;


if (!function_exists('di')) {
    /**
     * 容器实例
     *
     * @return ContainerInterface
     */
    function di(): ContainerInterface
    {
        return ApplicationContext::getContainer();
    }
}
if (!function_exists('validate')) {
    /**
     * 自定义验证器
     *
     * @param mixed ...$arg
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    function validate(...$arg)
    {
        $validator = di()->get(ValidatorFactoryInterface::class)->make(...$arg);
        if ($validator->fails()) {
            throw new AppException(\App\Constants\StatusCode::VALIDATION_ERROR,$validator->errors()->first());
        }
    }
}

if (!function_exists('transHump')) {
    /**
     * 将字符串格式转化为驼峰法
     */
    function transHump($str): string
    {
        $strArr = explode('_', $str);

        if (is_array($strArr)) {
            $strHump = strtolower($strArr[0]);
            for ($i = 1; $i < count($strArr); $i++) {
                $strHump .= ucwords(strtolower($strArr[$i]));
            }
            return $strHump;
        } else {
            return $str;
        }
    }
}

if (!function_exists('getLogArguments')) {
    /**
     * getLogArguments
     * 获取要存储的日志部分字段，monolog以外的业务信息
     * @param $message
     * @param int $executionTime 程序执行时间，运行时才能判断这里初始化为0
     * @param int $rbs 响应包体大小，初始化0，只有正常请求响应才有值
     * @return array
     */
    function getLogArguments($message, $executionTime = 0, $rbs = 0)
    {
        if (Context::get('http_request_flag') === true) {
            $request           = ApplicationContext::getContainer()->get(RequestInterface::class);
            $requestHeaders    = $request->getHeaders();
            $serverParams      = $request->getServerParams();
            $arguments         = $request->all();
            $url               = $request->fullUrl();
            $headers['token']  = $request->header('token');
            $headers['client'] = $request->header('client');
        } else {
            $requestHeaders = $serverParams = $arguments = [];
            $url            = '';
        }
        $agent = new Agent();
        $agent->setUserAgent($requestHeaders['user-agent'][0] ?? '');
        $ip                 = $requestHeaders['x-real-ip'][0] ?? $requestHeaders['x-forwarded-for'][0] ?? '';
        $logData['message'] = sprintf('[%s] || uri=%s || %s', $executionTime, $serverParams['request_uri'] ?? '', $message);

        $logData['context'] = [
            'qid' => $requestHeaders['qid'][0]??'',
            'server_name'        => $requestHeaders['host'][0] ?? '',
            'server_addr'        => getServerLocalIp() ?? '',
            'remote_addr'        => $serverParams['remote_addr'] ?? '',
            'forwarded_for'      => $requestHeaders['x-forwarded-for'][0] ?? '',
            'real_ip'            => $ip,
            'user_agent'         => $requestHeaders['user-agent'][0] ?? '',
            'platform'           => $agent->platform() ?? '',
            'device'             => $agent->device() ?? '',
            'browser'            => $agent->browser() ?? '',
            'url'                => $url,
            'uri'                => $serverParams['request_uri'] ?? '',
            'headers'            => json_encode($headers),
            'arguments'          => $arguments ? json_encode($arguments) : '',
            'method'             => $serverParams['request_method'] ?? '',
            'execution_time'     => $executionTime,
            'request_body_size'  => $requestHeaders['content-length'][0] ?? '',
            'response_body_size' => $rbs,
            'referer'            => $requestHeaders['referer'][0] ?? '',
            'unix_time'          => $serverParams['request_time'] ?? '',
        ];

        return $logData;
    }
}

if (!function_exists('getServerLocalIp')) {
    /**
     * getServerLocalIp
     * 获取服务端内网ip地址
     * @return string
     */
    function getServerLocalIp(): string
    {
        $ip  = '127.0.0.1';
        $ips = array_values(swoole_get_local_ip());
        foreach ($ips as $v) {
            if ($v && $v != $ip) {
                $ip = $v;
                break;
            }
        }

        return $ip;
    }
}

if (!function_exists('getToken')) {
    /**
     * 从上报数据获取 token
     * @return string
     */
    function getToken(): string
    {
        if ($token = Context::get('token')) {
            return $token;
        }
        $request = ApplicationContext::getContainer()->get(RequestInterface::class);
        $token   = $request->input('token', '');
        if (!$token) {
            $token = $request->header('authorization', '');
        }
        $token = ($token && strpos($token, 'Bearer ') === 0) ? str_replace('Bearer ', '', $token) : $token;
        return $token;
    }
}

if (!function_exists('getRequestClient')) {
    /**
     * 从上报数据获取请求客户端
     * @return string
     */
    function getRequestClient(): string
    {
        if ($requestClient = Context::get('request_client')) {
            return $requestClient;
        }
        $request       = ApplicationContext::getContainer()->get(RequestInterface::class);
        $requestClient = $request->input('client', '');
        if (!$requestClient) {
            $requestClient = $request->header('client', '');
        }
        return $requestClient;
    }
}

if (!function_exists('getCache')) {
    /**
     * 获取缓存
     * @param $key
     * @param $default
     * @return mixed
     */
    function getCache($key, $default = '')
    {
        $cache = ApplicationContext::getContainer()->get(\Psr\SimpleCache\CacheInterface::class);
        return $cache->get($key, $default);
    }
}

if (!function_exists('setCache')) {
    /**
     * 设置缓存
     * @param $key
     * @param $value
     * @param $ttl
     * @return bool
     */
    function setCache($key, $value, $ttl = 3600)
    {
        $cache = ApplicationContext::getContainer()->get(\Psr\SimpleCache\CacheInterface::class);
        return $cache->set($key, $value, $ttl);
    }
}

if (!function_exists('delCache')) {
    /**
     * 删除缓存，1条/多条
     * @param $keys
     * @return bool
     * @throws \Psr\SimpleCache\InvalidArgumentException
     */
    function delCache($keys): bool
    {
        $cache = make(\Psr\SimpleCache\CacheInterface::class);
        if(is_array($keys)){
            $cache->deleteMultiple($keys);
        }else{
            $cache->delete($keys);
        }
        return true;
    }
}


if (!function_exists('getFullUrl')) {
    /**
     * 补全CDN地址
     * @param $url
     * @return string
     */
    function getFullUrl($url): string
    {
        if (empty($url)) {
            return "";
        }

        if (stristr($url, "http") === false) {
            if (env('OSS_PATH')) {
                return env('OSS_PATH') . $url;
            }
        }

        return $url;
    }

}

if (!function_exists('detectEncoding')) {
    /**
     * 检测文件编码
     * @param string $file 文件路径
     * @return string|null 返回 编码名 或 null
     */
    function detectEncoding($file)
    {
        $list = array('GBK', 'UTF-8', 'UTF-16LE', 'UTF-16BE', 'ISO-8859-1');
        $str  = file_get_contents($file);
        foreach ($list as $item) {
            $tmp = mb_convert_encoding($str, $item, $item);
            if (md5($tmp) == md5($str)) {
                return $item;
            }
        }
        return null;
    }
}
if (!function_exists('autoRead')) {
    /**
     * 自动解析编码读入文件
     * @param string $file    文件路径
     * @param string $charset 读取编码
     * @return string 返回读取内容
     */
    function autoRead($file, $charset = 'UTF-8')
    {
        $list = array('GBK', 'UTF-8', 'UTF-16LE', 'UTF-16BE', 'ISO-8859-1');
        $str  = file_get_contents($file);
        foreach ($list as $item) {
            $tmp = mb_convert_encoding($str, $item, $item);
            if (md5($tmp) == md5($str)) {
                return mb_convert_encoding($str, $charset, $item);
            }
        }
        //没有检测到则直接原内容返回
        return $str;
        // return "";
    }
}
if (!function_exists('getDirAllFiles')) {
    /**

     * @return string 返回读取内容
     */
    /**
     * @param array $arr_file 目录下的所有文件
     * @param string $directory 需要检查的目录
     * @param $dir_name
     * @return void
     */
    function getDirAllFiles(&$arr_file, $directory)
    {
        $mydir = dir($directory);
        while ($file = $mydir->read()) {
            if ((is_dir("$directory/$file")) and ($file != ".") and ($file != "..")) {
                getDirAllFiles($arr_file, "$directory/$file");
            } else if (($file != ".") and ($file != "..")) {
                $arr_file[] = "$directory/$file";
            }
        }
        $mydir->close();
    }
}

if (!function_exists('utf16_to_utf8')) {
    /**
     * 将16位utf16转化为utf8
     * @param string $str
     * @return string
     */
    function utf16_to_utf8($str)
    {
        $c0 = ord($str[0]);
        $c1 = ord($str[1]);
        if ($c0 == 0xFE && $c1 == 0xFF) {
            $be = true;
        } else if ($c0 == 0xFF && $c1 == 0xFE) {
            $be = false;
        } else {
            return $str;
        }
        $str = substr($str, 2);
        $len = strlen($str);
        $dec = '';
        for ($i = 0; $i < $len; $i += 2) {
            $c = ($be) ? ord($str[$i]) << 8 | ord($str[$i + 1]) : ord($str[$i + 1]) << 8 | ord($str[$i]);
            if ($c >= 0x0001 && $c <= 0x007F) {
                $dec .= chr($c);
            } else if ($c > 0x07FF) {
                $dec .= chr(0xE0 | (($c >> 12) & 0x0F));
                $dec .= chr(0x80 | (($c >> 6) & 0x3F));
                $dec .= chr(0x80 | (($c >> 0) & 0x3F));
            } else {
                $dec .= chr(0xC0 | (($c >> 6) & 0x1F));
                $dec .= chr(0x80 | (($c >> 0) & 0x3F));
            }
        }
        return $dec;
    }
}

if(!function_exists('getRedmineUserId')){
    /**
     * 获取redmine userid
     * @return \Hyperf\Utils\HigherOrderTapProxy|mixed|void
     */
    function getRedmineUserId(){
        $third = make(\App\Core\Services\UserService::class)->getThird();
        return $third ? $third->third_user_id : null;
    }
}

if(!function_exists('getRedmineApikey')){
    /**
     * 获取redmine apikey
     * @return \Hyperf\Utils\HigherOrderTapProxy|mixed|void
     */
    function getRedmineApikey(){
        return make(\App\Core\Services\Redmine\UserService::class)->getToken();
        // return make(\App\Core\Services\UserService::class)->getThird()->api_key;
    }
}

if (!function_exists('formatRedmineSettingDefaultTrackerId')) {
    /**
     * 转换项目默认任务类型IDS
     * @param $value
     * @return array|mixed
     */
    function formatRedmineSettingDefaultTrackerId($value)
    {
        preg_match_all('/\'[0-9]*\'/', $value, $arr);
        $arr = !empty($arr[0]) ? $arr[0] : [];
        foreach ($arr as &$a) {
            $a = (int) str_replace('\'', '', $a);
        }
        return $arr;
    }
}

if (!function_exists('byteSize')) {
    function byteSize ($file_size)
    {
        $file_size = $file_size-1;
        if ($file_size >= 1099511627776) $show_filesize = number_format(($file_size / 1099511627776),2) . " TB";
        elseif ($file_size >= 1073741824) $show_filesize = number_format(($file_size / 1073741824),2) . " GB";
        elseif ($file_size >= 1048576) $show_filesize = number_format(($file_size / 1048576),2) . " MB";
        elseif ($file_size >= 1024) $show_filesize = number_format(($file_size / 1024),2) . " KB";
        elseif ($file_size > 0) $show_filesize = $file_size . " b";
        elseif ($file_size == 0 || $file_size == -1) $show_filesize = "0 b";
        return $show_filesize;
    }
}

if (!function_exists('num_format')) {
    function num_format($num,$dec=2){
        if(!is_numeric($num)){
            return false;
        }

        if($num==0){
            return sprintf("%.".$dec."f",'0.00');
        }
        $num = sprintf("%.".$dec."f",$num);
        $num = explode('.',$num);//把整数和小数分开
        $is_negative = false;
        if($num[0] < 0){
            $is_negative = true;
            $num[0] = abs($num[0]);
        }
        $rl = $num[1];//小数部分的值
        $j = strlen($num[0]) % 3;//整数有多少位
        $sl = substr($num[0], 0, $j);//前面不满三位的数取出来
        $sr = substr($num[0], $j);//后面的满三位的数取出来
        $i = 0;
        $rvalue = '';
        while($i <= strlen($sr)){
            $rvalue = $rvalue.','.substr($sr, $i, 3);//三位三位取出再合并，按逗号隔开
            $i = $i + 3;
        }
        $rvalue = $sl.$rvalue;
        $rvalue = substr($rvalue,0,strlen($rvalue)-1);//去掉最后一个逗号
        $rvalue = explode(',',$rvalue);//分解成数组
        if($rvalue[0]==0){
            array_shift($rvalue);//如果第一个元素为0，删除第一个元素
        }
        $rv = $rvalue[0];//前面不满三位的数
        for($i = 1; $i < count($rvalue); $i++){
            $rv = $rv.','.$rvalue[$i];
        }
        if(!empty($rl)){
            $rvalue = $rv.'.'.$rl;//小数不为空，整数和小数合并
        }else{
            $rvalue = $rv;//小数为空，只有整数
        }
        if($is_negative){
            $rvalue = '-'.$rvalue;
        }
        return $rvalue;
    }
}

/**
 * 获得当前年份汇率nowUsdRate
 * @param int $currency_id 币种ID 默认为53=美元
 */
if(!function_exists('exchangeRateByDate')){
    function exchangeRateByDate($datetime = null, $currencyId)
    {
        $datetime  = $datetime ? : date('Y-m-d');
        $date = date('Y', strtotime($datetime));
        $rate = \App\Model\TchipSale\CurrencyRateModel::query(true)->where('year_date', $date)
            ->where('currency_id', $currencyId)->where('delete_time', 0)->value('rate');
        // if (!$rate) {
        //     $rate = \App\Model\TchipSale\ConfigModel::query()->where('keyword', 'CFG_RATE')->value('vals');
        // }
        return $rate ?? 0;
    }
}

if(!function_exists('nowUsdRate')){
    function nowUsdRate($datetime = null)
    {
        $rate = exchangeRateByDate($datetime, \App\Constants\TchipSaleCode::CURRENCY_DOLLAR);
        return $rate ? : 6.81;
    }
}

if(!function_exists('nowHkbRate')){
    function nowHkbRate($datetime = null)
    {
        $rate = exchangeRateByDate($datetime, \App\Constants\TchipSaleCode::CURRENCY_HKB);
        return $rate ? : 0.88;
    }
}

if (!function_exists('getQuarter')) {
    /**
     * 获取当前季度
     * @param $data
     * @return int
     */
    function getQuarter($data = null): int
    {
        $data = $data ? $data : date('Y-m-d');
        $month = date('m', strtotime($data));
        $quater = 0;
        switch ($month) {
            case 1:
            case 2:
            case 3:
                $quater = 1;
                break;
            case 4:
            case 5:
            case 6:
                $quater = 2;
                break;
            case 7:
            case 8:
            case 9:
                $quater = 3;
                break;
            case 10:
            case 11:
            case 12:
                $quater = 4;
                break;
        }
        return $quater;
    }
}

if(!function_exists('getUnitNum')){
    /**
     * 单位转换
     * @param $unit
     * @return int
     */
    function getUnitNum($unit): int
    {
        $num = 0;
        switch ($unit) {
            case '百':
                $num = 100;
                break;
            case '千':
            case 'k':
                $num = 1000;
                break;
            case '万':
            case 'w':
                $num = 10000;
                break;
            case '亿':
                $num = 100000000;
                break;
        }
        return $num;
    }
}

if(!function_exists('reportPeriod')){
    /**
     * 获取默认统计周报的周期
     * @param $date
     * @return array
     */
    function reportPeriod($date, $thisWeek = false)
    {
        if ($thisWeek) {
            $config = ['start' => ['work'=> 'now', 'day' => 'mon', 'time' => '00:00:00'], 'end' => ['work'=> 'now', 'day' => 'fri', 'time' => '16:00:00']];
        } else {
            $config = make(\App\Model\TchipBi\ConfigModel::class)::query()->where('name', 'report_period')->first();
            $config = !empty($config->value_text) ? $config->value_text : ['start' => ['work'=> 'now', 'day' => 'mon', 'time' => '10:00:00'], 'end' => ['work'=> 'next', 'day' => 'mon', 'time' => '10:00:00']];
        }
        $period = [];
        foreach ($config as $key => $con) {
            $nowWeek = date('N', strtotime($date));
            $weekList = array_flip(\App\Core\Utils\TimeUtils::weekList());
            $time = date('H:i');
            switch ($con['work']) {
                case 'prev' :
                    if ($weekList[$nowWeek] == $con['day'] && $time < $con['time']) {
                        $weekDay = date('Y-m-d', strtotime('- 2 week', strtotime($date)));
                    } else {
                        $weekDay = date('Y-m-d', strtotime('- 1 week', strtotime($date)));
                    }
                    break;
                case 'next' :
                    if ($weekList[$nowWeek] == $con['day'] && $time < $con['time']) {
                        $weekDay = $date;
                    } else {
                        $weekDay = date('Y-m-d', strtotime('+ 1 week', strtotime($date)));
                    }
                    break;
                default:
                    if ($weekList[$nowWeek] == $con['day'] && $time < $con['time']) {
                        $weekDay = date('Y-m-d', strtotime('-1 week', strtotime($date)));
                    } else {
                        $weekDay = $date;
                    }
            }
            $period[$key] = \App\Core\Utils\TimeUtils::assignWeekDay($weekDay, $con['day']);
            $period[$key] = $period[$key] .' '. $con['time'];
        }
        return array_values($period);
    }
}

if (!function_exists('formatRedmineHeighthl')) {
    /**
     * 制定方法转换redmin任务说明中的高亮语法
     * @param $doc
     * @return mixed
     */
    function formatRedmineHeighthl($doc, $containerId = null, $containerType = null)
    {
        // 解释代码块
        // $matchs = [];
        // preg_match_all("/``` [\s\S]*```/", $doc, $matchs);
        // $matchs = $matchs[0] ?? $matchs;
        // foreach ($matchs as $match) {
        //     $strArr = explode(PHP_EOL, $match);
        //     $lang = str_replace('``` ', '', $strArr[0]);
        //     array_shift($strArr);
        //     array_pop($strArr);
        //     $str = implode(PHP_EOL, $strArr);
        //     $height = make(\GeSHi::class, [$str, $lang]);
        //     $newStr = $height->parse_code();
        //     $doc = str_replace($match, $newStr, $doc);
        // }

        // 解释图片
        $doc = handleMarkdownImg($doc, $containerId = null, $containerType = null);
        return $doc;
    }
}

if (!function_exists(('handleMarkdownImg'))) {
    function handleMarkdownImg($doc, $containerId = null, $containerType = null)
    {
        $oPattern = '/!\[\]\((.*?)\)/';
        $imgs = [];
        preg_match_all($oPattern, $doc, $imgs);
        if (!empty($imgs[1]) && is_array($imgs[1])) {
            foreach ($imgs[1] as $img) {
                $imgStr = urldecode($img);
                if (strpos($imgStr, 'http') === false) {
                    $url = \App\Model\Redmine\AttachmentModel::query()->where('filename', $imgStr)->where(function ($query) use($containerId, $containerType){
                        if ($containerId) {
                            $query->where('container_id', $containerId);
                        }
                        if ($containerType) {
                            $query->where('container_type', $containerType);
                        }
                    })->first();
                    $newUrl = $url->url ?? $img;
                    $doc = str_replace($imgStr, $newUrl, $doc);
                }
            }
        }
        return $doc;
    }
}

if(!function_exists('htmlImgPos')) {
    /**
     * 代替redmine内容中的img为前端可显示
     * @param String $text 内容
     * @param $containerId issues.id | journals.journalized_id
     * @param $containerType
     * @return array|mixed|string|string[]
     */
    function htmlImgPos(String $text, $containerId = null, $containerType = null) {
        $pattern = '/<img src="(.*?)"/';
        preg_match_all($pattern, $text, $images);
        if (!empty($images[1]) && is_array($images[1])) {
            foreach ($images[1] as $image) {
                if (strpos($image, 'http') !== 0 && $containerId) {
                    $decodeImg      = urldecode($image);
                    $attUrl         = \App\Model\Redmine\AttachmentModel::query()->where('filename', $decodeImg)
                        ->where(function ($query) use($containerId, $containerType){
                            if ($containerId) {
                                $query->where('container_id', $containerId);
                            }
                            if ($containerType) {
                                $query->where('container_type', $containerType);
                            }
                        })->first();
                    $redmineFileUrl = $attUrl->url ?? $image;
                    $text    = str_replace($image, $redmineFileUrl, $text);
                }
            }
        }
        return $text;
    }
}

if (!function_exists('reRedmineContentImg')){
    /**
     * 编辑内容数据后恢复redmine原来的图片内容
     * @param String $oText 原来的内容
     * @param String $nText 新的内容
     * @param String $contentType 新内容格式，默认为HTML,可选markdown
     * @return array|mixed|string|string[]
     */
    function reRedmineContentImg($oText, String $nText, $contentType = 'HTML')
    {
        // 1. 查找新内容的所有图片默认为HTML格式
        $nPattern = $contentType == 'MARKDOWN' ? '/!\[\]\((.*?)\)/' : '/<img src="(.*?)"/';
        $nMatches = [];
        preg_match_all($nPattern, $nText, $nMatches);
        if (!empty($nMatches[1]) && is_array($nMatches[1])){
            // 1.查找旧内容的所有图片(旧内容默认为markdown格式，用![]()查找)
            $oPattern = '/!\[\]\((.*?)\)/';
            $oMatches = [];
            preg_match_all($oPattern, $oText, $oMatches);

            if (!empty($oMatches[1]) && is_array($oMatches[1])) {
                foreach ($oMatches[1] as $match) {
                    $de = urldecode($match);
                    foreach ($nMatches[1] as $nMatch) {
                        // 匹配完成，恢复原来的图片内容
                        if (strpos($nMatch, $de) !== false) {
                            $nText = str_replace($nMatch, $match, $nText);
                        } else {
                            $attachment = \App\Model\Redmine\AttachmentModel::query()->where('filename', $de)->first();
                            if (!empty($attachment->disk_filename)) {
                                if (strpos($nMatch, $attachment->disk_filename) !== false) {
                                    $nText = str_replace($nMatch, $match, $nText);
                                }
                            }
                        }
                    }
                }
            }
        }
        return $nText;
    }
}

if(!function_exists('pregRemoveContent')){
    /**
     * 正则查找指定内容并除去
     * @param string $text 文本内容
     * @param string $preg 需要匹配的正则表达式
     * @return void
     */
    function pregRemoveContent(string $text, string $preg): string
    {
        preg_match_all($preg, $text, $nTexts);
        $nTexts = $nTexts[0] ?? [];
        foreach ($nTexts as $nText) {
            $text = str_replace($nText, '', $text);
        }
        $text = str_replace('<p></p>', '', $text);
        return $text;
    }
}


if(!function_exists('getShanghaiZone')){
    /**
     * 转换为上海时区
     * @param $datetime
     * @param string $format
     * @return string
     * @throws Exception
     */
    function getShanghaiZone($datetime, string $format = 'Y-m-d H:i'): string
    {
        $dt = new \DateTime($datetime,new \DateTimeZone('UTC'));
        $dt->setTimezone(new \DateTimeZone('Asia/Shanghai'));
        return $dt->format($format);
    }
}

/**
 * 获取前端地址
 */
if (!function_exists('biFrontendHost')) {
    function biFrontendHost()
    {
        $url = env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101');
        if (strpos($url, '#') === false) {
            $url = $url . DIRECTORY_SEPARATOR . '#';
        }
        return $url;
    }
}

/**
 * 获取后端地址
 */
if (!function_exists('biBackendHost')) {
    function biBackendHost()
    {
        return env('BI_BACKEND_HOST', 'http://bi.t-firefly.com:8057');
    }
}

if (!function_exists('makePassword')) {
    /**
     * 构建加密密码
     * @param $origin
     * @param $salt
     * @return string
     */
    function makePassword($origin, $salt) : string
    {
        return md5($origin . $salt);
    }
}

if(!function_exists('messageTempletReplace')) {
    /**
     * 消息模块内容替换
     * @param array $search
     * @param string $content
     * @return array|string|string[]
     */
    function messageTempletReplace(array $search, string $content)
    {
        foreach ($search as $key => $value) {
            $keywords = '{$'.$key.'}';
            $content = str_replace($keywords, $value, $content);
        }
        return $content;
    }
}

if (!function_exists('getBbsHost')) {
    function bbsHost()
    {
        return env('TCHIP_BI_BBS_URL', 'http://127.0.0.1:8065');
    }
}

if (!function_exists('hasHtmlTags')) {
    function hasHtmlTags($str) {
        // 匹配html标签，同时<?php不会被误判
        $a = preg_match("/<(?!.*\?)[^<]+>/", $str);
        return $a > 0 ? true : false;
    }
}

if (!function_exists('isMarkdown')) {
    function isMarkdown($str) {
        // Markdown语法规则的正则表达式
        $pattern = '/^(?:\#{1,6} .+)|(?:\*{1,3} .+)|(?:\d+\. .+)|(?:\>\s.+)|\[.*?\]\(.*?\)|```.*?```/s';
        // 使用preg_match函数进行匹配
        return preg_match($pattern, $str) === 1;
    }
}

if (!function_exists('removeMarkdownSyntax')) {
    function removeMarkdownSyntax($text) {
        $patterns = array(
            '/# /',
            '/## /',
            '/### /',
            '/#### /',
            '/##### /',
            '/\*\*(.*?)\*\*/', // **加粗**
            '/&#126;&#126;(.*?)&#126;&#126;/',      // &#126;&#126;删除线&#126;&#126;
            '/\[(.*?)\]\((.*?)\)/'   // [链接](URL)
        );

        return preg_replace($patterns, '$1$2', $text);
    }
}

if (!function_exists('matchAtUserName')) {
    function matchAtUserName($string)
    {
        $pattern = '/@(\w+)/u';
        preg_match_all($pattern, $string, $matches);
        $names = !empty($matches[1]) ? $matches[1] : [];
        return $names;
    }
}

if (!function_exists('sendEmail')) {
    function sendEmail($title, $content, $email, $nickName = '', $throwError = false)
    {
        $mail = make(\PHPMailer\PHPMailer\PHPMailer::class); //PHPMailer对象
        $mail->CharSet = 'UTF-8'; //设定邮件编码，默认ISO-8859-1，如果发中文此项必须设置，否则乱码
        $mail->isSMTP(); // 设定使用SMTP服务
        $mail->SMTPDebug = 0; // 关闭SMTP调试功能
        $mail->SMTPAuth = true; // 启用 SMTP 验证功能
        $mail->SMTPSecure = 'ssl'; // 使用安全协议
        $mail->Host = env('MAIL_SMTP_HOST', 'smtp.exmail.qq.com'); // SMTP 服务器
        $mail->Port = env('MAIL_SMTP_PORT', '465'); // SMTP服务器的端口号
        $mail->Username = env('MAIL_SMTP_USERNAME', '<EMAIL>'); // SMTP服务器用户名
        $mail->Password = env('MAIL_SMTP_PASSWORD', 'hUtBevE7m8HH6cQM'); // SMTP服务器密码
        $mail->setFrom(env('MAIL_FROM_ADDRESS', '<EMAIL>'), $nickName ? $nickName : env('MAIL_FROM_NAME', '数字天启系统通知')); // 邮箱，昵称
        $mail->Subject = $title;
        $mail->msgHTML($content);
        $mail->addAddress($email); // 收件人
        //防止附件过大，发送失败，先不添加附件
        // 添加附件
        // if (!empty($attachments)) {
        //     $tempFiles = [];
        //     foreach ($attachments as $attachment) {
        //         $fileName = $attachment['name']??$attachment['filename']??'附件';
        //         if(!empty($attachment['path'])){
        //             $mail->addAttachment($attachment['path'], $fileName);
        //             $tempFiles[] = $attachment['path'];
        //         }else{
        //             if(!empty($attachment['url'])){
        //                 $tempDir = sys_get_temp_dir();
        //                 $tempFile = tempnam($tempDir, 'email_attachment');
        //                 if (file_put_contents($tempFile, file_get_contents($attachment['url']))) {
        //                     $mail->addAttachment($tempFile, $fileName);
        //                     $tempFiles[] = $tempFile;
        //                 } else {
        //                     \App\Core\Utils\Log::get('app')->warning("附件下载失败或超过大小限制: {$attachment['url']}");
        //                 }
        //             }
        //         }
        //     }
        // }
        
        $result = $mail->send();

        // 清理临时文件
        // if (!empty($tempFiles)) {
        //     foreach ($tempFiles as $file) {
        //         if (file_exists($file)) {
        //             @unlink($file);
        //         }
        //     }
        // }
        if ($result) {
            \App\Core\Utils\Log::get('system')->info("发送邮件到 {$email} 成功. 标题:{$title}");
            return true;
        } else {
            \App\Core\Utils\Log::get('system')->info("发送邮件到 {$email} 失败, {$mail->ErrorInfo}. 标题:{$title}");
            if($throwError){
                throw new AppException(StatusCode::ERR_SERVER, $mail->ErrorInfo);
            }
            return false;
        }
    }
}

if (!function_exists('imgToBase64')) {
    function imgToBase64($url)
    {
        list($width, $height, $type) = getimagesize($url);
        switch ($type) {
            case 3:
                $fun = 'imagecreatefrompng';
                break;
            default:
                $fun = 'imagecreatefromjpeg';
        }
        // if ($height > $width) {
        //
        // } else if ($height < $width) {
        //
        // } else {
        //     $newWidth = $width > 200 ? 200 : $width;
        //     $newHeight = $height > 200 ? 200 : $height;
        // }
        $newWidth = $width > 200 ? 200 : $width;
        $newHeight = $height > 200 ? 200 : $height;
        $imageWp = imagecreatetruecolor($newWidth, $newHeight);

        // 设置为白背景
        $white = imagecolorallocate($imageWp, 255, 255, 255);
        imagefill($imageWp, 0,0, $white);

        $image  = $fun($url);
        $tempName = \App\Core\Utils\Random::alnum(8);
        imagecopyresampled($imageWp, $image, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);

        $imgTp = BASE_PATH.'/runtime/'.$tempName.'.jpeg';
        imagejpeg($imageWp, $imgTp);
        imagedestroy($imageWp);
        imagedestroy($image);
        $baseUrl = base64_encode(file_get_contents($imgTp));
        @unlink($imgTp);
        return 'data:image/jpg/png/gif;base64,'.$baseUrl;
        // return '<img decoding="async" src="data:image/jpg/png/gif;base64,'.$baseUrl . '" />';
    }
}

if (!function_exists('compressImage')) {
    /**
     * 压缩图片
     * @var Hyperf\HttpMessage\Upload\UploadedFile $file
     * @param $perNum 需要压缩的最大一边的分辨率
     * @return void
     */
    function compressImage($file, $perNum = 1500)
    {
        // 假设已经通过表单上传了文件到 $_FILES['image']
        $fileInfo = $file->toArray();
        $sourcePath = $fileInfo['tmp_file'];
        // 获取图片原始尺寸
        list($width, $height, $type) = getimagesize($fileInfo['tmp_file']);
        if ($height > $perNum || $width > $perNum) {

            // 设置需要设置的图片大小
            if ($height > $width && $height > $perNum) {
                $targetHeight = $perNum;
                $targetWidth = (int) round(($width / $height) * $targetHeight);
            } else if ($width > $height && $width > $perNum) {
                $targetWidth = $perNum;
                $targetHeight = (int) round(($height / $width) * $targetWidth);
            } else {
                $targetWidth = $targetHeight = $perNum;
            }
            switch ($type) {
                case 3:
                    $fun = 'imagecreatefrompng';
                    break;
                default:
                    $fun = 'imagecreatefromjpeg';
            }
            // // 创建画布
            // $imageWp = imagecreatetruecolor($targetWidth, $targetHeight);
            // // 设置为白背景
            // $white = imagecolorallocate($imageWp, 255, 255, 255);
            // imagefill($imageWp, 0,0, $white);
            // 创建新的图片资源
            $sourceImage = $fun($sourcePath);
            $targetImage = imagecreatetruecolor($targetWidth, $targetHeight);

            // 调整图片大小
            imagecopyresampled(
                $targetImage,
                $sourceImage,
                0, 0, 0, 0,
                $targetWidth,
                $targetHeight,
                $width,
                $height
            );

            $tempName = \App\Core\Utils\Random::alnum(8);
            $imgTp = BASE_PATH.'/runtime/'.$tempName.'.jpeg';
            // // 保存压缩后的图片
            imagejpeg($targetImage, $imgTp, 90); // 90 是图片质量参数
            // // 释放内存
            imagedestroy($sourceImage);
            imagedestroy($targetImage);
            return saveFile($fileInfo['name'], $imgTp, $fileInfo['size']);
        } else {
            $uploadFile = make(\App\Core\Services\Setting\AttachmentService::class)->upload($file);
            return $uploadFile;
        }
    }
}

if (!function_exists('saveFile')) {
    /**
     * 保存文件
     * @param $filename
     * @param $filePath
     * @return void
     */
    function saveFile($filename, $filePath, $size = 0)
    {
        list($width, $height, $type, $style, ) = getimagesize($filePath);
        try {
            $uid = make(Qbhy\HyperfAuth\AuthManager::class)->user()->getId();
        } catch (AuthException $e) {
            $uid = 0;
        }

        $imgType = [
            1 => 'gif',
            2 => 'jpg',
            3 => 'png',
            4 => 'swf',
            5 => 'psd',
            6 => 'bmp',
            7 => 'tiff',
            8 => 'tiff',
            9 => 'jpc',
            10 => 'jp2',
            11 => 'jpx',
            12 => 'jb2',
            13 => 'swc',
            14 => 'iff',
            15 => 'wbmp',
            16 => 'xbm',
        ];

        $sha1 = hash_file('sha1', $filePath);

        $savekey = setFileSavePath($filePath);
        $savekey = '/' . ltrim($savekey, '/');
        // $uploadDir = substr($savekey, 0, strripos($savekey, '/') + 1);
        // 转存文件到上传目录
        @copy($filePath, BASE_PATH . '/public' . $savekey);
        @unlink($filePath);

        $params = [
            'user_id'     => $uid,
            'filename'    => substr(htmlspecialchars(strip_tags($filename)), 0, 100),
            'filesize'    => $size,
            'filetype'    => 'image',
            'imagewidth'  => $width,
            'imageheight' => $height,
            'imagetype'   => $imgType[$type] ?? '',
            'imageframes' => 0,
            'mimetype'    => !empty($imgType[$type]) ? 'image/' . $imgType[$type] : '',
            'url'         => $savekey,
            'storage'     => 'local',
            'sha1'        => $sha1,
            'extparam'    => '',
        ];
        $first = [
            'filename' => $params['filename'],
            'filesize' => $params['filesize'],
            'filetype' => $params['filetype'],
            'url' => $params['url'],
            'sha1' => $params['sha1'],
        ];
        $attachment = AttachmentModel::query()->firstOrCreate($first, $params);
        return $attachment;
    }
}


if (!function_exists('setFileSavePath')) {
    /**
     * 生成保存文件的目录
     * @param $filename
     * @return array|string|string[]
     */
    function setFileSavePath($filename = null)
    {
        if ($filename) {
            $suffix = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
            $suffix = $suffix && preg_match("/^[a-zA-Z0-9]+$/", $suffix) ? $suffix : 'file';
        }

        $md5 = md5_file($filename);
        $replaceArr = [
            '{year}'     => date("Y"),
            '{mon}'      => date("m"),
            '{day}'      => date("d"),
            '{hour}'     => date("H"),
            '{min}'      => date("i"),
            '{sec}'      => date("s"),
            '{random}'   => Random::alnum(16),
            '{random32}' => Random::alnum(32),
            '{filename}' => substr($filename, 0, 100),
            '{suffix}'   => $suffix,
            '{.suffix}'  => $suffix ? '.' . $suffix : '',
            '{filemd5}'  => $md5,
        ];
        $savekey = str_replace(array_keys($replaceArr), array_values($replaceArr), '/uploads/{year}{mon}{day}/{filemd5}{.suffix}');
        $uploadDir = BASE_PATH . '/public' . substr($savekey, 0, strripos($savekey, '/') + 1);
        is_dir($uploadDir) || mkdir($uploadDir, 0755, true);
        return $savekey;
    }
}

/**
 * 获取存储路径
 * @param string $path 相对路径
 * @return string 完整的存储路径
 */
if (!function_exists('storage_path')) {
    function storage_path(string $path = ''): string
    {
        return BASE_PATH . '/storage' . ($path ? '/' . ltrim($path, '/') : '');
    }
}

if (!function_exists('convertToChinese')) {
    function convertToChinese($number)
    {
        $number = round($number, 2); // 保留两位小数
        $units = ["元", "角", "分"];
        $nums = ["零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"];
        $parts = explode('.', (string) $number);
        $integerPart = $parts[0];
        $decimalPart = isset($parts[1]) ? $parts[1] : '';

        $integerStr = '';
        $len = strlen($integerPart);
        $unitPos = 0;
        $needZero = false;

        for ($i = 0; $i < $len; $i++) {
            $num = $integerPart[$i];
            $pos = $len - $i - 1;
            $unit = getUnit($pos);
            if ($num == '0') {
                $needZero = true;
            } else {
                if ($needZero) {
                    $integerStr .= $nums[0];
                    $needZero = false;
                }
                $integerStr .= $nums[$num] . $unit;
            }
        }

        if ($integerStr == '') {
            $integerStr = $nums[0] . $units[0];
        } else {
            $integerStr .= $units[0];
        }

        $decimalStr = '';
        $decimalLen = strlen($decimalPart);

        for ($i = 0; $i < $decimalLen; $i++) {
            $num = $decimalPart[$i];
            if ($num != '0') {
                $decimalStr .= $nums[$num] . $units[$i + 1];
            }
        }

        if ($decimalStr == '') {
            $decimalStr = '整';
        }

        return '人民币'.$integerStr . $decimalStr;
    }

    function getUnit($pos)
    {
        $units = ["", "拾", "佰", "仟", "万", "拾", "佰", "仟", "亿", "拾", "佰", "仟", "兆"];
        return $units[$pos] ?? '';
    }

}

//索引数组转id-name关联数组
if (!function_exists('associativelyIndex')) {
    function associativelyIndex($array, $key1 = 'id', $key2 = 'name'){
        if(empty($array)) return $array;
        $data = [];
        foreach($array as $key => $value){
            $data[$key][$key1] = $key;
            $data[$key][$key2] = $value;
        }
        return array_values($data);
    }
}

//获取与当前日期相差多少天
if(!function_exists('calculateDaysBetween')){
    function calculateDaysBetween(string $datetime1, string $datetime2): int
    {
        // 创建 DateTime 对象，并将时间部分设置为午夜（00:00:00）
        $dateOnly1 = new DateTime($datetime1);
        $dateOnly2 = new DateTime($datetime2);

        // 设置时间部分为午夜（00:00:00），只计算日期
        $dateOnly1->setTime(0, 0, 0);
        $dateOnly2->setTime(0, 0, 0);

        // 计算日期差异
        $interval = $dateOnly1->diff($dateOnly2);
        // 获取天数差
        $daysDifference = $interval->days;

        // 如果第一个日期晚于第二个日期，返回负值
        if ($dateOnly1 > $dateOnly2) {
            return -$daysDifference;
        }

        return $daysDifference;
    }
}

//计算百分比
if(!function_exists('calculateRate')){
    function calculateRate($numerator, $denominator, $decimals = 1, $includePercent = true, $is4She5Ru = false)
    {
        // 确保分母不为0
        if ($denominator == 0 || $denominator == 0) {
            return $includePercent?'0%':'0';
        }
        $numerator = (string)$numerator;
        $denominator = (string)$denominator;
        // 使用bcmath进行高精度计算
        $percentage = bcdiv($numerator, $denominator, 10) ?? 0; // 精确到小数点后10位
        // 乘以100并保留指定小数位数
        $percentage = bcmul($percentage, '100', $decimals + ($is4She5Ru ? 1 : 0)); // 或保留额外1位小数

        if ($is4She5Ru) {
            // 四舍五入处理
            $roundingFactor = '0.' . str_repeat('0', $decimals) . '5'; // 生成用于四舍五入的因子
            $percentage = bcadd($percentage, $roundingFactor, $decimals + 1); // 加上因子
            $percentage = bcdiv($percentage, '1', $decimals); // 保留指定小数位
        }

        // 如果需要附加百分号
        if ($includePercent) {
            return $percentage . '%';
        }
        return $percentage;
    }
}


/**
 * DB查询的对象数据转为数组
 */
if(!function_exists('dbToArray')) {
    function dbToArray($dbData)
    {
        // DB查询的结果如果使用->toArray，子数组还是claClass的结构 故使用json_decode方法
        return is_object($dbData) ? json_decode(json_encode($dbData), true) : (is_array($dbData) ? $dbData : null);
    }
}

if (!function_exists('compressUploadImage')) {
    function base64Image($image) {


        $compressedImage = file_get_contents($image);

        // 转换为 Base64
        $base64Image = base64_encode($compressedImage);

        // 返回结果
        return $base64Image;
    }
}

/**
 * 收集列表里多个属性组成的数据，比如需要整合所有数据中的各种用户id
 */
if (!function_exists('collectFiledArrFromData')) {
    function collectFiledArrFromData($data,$fields = array()) {
        $arr = [];
        foreach ($fields as $field){
            foreach ($data as $item){
                //如果是数组集合，则合并
                if(isset($item[$field]) && is_array($item[$field])){
                    $arr = array_merge($arr,$item[$field]);
                }else{
                    $arr[] = $item[$field]??0;
                }
            }
        }
        return array_unique(array_filter($arr));
    }
}
/**
 * 实现array_column，当$arr中的值为对象时，array_column失效
 * @param $arr
 * @param $val_key
 * @param null $new_key
 * @return array
 */
if (!function_exists('array_object_column')) {
    function array_object_column($arr, $val_key, $new_key = null)
    {
        $new_arr = [];
        foreach ($arr as $item) {
            if (!is_null($new_key)) {
                $new_arr[$item[$new_key]] = $item[$val_key];
            } else {
                $new_arr[] = $item[$val_key];
            }
        }
        return $new_arr;
    }
}
/**
 * 判断是否是请求
 */
if (!function_exists('is_request')) {
    function is_request(): bool
    {
        $request = Context::get(ServerRequestInterface::class);

        return (bool)$request;
    }
}

/**
 * 判断是否是请求
 */
if (!function_exists('get_login_user_id')) {
    function get_login_user_id()
    {
        try {
            $uid = is_request() ? make(AuthManager::class)->user()->getId() : make(UserService::class)->getUserIdByUserName(CrontabCode::DEFAULT_USER_NAME);
        } catch (Throwable $e) {
            $uid = 0;
        }
        return $uid ?: 0;
    }
}
/**
 * 二维数组多个字段排序
 */
if (!function_exists('sort_array_by_fields')) {
    function sort_array_by_fields(array &$array, array $order): void
    {
        foreach ($order as &$value) {
            $value = strtolower($value);
        }
        usort($array, function ($a, $b) use ($order) {
            foreach ($order as $key => $direction) {
                if ($a[$key] == $b[$key]) {
                    continue;
                }
                return ($direction === 'asc' ? 1 : -1) * ($a[$key] <=> $b[$key]);
            }
            return 0;
        });
    }
}

/**
 * 二维数组按字段添加序号
 */
if (!function_exists('add_index_by_field')) {
    function add_index_by_field(array &$array, string $filed, string $indexName = 'index'): void
    {
        $index = 1;

        foreach ($array as &$item) {
            // 如果当前的 change_log_id 和上一项不同，index 才递增
            if (!isset($lastChangeLogId) || $item[$filed] !== $lastChangeLogId) {
                $lastChangeLogId = $item[$filed];
                $item[$indexName] = $index++;
            } else {
                // 如果当前的 change_log_id 与上一项相同，index 保持不变
                $item[$indexName] = $lastIndex;
            }
            $lastIndex = $item[$indexName]; // 更新最新的 index
        }
    }
}

/**
 * 去除数组中的空值和重复值
 */
if (!function_exists('unique_filter')) {
    function unique_filter(array $array): array
    {
        return array_unique(array_filter($array));
    }
}

/**
 * 去除数组中的空值和重复值
 */
if (!function_exists('unique_filter_column')) {
    function unique_filter_column(array $array,string $field): array
    {
        return array_unique(array_filter(array_column($array, $field)));
    }
}

if(!function_exists('split_text_and_number')){
    function split_text_and_number($input) {
        if (preg_match('/(.*?)(\d+)$/', $input, $matches)) {
            return [$matches[1], $matches[2], (int)$matches[2]];
        }
        return [$input, '0', 0]; // 如果没有匹配数字，则返回原字符串
    }
}

// 将数组中的字符串转换为大写
if(!function_exists('upper_arr')){
    function upper_arr($arr) {
        foreach ($arr as $key => $value){
            $arr[$key] = strtoupper($value);
        }
        return $arr;
    }
}
//数组匹配选项,option为key=>value的数组
if(!function_exists('array_match_option')){
    function array_match_option($idArr,$option,$returnArr = false)
    {
        $textArr = [];
        foreach ($idArr as $item){
            if(!empty($option[$item])){
                $textArr[] = $option[$item];
            }
        }
        return $returnArr?$textArr:implode(',',array_unique($textArr));
    }
}

//一维数组比较
if(!function_exists('isSameArray')){
    function isSameArray(array $a, array $b): bool {
        sort($a); sort($b); return $a === $b;
    }
}


if (!function_exists('redmineUserIdsToBiUserids')) {
    /**
     * redmine userid 转 bi userid，返回键值映射（redmine_user_id => bi_user_id）
     * @param array $redmineUserIds
     * @return array
     */
    function redmineUserIdsToBiUserids(array $redmineUserIds): array
    {
        $AllIdInfo = make(UserThirdModel::class)::query()
            ->whereIn('third_user_id', $redmineUserIds)
            ->where('platform', 'redmine')
            ->get()
            ->toArray();

        // 构造映射数组
        $map = [];
        foreach ($AllIdInfo as $item) {
            $map[$item['third_user_id']] = $item['user_id'];
        }

        return $map;
    }
}
if (!function_exists('buildOnlineFileUrl')) {
    /**
     * 构建线上环境URL，将本地URL替换为线上URL
     * @param string $url
     * @return string
     */
    function buildOnlineFileUrl(string $url): string
    {
        //如果不是线上环境，则返回原来url
        $env = env('APP_ENV', 'dev');
        if($env !== 'prod'){
            return $url;
        }
        //如果是http开头且不是http://online开头，则把/files/前面的内容替换为http://online.bi.t-firefly.com:2101
        if(strpos($url, 'http') === 0 && strpos($url, 'http://online') !== 0){
            // 查找/files/的位置
            $filesPos = strpos($url, '/files/');
            if($filesPos !== false){
                // 替换/files/前面的内容为线上域名
                $url = 'http://online.bi.t-firefly.com:2101' . substr($url, $filesPos);
            }
        }
        return $url;
    }
}

/**
 * 上传文件到OSS快捷方法
 * @param string $loadPath 本地上传后的路径 '/uploads/20250722/xxx.jpg'
 * @return string cdn url 'http://cdn.t-firefly.com/bi/uploads/20250722/image-15113869535.png'
 */
if (!function_exists('uploadFileOss')) {
    function uploadFileOss($loadPath)
    {
        // 上传到OSS的路径
        $uploadPath = str_replace('/', DIRECTORY_SEPARATOR, str_replace('//', DIRECTORY_SEPARATOR, env('OSS_BI_PATH', 'bi/') . $loadPath));
        // 地绝对路径
        $fileDir = BASE_PATH . '/public' . $loadPath;
        $result  = \App\Core\Utils\Uploads\UploadFactory::create('Aliyun')->upload($fileDir, $uploadPath);

        $resultUrl = '';
        if (!empty($result['oss-request-url'])) {
            $resultUrl = str_replace(env('OSS_DOMAIN'), env('OSS_CDN'), $result['oss-request-url']);
        }
        return $resultUrl;
        // oss-request-url: "http://t-firefly-cdn.oss-cn-hangzhou.aliyuncs.com/bi/uploads/20250722/image-14094022817.png"
        // return [$uploadPath, $fileDir, file_exists($fileDir)];
    }
}
