<?php

namespace App\Constants;

class OaQcSpecialOrderCode
{
    const QcRole = 'Qc';

    // 特采订单状态
    const STATUS_PENDING = 'pending';     // 待审批
    const STATUS_STEP1 = 'step1';         // 采购审批中
    const STATUS_STEP2 = 'step2';         // 品质经理审批中
    const STATUS_STEP3 = 'step3';         // PMC审批中
    const STATUS_STEP4 = 'step4';         // 副总经理审批中
    const STATUS_APPROVED = 'approved';   // 已通过
    const STATUS_REJECTED = 'rejected';   // 已驳回
    const STATUS_CANCELLED = 'cancelled'; // 已取消
    const STATUS_RETURNED = 'returned';   // 已退回申请人

    // 审批步骤
    const STEP_PENDING = 0;    // 待提交
    const STEP_PURCHASE = 1;   // 采购
    const STEP_QUALITY = 2;    // 品质经理
    const STEP_PMC = 3;        // PMC
    const STEP_MANAGER = 4;    // 副总经理
    const STEP_COMPLETED = 5;  // 已完成
    const STEP_RETURNED = 99;  // 已退回申请人

    // 审批动作
    const ACTION_APPROVE = 'approve';  // 同意
    const ACTION_REJECT = 'reject';    // 驳回
    const ACTION_RETURNED = 'returned'; // 退回申请人

    /**
     * 获取状态文本映射
     */
    public static function getStatusTextMap(): array
    {
        return [
            self::STATUS_PENDING => '申请中',
            self::STATUS_STEP1 => '申请中',
            self::STATUS_STEP2 => '申请中',
            self::STATUS_STEP3 => '申请中',
            self::STATUS_STEP4 => '申请中',
            self::STATUS_APPROVED => '已通过',
            self::STATUS_REJECTED => '已驳回',
            self::STATUS_CANCELLED => '已取消',
            self::STATUS_RETURNED => '已退回',
        ];
    }

    /**
     * 获取步骤文本映射
     */
    public static function getStepTextMap(): array
    {
        return [
            self::STEP_PENDING => '待提交',
            self::STEP_PURCHASE => '采购审批',
            self::STEP_QUALITY => '品质经理审批',
            self::STEP_PMC => 'PMC审批',
            self::STEP_MANAGER => '副总经理审批',
            self::STEP_COMPLETED => '审批完成',
            self::STEP_RETURNED => '已退回申请人',
        ];
    }

    /**
     * 获取状态文本
     */
    public static function getStatusText(string $status): string
    {
        $statusMap = self::getStatusTextMap();
        return $statusMap[$status] ?? '未知状态';
    }

    /**
     * 获取步骤文本
     */
    public static function getStepText(int $step): string
    {
        $stepMap = self::getStepTextMap();
        return $stepMap[$step] ?? '未知步骤';
    }

    /**
     * 获取步骤对应的状态
     */
    public static function getStatusByStep(int $step): string
    {
        $stepStatusMap = [
            self::STEP_PENDING => self::STATUS_PENDING,
            self::STEP_PURCHASE => self::STATUS_STEP1,
            self::STEP_QUALITY => self::STATUS_STEP2,
            self::STEP_PMC => self::STATUS_STEP3,
            self::STEP_MANAGER => self::STATUS_STEP4,
            self::STEP_COMPLETED => self::STATUS_APPROVED,
            self::STEP_RETURNED => self::STATUS_RETURNED,
        ];

        return $stepStatusMap[$step] ?? self::STATUS_PENDING;
    }

    /**
     * 获取下一个审批人ID
     */
    public static function getNextApproverId(int $step): ?int
    {
        $approverMap = [
            self::STEP_PURCHASE => 268,  // 采购部门审批人(王婷)
            self::STEP_QUALITY => 294,   // 品质经理(胡宏志)
            self::STEP_PMC => 177,       // PMC(黄丝丝)
            self::STEP_MANAGER => 111,   // 副总经理(文晓东)
        ];

        return $approverMap[$step] ?? null;
    }


}