<?php
declare(strict_types=1);

namespace App\Constants;

class TestPlanCode
{
    // 测试计划状态
    const STATUS_NOT_START = 1; // 未开始
    const STATUS_ON_GOING = 2; // 进行中
    const STATUS_FINISHED = 3; // 已完成

    const STATUS_MAP = [
        self::STATUS_NOT_START => '未开始',
        self::STATUS_ON_GOING  => '进行中',
        self::STATUS_FINISHED  => '已完成'
    ];

    //测试用例状态
    const CASE_STATUS_NOT_START = 1; // 未测
    const CASE_STATUS_IGNORE = 2; // 忽略
    const CASE_STATUS_RESTART = 3; // 重测
    const CASE_STATUS_STOP = 4; // 受阻
    const CASE_STATUS_FAIL = 5; // 失败
    const CASE_STATUS_PASS = 6; // 通过

    const CASE_STATUS_MAP = [
        self::CASE_STATUS_PASS      => '通过',
        self::CASE_STATUS_FAIL      => '失败',
        self::CASE_STATUS_STOP      => '受阻',
        self::CASE_STATUS_RESTART   => '重测',
        self::CASE_STATUS_IGNORE    => '忽略',
        self::CASE_STATUS_NOT_START => '未测',
    ];

    // 测试用例 变更日志 变更类型
    const CASE_CHANGE_STEP = 'test_case_step'; // 步骤 (表名称）
    const CASE_CHANGE_CASE = 'test_case'; // 用例

    const CASE_FIELD_TEXT = [
        'library_id' => '版本',
        'title' => '标题',
        'step_id' => '步骤',
        'step_order' => '步骤',
        'expected_result' => '步骤',
        'step' => '步骤',
        'estimated_hours' => '评估工时',
        'precondition' =>'前置条件',
        'tmpPrecondition' =>'前置条件',
        'category_id' => '模块',
        'priority' => '优先级',
        'directory_id' => '目录',
        'project_id' => '项目',
        'relation_issue_id' =>'关联事项',
        'attachment_id' => '附件',
        'description' => '文本描述',
        'desc_type' => '描述类型',
    ];
}