<?php

declare(strict_types=1);

namespace App\Constants;

/**
 * 系统账号配置常量
 */
class SystemAccountConfig
{
    /**
     * 当前系统（Tchip BI）配置
     */
    const TCHIP_BI = [
        'system_name' => 'Tchip BI',
        'system_description' => '数字天启管理系统',
        'login_url' => 'http://bi.t-firefly.com:2101',
        'notice_method' => 'accountCreation',
        'default_role' => '基础用户'
    ];

    /**
     * 售后系统配置
     */
    const FIREFLY_SERVICE = [
        'system_name' => 'Firefly Service',
        'system_description' => '售后管理系统',
        'login_url' => 'http://service.t-firefly.com',
        'notice_method' => 'accountCreation',
        'default_role' => '售后组',
        'role_id' => 6
    ];

    /**
     * 销售系统配置
     */
    const TCHIP_SALE = [
        'system_name' => 'Tchip Sale',
        'system_description' => '销售管理系统',
        'login_url' => 'http://sale.t-firefly.com:2102/',
        'notice_method' => 'salesAccountCreation',
        'default_role' => '销售员',
        'role_id' => 4, // 对应dwin_user_main_table中的group_id
        'company_id' => 1001, // 公司ID
        'part_id' => 47 // 部门ID
    ];

    /**
     * 根据系统类型获取配置
     * @param string $systemType
     * @return array
     */
    public static function getConfig(string $systemType): array
    {
        $configs = [
            'firefly_service' => self::FIREFLY_SERVICE,
            'tchip_sale' => self::TCHIP_SALE,
            'tchip_bi' => self::TCHIP_BI
        ];

        return $configs[$systemType] ?? [];
    }

    /**
     * 获取所有系统配置
     * @return array
     */
    public static function getAllConfigs(): array
    {
        return [
            'firefly_service' => self::FIREFLY_SERVICE,
            'tchip_sale' => self::TCHIP_SALE,
            'tchip_bi' => self::TCHIP_BI
        ];
    }

    /**
     * 根据部门ID获取应该创建的系统账号
     * @param array $departments
     * @return array
     */
    public static function getSystemsByDepartments(array $departments): array
    {
        $systems = [];

        // 平台销售中心(ID=2) - 创建销售系统和售后系统账号
        if (in_array(2, $departments)) {
            $systems[] = 'tchip_sale';
            $systems[] = 'firefly_service';
        }

        // 工程组(ID=13) - 创建售后系统账号
        if (in_array(13, $departments)) {
            $systems[] = 'firefly_service';
        }

        // 临时测试：部门31 测试用
        if (in_array(31, $departments)) {
            $systems[] = 'firefly_service';
            $systems[] = 'tchip_sale';
        }

        return array_unique($systems);
    }
}
