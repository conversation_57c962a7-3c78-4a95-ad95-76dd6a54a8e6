<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */

namespace App\Constants;

class AssembleOrderCode
{
    const ORDER_KEYWORDS = 'assemble_order';
    // 核准状态
    const APPROVE_STATUS_WAIT = 1;
    public const APPROVE_STATUS = [
        1 => '待核准',
        2 => '已核准',
        3 => '挂起',
    ];
    //记录核准时间的状态
    public const APPROVE_STATUS_FINISHED = [
        2,
        3
    ];

    // 订单类型

    public const ORDER_TYPE_COMMON = 1;

    public const ORDER_TYPE_SALES = 2;

    public const ORDER_TYPE = [
        self::ORDER_TYPE_COMMON => '常规备货',
        self::ORDER_TYPE_SALES  => '销售备货',
    ];

    // 组装类型

    public const ASSEMBLE_TYPE_COMMON = 1;

    public const ASSEMBLE_TYPE_CUSTOM = 2;

    public const ASSEMBLE_TYPE_COMPUTE = 3;

    public const ASSEMBLE_TYPE = [
        self::ASSEMBLE_TYPE_COMMON  => '常规组装',
        self::ASSEMBLE_TYPE_CUSTOM  => '客户定制',
        self::ASSEMBLE_TYPE_COMPUTE => '算能定制',
    ];

    //----------------------------------文件------------------------------------------------------------------

    // 附件类型
    const ATTACHMENT_TYPE = 'assemble_order_attachment';   //总标识
    //---------------------------------组装资料-----------------------------------------
    const ATTACHMENT_TYPE_ASSEMBLE = 'assemble_data';        //组装数据
    const ATTACHMENT_TYPE_ASSEMBLE_SOP = 'sop_category';        //组装SOP
    const ATTACHMENT_TYPE_ASSEMBLE_PACKAGE = 'package_photo_category';        //组装SOP
    const ATTACHMENT_TYPE_ASSEMBLE_TAG = 'tag_category';        //组装标签

    const ATTACHMENT_TYPE_ASSEMBLE_ARRAY = [
        self::ATTACHMENT_TYPE_ASSEMBLE,
        self::ATTACHMENT_TYPE_ASSEMBLE_SOP,
        self::ATTACHMENT_TYPE_ASSEMBLE_PACKAGE,
        self::ATTACHMENT_TYPE_ASSEMBLE_TAG,
    ];

    //------------------软件资料【start】-------------------------
    const ATTACHMENT_TYPE_FIRMWARE = 'soft_data';        //软件资料
    const ATTACHMENT_TYPE_FIRMWARE_COMMON = 'common_firmware';//公板固件
    const ATTACHMENT_TYPE_FIRMWARE_CUSTOMER = 'customer_firmware';//客户固件
    const ATTACHMENT_TYPE_FIRMWARE_ARRAY = [
        self::ATTACHMENT_TYPE_FIRMWARE,
        self::ATTACHMENT_TYPE_FIRMWARE_COMMON,
        self::ATTACHMENT_TYPE_FIRMWARE_CUSTOMER,
    ];

    //------------------首件资料-------------------------
    const ATTACHMENT_TYPE_FIRST = 'first_data';               //首件资料
    const ATTACHMENT_TYPE_FIRST_ASSEMBLE = 'first_assemble_category';               //首件组装
    const ATTACHMENT_TYPE_FIRST_SOFT = 'first_software_category';               //首件软件
    const ATTACHMENT_TYPE_FIRST_ARRAY = [
        self::ATTACHMENT_TYPE_FIRST,
        self::ATTACHMENT_TYPE_FIRST_ASSEMBLE,
        self::ATTACHMENT_TYPE_FIRST_SOFT,
    ];

    //------------------成品资料-------------------------
    const ATTACHMENT_TYPE_PRODUCT = 'product_data'; //成品资料
    const ATTACHMENT_TYPE_PRODUCT_FINISH = 'product_finished_category'; //生产成品
    const ATTACHMENT_TYPE_PRODUCT_SOFTWARE = 'product_soft_category'; //生产软件
    const ATTACHMENT_TYPE_PRODUCT_ARRAY = [
        self::ATTACHMENT_TYPE_PRODUCT,
        self::ATTACHMENT_TYPE_PRODUCT_FINISH,
        self::ATTACHMENT_TYPE_PRODUCT_SOFTWARE,
    ];


    // 文件审核状态
    public const ATTACHMENT_AUDIT_WAITING = 1;

    public const ATTACHMENT_AUDIT_PASS = 2;

    public const ATTACHMENT_AUDIT_REJECT = 3;

    public const ATTACHMENT_AUDIT_STATUS = [
        self::ATTACHMENT_AUDIT_WAITING => '待审核',
        self::ATTACHMENT_AUDIT_PASS    => '已审核',
        self::ATTACHMENT_AUDIT_REJECT  => '未通过',
    ];

    const ATTACHMENT_STATUS_KEYS = [
        'assemble_data_status'         => self::ATTACHMENT_TYPE_ASSEMBLE,
        'soft_data_status'             => self::ATTACHMENT_TYPE_FIRMWARE,
        'first_assemble_data_status'   => self::ATTACHMENT_TYPE_FIRST_ASSEMBLE,
        'first_soft_data_status'       => self::ATTACHMENT_TYPE_FIRST_SOFT,
        'product_finished_data_status' => self::ATTACHMENT_TYPE_PRODUCT_FINISH,
        'product_soft_data_status'     => self::ATTACHMENT_TYPE_PRODUCT_SOFTWARE,
    ];


    // 齐料状态
    public const MATERIAL_STATUS_WAITING = 1;

    public const MATERIAL_STATUS_FINISHED = 2;

    public const MATERIAL_STATUS_NOT = 3;

    public const MATERIAL_STATUS = [
        1 => '等待',
        2 => '已齐',
        3 => '未齐',
    ];

    //订单汇总齐料状态
    const ORDER_MATERIAL_STATUS_NOT = 1;//未齐
    const ORDER_MATERIAL_STATUS_COMPLETE = 2;//已齐
    const ORDER_MATERIAL_STATUS_PART = 3;//部分齐

    // 齐料类型
    const MATERIAL_TYPE_TAG = 5;
    public const MATERIAL_TYPE = [
        1 => '主板',
        2 => '外壳',
        3 => '配件',
        4 => '包材',
        5 => '标签',
    ];

    //订单录入类型
    const CREATE_TYPE_HAND = 1;//手动新增
    const CREATE_TYPE_AUTO = 2;//自动生成、

    //出货地点
    const SHIPMENT_PLACE_SELF = 1;  //天启
    const SHIPMENT_PLACE = [
        1 => '天启',
        2 => '朗格',
        3 => '勤奋',
        4 => '创隆'
    ];


    // 生产总结状态
    public const SUMMARY_STATUS = [
        1 => '新建',
        2 => '已解决',
        3 => '挂起',
    ];

    //总结的组装测试状态
    public const ASSEMBLE_TEST_STATUS = [
        1 => '未完成',
        2 => '已完成'
    ];

    //角色
    const PRODUCTION_ROLE = 'Assemble';//组装角色
    const WARE_ROLE = 'Warehouse';//仓库角色

    //默认订单负责人
    const DEFAULT_ORDER_USER = [
        'order_user_id'=>'文晓东',
        'assemble_user_id'=>'卓光兴',
//        'software_user_id'=>'陈祁香',
        'software_user_id'=>'陈坤艳',
    ];
    const DEFAULT_ASSEMBLE_ADDRESS_USER = [
        '创隆'=>'师彩萍',
        '朗格'=>'师彩萍',
        '勤奋'=>'杨梓恩',
//        '天启'=>'陈祁香',
        '天启'=>'陈坤艳',
    ];
    const FACTORY_USER = [
        '师彩萍','杨梓恩'
    ];

    //齐料状态
    const COMPLETENESS_STATUS = [
        1 =>  '等待中',
        2 => '备料中',
        3 => '已备未发',
        4 => '已转组装',
    ];

    const DATA_STATUS_WAIT = 1;
    const DATA_STATUS_UPLOADED = 2;
    const DATA_STATUS_FINISHED = 3;
    const DATA_STATUS = [
        1=>'待上传',
        2=>'上传完成',
        3=>'审核完成'
    ];
    //资料完成情况
    const DATA_COMPLETE_STATUS = [
        1=>'待完成',
        2=>'已完成'
    ];

    //是否完善
    const IS_COMPLETE = [
        0=>'未完善',
        1=>'已完善'
    ];

    const TAG_AUDIT_STATUS_PASS = 2;
    //标签审核状态
    const TAG_AUDIT_STATUS = [
        1=>'待审核',
        2=>'已审核',
        3=>'未通过'
    ];
    //日志变更类型
    const LOG_CHANGE_CATEGORY_ORDER = 'assemble_order';
    const LOG_CHANGE_CATEGORY_INFO = 'assemble_info';
    const LOG_CHANGE_CATEGORY_COMPLETENESS = 'assemble_completeness';
    const LOG_CHANGE_CATEGORY_SUMMARY = 'assemble_summary';
    const LOG_CHANGE_CATEGORY_ASSEMBLE = 'assemble_assemble';
    const LOG_CHANGE_CATEGORY_SOFTWARE = 'assemble_software';
    const LOG_CHANGE_CATEGORY_FIRST = 'assemble_first';
    const LOG_CHANGE_CATEGORY_PRODUCT = 'assemble_product';
    const LOG_CHANGE_CATEGORY_OTHER = 'assemble_other';

    const LOG_CHANGE_CATEGORY_ARR = [
        self::LOG_CHANGE_CATEGORY_ORDER        => '订单信息',
        self::LOG_CHANGE_CATEGORY_INFO         => '基本信息',
        self::LOG_CHANGE_CATEGORY_COMPLETENESS => '备料信息',
        self::LOG_CHANGE_CATEGORY_SUMMARY      => '组装总结',
        self::LOG_CHANGE_CATEGORY_ASSEMBLE     => '组装资料',
        self::LOG_CHANGE_CATEGORY_SOFTWARE     => '软件资料',
        self::LOG_CHANGE_CATEGORY_FIRST        => '首件资料',
        self::LOG_CHANGE_CATEGORY_PRODUCT      => '生产资料',
        self::LOG_CHANGE_CATEGORY_OTHER        => '其他'
    ];

}
