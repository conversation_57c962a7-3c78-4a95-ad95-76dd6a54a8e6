<?php
declare(strict_types=1);

namespace App\Constants;

/**
 * 事项类型配置常量类
 * 统一管理所有事项类型的配置信息
 */
class TrackerTypeConfig
{
    /**
     * 事项类型配置映射
     * 包含颜色、识别符、匹配关键词等信息
     */
    const CONFIGS = [
        'requirement' => [
            'color' => '#409EFF',
            'identifier' => 'REQ',
            'keywords' => ['需求', 'feature', 'requirement'],
        ],
        'bug' => [
            'color' => '#F56C6C',
            'identifier' => 'BUG',
            'keywords' => ['缺陷', 'bug'],
        ],
        'task' => [
            'color' => '#67C23A',
            'identifier' => 'TASK',
            'keywords' => ['任务', 'task'],
        ],
    ];

    /**
     * 获取事项类型的默认颜色
     * @param string $trackerType
     * @return string
     */
    public static function getDefaultColor(string $trackerType): string
    {
        return self::CONFIGS[$trackerType]['color'] ?? '#409EFF';
    }

    /**
     * 获取事项类型的默认识别符
     * @param string $trackerType
     * @param string $name 事项类型名称（用于兜底生成）
     * @return string
     */
    public static function getDefaultIdentifier(string $trackerType, string $name = ''): string
    {
        $identifier = self::CONFIGS[$trackerType]['identifier'] ?? null;
        
        if ($identifier) {
            return $identifier;
        }
        
        // 兜底：使用名称的前3个字符
        return $name ? strtoupper(substr($name, 0, 3)) : 'XXX';
    }

    /**
     * 根据名称推断事项类型
     * @param string $name
     * @return string
     */
    public static function inferTrackerTypeByName(string $name): string
    {
        $name = strtolower($name);
        
        foreach (self::CONFIGS as $type => $config) {
            foreach ($config['keywords'] as $keyword) {
                if (strpos($name, strtolower($keyword)) !== false) {
                    return $type;
                }
            }
        }
        
        return 'other';
    }

    /**
     * 生成用于数据库查询的 CASE 语句
     * @return string
     */
    public static function generateCaseStatement(): string
    {
        $cases = [];
        
        foreach (self::CONFIGS as $type => $config) {
            $conditions = [];
            foreach ($config['keywords'] as $keyword) {
                $conditions[] = "t.name LIKE '%{$keyword}%'";
            }
            $conditionStr = implode(' OR ', $conditions);
            $cases[] = "WHEN ({$conditionStr}) THEN '{$type}'";
        }
        
        $caseStatement = "CASE\n    " . implode("\n    ", $cases) . "\n    ELSE 'other'\nEND";
        
        return $caseStatement;
    }

    /**
     * 获取所有支持的事项类型
     * @return array
     */
    public static function getAllTypes(): array
    {
        return array_keys(self::CONFIGS);
    }

    /**
     * 检查是否为有效的事项类型
     * @param string $trackerType
     * @return bool
     */
    public static function isValidType(string $trackerType): bool
    {
        return array_key_exists($trackerType, self::CONFIGS);
    }

    /**
     * 获取指定类型的完整配置
     * @param string $trackerType
     * @return array|null
     */
    public static function getConfig(string $trackerType): ?array
    {
        return self::CONFIGS[$trackerType] ?? null;
    }

    /**
     * 获取所有配置
     * @return array
     */
    public static function getAllConfigs(): array
    {
        return self::CONFIGS;
    }
}
