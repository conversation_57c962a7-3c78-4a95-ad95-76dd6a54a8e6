<?php
declare(strict_types=1);

namespace App\Constants;

class ProductionOrderCode
{
    //---------------info[start]----------------
    //核准状态
    const APPROVE_STATUS_WAIT = 1;
    const APPROVE_STATUS = [
        1 => '待核准',
        2 => '已核准',
        3 => '挂起'
    ];


    //文件审核状态
    const ATTACHMENT_AUDIT_STATUS = [
        0 => '待审核',
        1 => '已审核',
        2 => '未通过'
    ];

    //总结的贴片测试状态
    const TEST_STATUS = [
        0 => '未完成',
        1 => '已完成'
    ];

    //生产总结状态
    const SUMMARY_STATUS = [
        0 => '新建',
        1 => '已解决',
        2 => '挂起',
    ];
    //默认人员
    const FACTORY_DEFAULT_INFO = [
        '中山市勤奋光电科技有限公司' => [
            'production_user_id' => 127,//刘华成
            'test_user_id' => 128,//杨梓恩
            'short_name' => '勤奋光电'
        ],
        '中山市朗格智能科技有限公司' => [
            'production_user_id' => 127,
            'test_user_id' => 129,//师彩萍
            'short_name' => '朗格智能'
        ],
        '东莞市合权电子有限公司' => [
            'production_user_id' => 127,
//            'test_user_id' => 135,//陈祁香
            // 'test_user_id' => 293,//陈坤艳
            'test_user_id' => 250,//李彩云
            'short_name' => '合权电子'
        ],
    ];
    //---------------info[end]----------------

    //---------------attachment[start]----------------
    const ATTACH_TYPE_TOP = 'production_order_attachment';//文件分类首级标识
    //-----------------二级--------------
    const ATTACH_TYPE_BASE = 'base_file';//基础文件分类标识
    const ATTACH_TYPE_FIRST = 'first_production_file';//首件文件分类标识
    //-----------------------------------
    //-----------------三级-------------
    const ATTACH_TYPE_HARD = 'hardware';//硬件资料分类标识
    const ATTACH_TYPE_SOFT = 'software';//软件资料分类标识
    const ATTACH_TYPE_STRUCTURE = 'structure';//结构资料分类标识
    const ATTACH_TYPE_BASE_CHILD = [
        self::ATTACH_TYPE_HARD,
        self::ATTACH_TYPE_SOFT,
        self::ATTACH_TYPE_STRUCTURE,
    ];
    //---------------------------------
    const ATTACH_TYPE_HIGH_TEMP = 'high_temp_requirements';//高温老化要求的分类标识

    const ATTACH_TYPE_NOT_REQUIRE_ARR = ['SOP'];

    const ATTACH_TYPE_LAYOUT_ARR = [
        'zuobiaotu','yuanlitu','tiepiantu','pcbtu','yinwangziliao'
    ];

    const ATTACH_AUDIT_STATUS_NOT = 0;      //待审核
    const ATTACH_AUDIT_STATUS_PASS = 1;     //已通过
    const ATTACH_AUDIT_STATUS_REJECT = 2;   //未通过

    //不是必须要上传的文件类别
    const NOT_IS_MUST_CATEGORY = [
        'structure',
    ];
    //---------------attachment[end]----------------

    //---------------role[start]----------------
    const PRODUCTION_ROLE = 'Production';//生产角色
    const FACTORY_ROLE = 'Factory';//工厂角色
    const PRODUCTION_FILE_UP_ROLE = 'ProductionFileUploader';//生产文件上传角色
    //---------------attachment[end]----------------

    //----------------order-----------------------
    //订单录入类型
    const CREATE_TYPE_HAND = 1;//手动新增
    const CREATE_TYPE_AUTO = 2;//自动生成
    const CREATE_TYPE_ARR = [
        1 => '手动新增',
        2 => '自动生成'
    ];
    //---------------order【end】----------------


    //日志变更类型
    const LOG_CHANGE_CATEGORY_ORDER = 'production_order';
    const LOG_CHANGE_CATEGORY_INFO = 'production_info';
    const LOG_CHANGE_CATEGORY_SUMMARY = 'production_summary';
    const LOG_CHANGE_CATEGORY_BASE_FILE = 'production_base_file';
    const LOG_CHANGE_CATEGORY_FIRST_FILE = 'production_first_file';
    const LOG_CHANGE_CATEGORY_OUTHELP = 'production_outhelp ';
    const LOG_CHANGE_CATEGORY_OTHER = 'production_other';

    const LOG_CHANGE_CATEGORY_ARR = [
        self::LOG_CHANGE_CATEGORY_ORDER      => '订单信息',
        self::LOG_CHANGE_CATEGORY_INFO       => '基本信息',
        self::LOG_CHANGE_CATEGORY_SUMMARY    => '生产总结',
        self::LOG_CHANGE_CATEGORY_BASE_FILE  => '基础资料',
        self::LOG_CHANGE_CATEGORY_FIRST_FILE => '首件资料',
        self::LOG_CHANGE_CATEGORY_OUTHELP    => '生产备注',
        self::LOG_CHANGE_CATEGORY_OTHER      => '其他'
    ];

}