<?php


namespace App\Constants;

use Hyperf\Constants\AbstractConstants;
use Hyperf\Constants\Annotation\Constants;

class KdhCode extends AbstractConstants
{

    /* 揽收 */
    const QUERY_STATE_COLLEECT = 1;

    /* 疑难	快件存在疑难 */
    const QUERY_STATE_DIFFICUT = 2;

    /* 签到 */
    const QUERY_STATE_SIGN = 3;

    /* 退签 */
    const QUERY_STATE_REVOKE = 4;

    /* 圆通速递 */
    CONST EXPRESS_YUANTONG = 'yuantong';

    /* 顺丰 */
    CONST EXPRESS_SHUNFENG = 'shunfeng';


}


// 1	揽收	1	揽收	快件揽件
// 101	已下单	已经下快件单
// 102	待揽收	待快递公司揽收
// 103	已揽收	快递公司已经揽收
// 0	在途	0	在途	快件在途中
// 1001	到达派件城市	快件到达收件人城市
// 1002	干线	快件处于运输过程中
// 1003	转递	快件发往到新的收件地址
// 5	派件	5	派件	快件正在派件
// 501	投柜或驿站	快件已经投递到快递柜或者快递驿站
// 3	签收	3	签收	快件已签收
// 301	本人签收	收件人正常签收
// 302	派件异常后签收	快件显示派件异常，但后续正常签收
// 303	代签	快件已被代签
// 304	投柜或站签收	快件已从快递柜或者驿站取出签收
// 6	退回	6	退回	快件正处于返回发货人的途中
// 4	退签	4	退签	此快件单已退签
// 401	已销单	此快件单已撤销
// 14	拒签	收件人拒签快件
// 7	转投	7	转投	快件转给其他快递公司邮寄
// 2	疑难	2	疑难	快件存在疑难
// 201	超时未签收	快件长时间派件后未签收
// 202	超时未更新	快件长时间没有派件或签收
// 203	拒收	收件人发起拒收快递,待发货方确认
// 204	派件异常	快件派件时遇到异常情况
// 205	柜或驿站超时未取	快件在快递柜或者驿站长时间未取
// 206	无法联系	无法联系到收件人
// 207	超区	超出快递公司的服务区范围
// 208	滞留	快件滞留在网点，没有派送
// 209	破损	快件破损
// 210	销单	寄件人申请撤销寄件
// 8	清关	8	清关	快件清关
// 10	待清关	快件等待清关
// 11	清关中	快件正在清关流程中
// 12	已清关	快件已完成清关流程
// 13	清关异常	货物在清关过程中出现异常
// 14	拒签	\	\	收件人拒签快件