<?php
/**
 * @Copyright T-chip Team.
 * @Date 2023/6/27 上午9:34
 * <AUTHOR>
 * @Description
 */


namespace App\Casts;

use Hyperf\Contract\CastsAttributes;
use League\HTMLToMarkdown\HtmlConverter;

class JournalsNotesCasts implements CastsAttributes
{
    /**
     * 将取出的数据进行转换
     */
    public function get($model, $key, $value, $attributes)
    {
        return $value;
    }

    /**
     * 转换成将要进行存储的值
     */
    public function set($model, $key, $value, $attributes)
    {
        $converter = make(HtmlConverter::class);
        return $converter->convert($value);
    }
}
