<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2023/4/17
 * <AUTHOR> @Description
 *
 */

namespace App\Annotation;

use Hyperf\Di\Annotation\AbstractAnnotation;
use Hyperf\Di\Annotation\AnnotationCollector;

/**
 * 定时器日志
 * Class SubscribeAnnotation
 * @package App\Annotation
 * @Annotation
 * @Target({"METHOD"})
 */
class CrotabLogsAnnotation extends AbstractAnnotation
{
    /**
     * @var string
     */
    public $type;



    public function collectMethod(string $className, ?string $target): void
    {
        AnnotationCollector::collectMethod($className, $target, static::class, $this);
    }
}