<?php

declare(strict_types=1);

namespace App\Command;

use App\Constants\StatusCode;
use App\Exception\AppException;
use App\Model\Marketing\MarketingContent;
use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Command\Annotation\Command;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use Psr\Container\ContainerInterface;
use Symfony\Component\Console\Input\InputOption;

/**
 * 导入StationPC运营数据
 * @Command
 */
class productMigrateProject extends HyperfCommand
{
    /**
     * @var ContainerInterface
     */
    protected $container;

    /**
     * @Inject()
     * @var MarketingContent
     */
    protected $marketingContentModel;

    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;

        parent::__construct('productMigrateProject:command');
    }

    public function configure()
    {
        parent::configure();
        $this->setDescription('迁移产品数据到项目');
        // $this->addUsage('--path 文件地址');
        // $this->addArgument('path', InputArgument::REQUIRED, '文件地址');
        // $this->addOption('path', 'path', InputOption::VALUE_REQUIRED);
    }

    public function handle()
    {
        make(\App\Task\Redmine\ProductRedmineTask::class)->productMigrateProject();
    }
}
