<?php

declare(strict_types=1);

namespace App\Command\Points;

use App\Core\Services\Points\PointService;
use App\Model\Points\UserPointsModel;
use Carbon\Carbon;
use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Command\Annotation\Command;
use Hyperf\Crontab\Annotation\Crontab;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Logger\LoggerFactory;
use Psr\Container\ContainerInterface;
use Symfony\Component\Console\Input\InputOption;

/**
 * 周期性积分重置定时任务
 * 每日凌晨0点执行，重置所有用户的日、周、月、年积分
 * @Command()
 */
class ResetPeriodPointsCommand extends HyperfCommand
{
//  * @Crontab(rule="* * * * *", name="resetPeriodPoints", memo="每日凌晨重置周期性积分", callback="handle")

    /**
     * 执行的命令行
     */
    protected $name = 'points:command';

    /**
     * @Inject
     */
    protected ContainerInterface $container;

    /**
     * @Inject
     */
    protected LoggerFactory $loggerFactory;

    /**
     * @Inject
     */
    protected PointService $pointService;

    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;
        parent::__construct('points:reset-period');
    }

    public function configure()
    {
        parent::configure();
        $this->setDescription('重置用户周期性积分（日、周、月、年）');
        $this->addOption('batch-size', 'b', InputOption::VALUE_OPTIONAL, '批处理大小', 100);
        $this->addOption('dry-run', 'd', InputOption::VALUE_NONE, '仅测试不实际执行');
    }

    public function handle()
    {
        $startTime = Carbon::now();
        $batchSize = (int) $this->input->getOption('batch-size');
        $isDryRun = $this->input->getOption('dry-run');
        
        $logger = $this->loggerFactory->get('points');
        
        $this->line('开始执行周期性积分重置任务...', 'info');
        $logger->info('周期性积分重置任务开始', [
            'start_time' => $startTime->toDateTimeString(),
            'batch_size' => $batchSize,
            'is_dry_run' => $isDryRun
        ]);

        try {
            // 统计总用户数
            $totalUsers = UserPointsModel::count();
            $this->line("发现 {$totalUsers} 个用户需要处理", 'info');

            $processedCount = 0;
            $errorCount = 0;
            $resetCount = 0;
            
            // 分批处理用户
            UserPointsModel::chunk($batchSize, function ($userPointsList) use (&$processedCount, &$errorCount, &$resetCount, $logger, $isDryRun) {
                foreach ($userPointsList as $userPoints) {
                    try {
                        $processedCount++;
                        
                        if ($isDryRun) {
                            // 仅检查是否需要重置，不实际执行
                            $needsReset = $this->checkIfNeedsReset($userPoints);
                            if ($needsReset) {
                                $resetCount++;
                                $this->line("用户 {$userPoints->user_id} 需要重置积分", 'comment');
                            }
                        } else {
                            // 实际执行重置
                            $beforeReset = [
                                'daily' => $userPoints->daily_points,
                                'weekly' => $userPoints->weekly_points,
                                'monthly' => $userPoints->monthly_points,
                                'yearly' => $userPoints->year_points
                            ];
                            
                            // 调用重置方法
                            $this->resetUserPeriodPoints($userPoints);
                            
                            // 检查是否有变化
                            $userPoints->refresh();
                            $afterReset = [
                                'daily' => $userPoints->daily_points,
                                'weekly' => $userPoints->weekly_points,
                                'monthly' => $userPoints->monthly_points,
                                'yearly' => $userPoints->year_points
                            ];
                            
                            if ($beforeReset !== $afterReset) {
                                $resetCount++;
                                $logger->debug('用户积分已重置', [
                                    'user_id' => $userPoints->user_id,
                                    'before' => $beforeReset,
                                    'after' => $afterReset
                                ]);
                            }
                        }

                        // 显示进度
                        if ($processedCount % 50 === 0) {
                            $this->line("已处理 {$processedCount} 个用户...", 'comment');
                        }

                    } catch (\Exception $e) {
                        $errorCount++;
                        $logger->error('处理用户积分重置失败', [
                            'user_id' => $userPoints->user_id,
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);
                        $this->line("处理用户 {$userPoints->user_id} 失败: {$e->getMessage()}", 'error');
                    }
                }
            });

            $endTime = Carbon::now();
            $duration = $endTime->diffInSeconds($startTime);

            // 输出执行结果
            $this->line('', 'info');
            $this->line('=== 执行结果 ===', 'info');
            $this->line("处理模式: " . ($isDryRun ? '测试模式' : '正式执行'), 'info');
            $this->line("总处理用户数: {$processedCount}", 'info');
            $this->line("需要/已重置用户数: {$resetCount}", 'info');
            $this->line("错误数量: {$errorCount}", $errorCount > 0 ? 'error' : 'info');
            $this->line("执行耗时: {$duration} 秒", 'info');
            $this->line("完成时间: " . $endTime->toDateTimeString(), 'info');

            // 记录最终日志
            $logger->info('周期性积分重置任务完成', [
                'end_time' => $endTime->toDateTimeString(),
                'duration_seconds' => $duration,
                'total_processed' => $processedCount,
                'reset_count' => $resetCount,
                'error_count' => $errorCount,
                'is_dry_run' => $isDryRun
            ]);

            if ($errorCount === 0) {
                $this->line('任务执行成功！', 'info');
                return 0;
            } else {
                $this->line("任务完成但有 {$errorCount} 个错误，请检查日志", 'error');
                return 1;
            }

        } catch (\Exception $e) {
            $logger->error('周期性积分重置任务执行失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            $this->line("任务执行失败: {$e->getMessage()}", 'error');
            return 1;
        }
    }

    /**
     * 检查用户是否需要重置积分
     */
    private function checkIfNeedsReset(UserPointsModel $userPoints): bool
    {
        $now = Carbon::now();
        
        // 检查日积分重置
        $lastDailyReset = $userPoints->last_daily_reset ? Carbon::parse($userPoints->last_daily_reset) : null;
        if (!$lastDailyReset || $lastDailyReset->toDateString() !== $now->toDateString()) {
            return true;
        }
        
        // 检查周积分重置
        $lastWeeklyReset = $userPoints->last_weekly_reset ? Carbon::parse($userPoints->last_weekly_reset) : null;
        if (!$lastWeeklyReset || $lastWeeklyReset->week !== $now->week || $lastWeeklyReset->year !== $now->year) {
            return true;
        }
        
        // 检查月积分重置
        $lastMonthlyReset = $userPoints->last_monthly_reset ? Carbon::parse($userPoints->last_monthly_reset) : null;
        if (!$lastMonthlyReset || $lastMonthlyReset->month !== $now->month || $lastMonthlyReset->year !== $now->year) {
            return true;
        }
        
        // 检查年积分重置
        $lastYearlyReset = $userPoints->last_yearly_reset ? Carbon::parse($userPoints->last_yearly_reset) : null;
        if (!$lastYearlyReset || $lastYearlyReset->year !== $now->year) {
            return true;
        }
        
        return false;
    }

    /**
     * 重置用户周期积分
     */
    private function resetUserPeriodPoints(UserPointsModel $userPoints): void
    {
        $now = Carbon::now();
        $updates = [];
        
        // 检查并重置日积分
        $lastDailyReset = $userPoints->last_daily_reset ? Carbon::parse($userPoints->last_daily_reset) : null;
        if (!$lastDailyReset || $lastDailyReset->toDateString() !== $now->toDateString()) {
            $updates['daily_points'] = 0;
            $updates['last_daily_reset'] = $now->toDateString();
        }
        
        // 检查并重置周积分
        $lastWeeklyReset = $userPoints->last_weekly_reset ? Carbon::parse($userPoints->last_weekly_reset) : null;
        if (!$lastWeeklyReset || $lastWeeklyReset->week !== $now->week || $lastWeeklyReset->year !== $now->year) {
            $updates['weekly_points'] = 0;
            $updates['last_weekly_reset'] = $now->toDateString();
        }
        
        // 检查并重置月积分
        $lastMonthlyReset = $userPoints->last_monthly_reset ? Carbon::parse($userPoints->last_monthly_reset) : null;
        if (!$lastMonthlyReset || $lastMonthlyReset->month !== $now->month || $lastMonthlyReset->year !== $now->year) {
            $updates['monthly_points'] = 0;
            $updates['last_monthly_reset'] = $now->toDateString();
        }
        
        // 检查并重置年积分
        $lastYearlyReset = $userPoints->last_yearly_reset ? Carbon::parse($userPoints->last_yearly_reset) : null;
        if (!$lastYearlyReset || $lastYearlyReset->year !== $now->year) {
            $updates['year_points'] = 0;
            $updates['last_yearly_reset'] = $now->toDateString();
        }
        
        // 如果有需要更新的字段，则执行更新
        if (!empty($updates)) {
            $userPoints->update($updates);
        }
    }
} 